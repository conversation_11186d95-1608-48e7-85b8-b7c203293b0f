###############################################################################
# 1??  ????? / ???? / ??
###############################################################################
server.port=8080
spring.threads.virtual.enabled=true

###############################################################################
# 2??  ???? ?? Console & File ????? JSON
#     ?? Spring Boot ??? logging.pattern.console / logging.file.*
###############################################################################
# Console
logging.pattern.console={"ts":"%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}",\
"level":"%5p","thread":"%t","logger":"%logger{36}",\
"msg":%replace(%msg){'\n','\\n'},"exc":%replace(%ex){'\n','\\n'}}

# File??????
logging.file.name=logs/app.log
logging.pattern.file={"ts":"%d{yyyy-MM-dd'T'HH:mm:ss.SSSXXX}",\
"level":"%5p","thread":"%t","logger":"%logger{36}",\
"msg":%replace(%msg){'\n','\\n'},"exc":%replace(%ex){'\n','\\n'}}

# ???? 30 ?????
logging.file.total-size-cap=10GB
logging.file.max-history=30

###############################################################################
# 3??  HTTP ???? ?? ?? Tomcat ?? AccessLogValve??? JSON
###############################################################################
server.tomcat.accesslog.enabled=true
server.tomcat.accesslog.directory=logs
server.tomcat.accesslog.prefix=access
server.tomcat.accesslog.suffix=.log
server.tomcat.accesslog.rotate=true
server.tomcat.accesslog.file-date-format=.yyyy-MM-dd

# ?? JSON Pattern?????????????
server.tomcat.accesslog.pattern={"ts":"%t","remote":"%h","method":"%m",\
"uri":"%U","query":"%q","status":%s,"bytes":%b,"time_ms":%D,\
"thread":"%I","user_agent":%{User-Agent}i}

###############################################################################
# ? 2. ????PostgreSQL + HikariCP?
###############################################################################
#spring.datasource.driver-class-name=org.postgresql.Driver
#spring.datasource.url=****************************************
#spring.datasource.username=your_user
#spring.datasource.password=your_pass

# ???????????? PG ? max_connections
spring.datasource.hikari.maximum-pool-size=64
spring.datasource.hikari.minimum-idle=16
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=7200000

###############################################################################
# ? 3. JPA / Hibernate?????????????
###############################################################################
spring.jpa.open-in-view=false
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.batch_size=200
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

###############################################################################
# ? 4. Redis?Lettuce?
###############################################################################
#spring.data.redis.host=127.0.0.1
#spring.data.redis.port=6379
spring.data.redis.timeout=5s
spring.data.redis.lettuce.pool.max-active=128
spring.data.redis.lettuce.pool.max-idle=32
spring.data.redis.lettuce.pool.min-idle=8

###############################################################################
# 5??  ????
###############################################################################
logging.level.root=INFO
logging.level.org.hibernate.SQL=ERROR

###############################################################################
# ? 5. ???? & ??????????????? pool.size?
###############################################################################
spring.task.execution.thread-name-prefix=async-vt-
spring.task.scheduling.thread-name-prefix=sched-vt-

###############################################################################
# ?? 6. ????
###############################################################################
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=Asia/Shanghai
