package ai.yourouter.jpa.system.model.price.repository;


import ai.yourouter.jpa.system.model.price.bean.SystemLLMPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SystemLLMPriceRepository extends JpaRepository<SystemLLMPrice,Long> {

    public SystemLLMPrice findSystemLLMPriceBySystemModelIdAndStatusesAndType(Long systemModelId, Integer statuses,
                                                                              Integer type);

    public SystemLLMPrice findSystemLLMPriceBySystemModelIdAndStatuses(Long systemModelId, Integer statuses);

    public SystemLLMPrice findBySystemModelId(Long systemModelId);
}
