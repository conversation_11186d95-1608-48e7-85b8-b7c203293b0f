package ai.yourouter.jpa.system.model.price.bean;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_system_llm_price")
public class SystemLLMPrice {

    @Id
    private Long id;

    private Long systemModelId;

    @Column(precision = 29, scale = 18)
    private BigDecimal textPrompt;

    @Column(precision = 29, scale = 18)
    private BigDecimal textCachePrompt;

    @Column(precision = 29, scale = 18)
    private BigDecimal textCompletion;

    @Column(precision = 29, scale = 18)
    private BigDecimal audioPrompt;

    @Column(precision = 29, scale = 18)
    private BigDecimal audioCachePrompt;

    @Column(precision = 29, scale = 18)
    private BigDecimal audioCompletion;

    @Column(precision = 29, scale = 18)
    private BigDecimal reasoningCompletion;

    @Column(precision = 29, scale = 18)
    private BigDecimal imagePrompt;

    @Column(precision = 29, scale = 18)
    private BigDecimal imageCachePrompt;

    @Column(precision = 29, scale = 18)
    private BigDecimal imageCompletion;

    @Column(precision = 29, scale = 18)
    private BigDecimal textCachePromptWrite5M;

    @Column(precision = 29, scale = 18)
    private BigDecimal textCachePromptWrite1H;

    @Column(precision = 29, scale = 18)
    private BigDecimal searchTool;

    private Integer type;

    private Long createTime;

    //0 未验证 1已验证
    private Integer statuses;
}
