package ai.yourouter.jpa.system.startup.repository;

import ai.yourouter.jpa.system.startup.bean.StartupApplication;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StartupApplicationRepository extends JpaRepository<StartupApplication, Long> {

    List<StartupApplication> findByOrganizationIdAndStatus(Long orgId, StartupApplication.Status status);

    long countByOrganizationIdAndStatus(Long orgId, StartupApplication.Status status);


    @Query("select a from StartupApplication a where a.id = :id")
    Optional<StartupApplication> findByIdForUpdate(@Param("id") Long id);

    Optional<StartupApplication> findTopByOrganizationIdAndEmailOrderByCreatedAtDesc(Long orgId, String email);
}
