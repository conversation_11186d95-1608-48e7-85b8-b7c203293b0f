package ai.yourouter.jpa.system.model.price.bean;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_system_search_price")
public class SystemSearchPrice {

    @Id
    private Long id;

    private Long systemModelId;

    @Column(precision = 29, scale = 18)
    private BigDecimal call;

    private Long createTime;

    //0 未验证 1已验证
    private Integer statuses;
}
