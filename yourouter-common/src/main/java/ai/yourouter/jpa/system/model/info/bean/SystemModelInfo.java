package ai.yourouter.jpa.system.model.info.bean;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import java.util.List;

import static java.util.Collections.emptyList;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Cacheable(value = true)
@Table(name = "sp_platform_system_model_info")
public class SystemModelInfo {

    @Id
    private Long id;

    @Column(columnDefinition = "text")
    private String logoUrl;

    /**
     * 实际调用的模型名称
     */
    private String modelName;

    private String manufacturerName;

    @ColumnDefault("'{}'")
    @Column(columnDefinition = "text[]")
    private List<String> vendorNames = emptyList();

    //0 llm
    //1 search
    private Integer modelType;

    private Long createTime;

    //0可用 1不可用
    private Integer statuses;
}
