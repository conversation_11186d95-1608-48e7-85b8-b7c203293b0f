package ai.yourouter.jpa.system.model.price.repository;

import ai.yourouter.jpa.system.model.price.bean.SystemSearchPrice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SystemSearchPriceRepository extends JpaRepository<SystemSearchPrice,Long> {

    public SystemSearchPrice findSystemSearchPriceBySystemModelIdAndStatuses(Long systemModelId, Integer statuses);

    public SystemSearchPrice findBySystemModelId(Long systemModelId);
}
