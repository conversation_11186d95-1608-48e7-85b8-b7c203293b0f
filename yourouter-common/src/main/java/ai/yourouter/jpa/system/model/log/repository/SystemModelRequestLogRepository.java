package ai.yourouter.jpa.system.model.log.repository;

import ai.yourouter.jpa.system.model.log.bean.SystemModelRequestLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import ai.yourouter.response.system.model.log.MinuteAvgTpsDto;

import java.util.List;

@Repository
public interface SystemModelRequestLogRepository extends JpaRepository<SystemModelRequestLog, Long> {


    @Query(value = """
        SELECT
          FLOOR(create_time/60000)*60000          AS minuteStart,
          system_vendor                           AS systemVendor,
          system_model_name                       AS systemModelName,
          AVG(tps)                                AS avgTps
        FROM sp_platform_system_model_request_log
        WHERE create_time >= :startMs AND create_time < :endMs
        GROUP BY minuteStart, system_vendor, system_model_name
        """, nativeQuery = true)
    List<MinuteAvgTpsDto> findAvgTpsPerMinute(@Param("startMs") long startMs,
                                              @Param("endMs")   long endMs);
}
