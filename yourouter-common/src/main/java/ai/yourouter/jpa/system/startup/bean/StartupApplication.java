package ai.yourouter.jpa.system.startup.bean;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Locale;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(
        name = "sp_platform_system_startup_application"
        //,
//        uniqueConstraints = @UniqueConstraint(
//                name = "uk_startup_application_unique_submit",
//                columnNames = {"organization_id", "email", "status"}
//        ),
//        indexes = {
//                @Index(name = "idx_startup_app_org_status", columnList = "organization_id,status"),
//                @Index(name = "idx_startup_app_email",      columnList = "email")
//        }
)
public class StartupApplication {

    public enum Status { SUBMITTED, APPROVED, REJECTED, FUNDED }

    @Id
    private Long id;

    @Column(name = "organization_id", nullable = false)
    private Long organizationId;

    @Column(nullable = false, length = 255)
    private String email;

    // PostgreSQL 推荐用 text，而不是 @Lob(oid)
    @Column(name = "project_idea", nullable = false, columnDefinition = "text")
    private String projectIdea;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 32)
    @Builder.Default
    private Status status = Status.SUBMITTED;

    private Integer score;                 // 可选：内部评分
    private String decisionReason;         // 审核备注
    private String reviewerId;             // 审核人

    @Column(name = "grant_amount", precision = 20, scale = 6, nullable = false)
    private BigDecimal grantAmount;

    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    @Column(name = "reviewed_at")
    private Instant reviewedAt;

    @Column(name = "funded_at")
    private Instant fundedAt;

    @Column(name = "grant_issued", nullable = false)
    private Boolean grantIssued;           // 缺省在 @PrePersist 里赋 false


    /* ---------- 生命周期钩子：默认值 & 规范化 ---------- */

    @PrePersist
    protected void onCreate() {
        Instant now = Instant.now();
        if (createdAt == null) createdAt = now;
        if (updatedAt == null) updatedAt = now;

        // 缺省金额
        if (grantAmount == null) grantAmount = new BigDecimal("1000.00");
        // 缺省布尔
        if (grantIssued == null) grantIssued = Boolean.FALSE;
        // 缺省状态
        if (status == null) status = Status.SUBMITTED;

        // 规范化邮箱
        if (email != null) email = email.trim().toLowerCase(Locale.ROOT);
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
        if (email != null) email = email.trim().toLowerCase(Locale.ROOT);
    }
}