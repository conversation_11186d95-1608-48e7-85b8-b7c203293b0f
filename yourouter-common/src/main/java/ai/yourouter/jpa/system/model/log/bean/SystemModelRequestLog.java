package ai.yourouter.jpa.system.model.log.bean;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;


@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_system_model_request_log")
public class SystemModelRequestLog {

    @Id
    private Long id;

    private String systemModelName;

    private String systemVendor;

    private Double tps;

    public Long createTime;


}
