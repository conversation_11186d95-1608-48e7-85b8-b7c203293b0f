package ai.yourouter.jpa.system.model.info.repository;

import ai.yourouter.jpa.system.model.info.bean.SystemModelInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SystemModelInfoRepository extends JpaRepository<SystemModelInfo,Long> {

    public SystemModelInfo findSystemModelInfoByModelName(String modelName);

    List<SystemModelInfo> findSystemModelInfosByStatuses(Integer statuses);


    @Query(value = """
        SELECT
          smi.id,
          smi.model_name,
          smi.manufacturer_name,
          smi.vendor_names,
          smi.model_type,
          slp.text_prompt,
          slp.text_cache_prompt,
          slp.text_completion,
          slp.audio_prompt,
          slp.audio_cache_prompt,
          slp.audio_completion,
          slp.reasoning_completion,
          slp.image_prompt,
          slp.image_cache_prompt,
          slp.image_completion,
          slp.text_cache_prompt_write5m,
          slp.text_cache_prompt_write1h,
          ssp.call AS search_call,
          slp.search_tool
        FROM sp_platform_system_model_info smi
        LEFT JOIN sp_platform_system_llm_price slp
          ON smi.id = slp.system_model_id
        LEFT JOIN sp_platform_system_search_price ssp
          ON smi.id = ssp.system_model_id
        WHERE smi.statuses = 1
    """, nativeQuery = true)
    List<Object[]> fetchModelPricingInfoRaw();
}
