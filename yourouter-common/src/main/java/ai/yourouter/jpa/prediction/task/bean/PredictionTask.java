package ai.yourouter.jpa.prediction.task.bean;

import jakarta.persistence.*;
import lombok.*;

/**
 * 预测任务实体
 * 用于存储长时间运行的预测任务信息
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_google_prediction_task")
public class PredictionTask {

    @Id
    private Long id;

    /**
     * 任务ID，用于外部查询
     */
    @Column(unique = true, nullable = false)
    private String name;

    /**
     * 组织ID
     */
    private Long organizationId;

    /**
     * 模型名称
     */
    private String modelName;

    /**
     * 请求内容
     */
    @Column(columnDefinition = "text")
    private String requestBody;

    /**
     * 响应内容
     */
    @Column(columnDefinition = "text")
    private String responseBody;

    /**
     * 任务状态
     * 0: 进行中 (Google处理中)
     * 1: 已完成 (CF Worker已更新)
     * 2: 失败
     */
    private String status = Status.RUNNING;

    /**
     * 错误信息
     */
    @Column(columnDefinition = "text")
    private String errorMessage;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 完成时间 (CF Worker更新时间)
     */
    private Long completeTime;

    /**
     * 密钥ID
     */
    private Long keyId;


    public static class Status {
        public static final String RUNNING = "RUNNING";
        public static final String COMPLETED = "COMPLETED";
    }
}
