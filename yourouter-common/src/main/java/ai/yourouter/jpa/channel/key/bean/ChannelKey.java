package ai.yourouter.jpa.channel.key.bean;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_channel_key")
public class ChannelKey {

    @Id
    private Long id;

    private Long userId;

    private Long vendorId;

    private String channelKey;

    private String secretKey;

    private String keyJson;

    private String region;

    private Long createTime;

    private Integer statuses;

}
