package ai.yourouter.jpa.channel.model.bean;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_channel_model")
public class ChannelModel {

    @Id
    private Long id;

    private Long channelKeyId;

    private String asModelName;

    private Integer tpm;

    private Integer rpm;

}
