package ai.yourouter.jpa.channel.dailybill.bean;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_channel_dailybill")
public class ChannelDailyBill {

    @Id
    private Long id;

    private Long userId;

    private Long channelKeyId;

    private Long channelModelId;

    private Long textPrompt;

    private Long textCachePrompt;

    private Long textCompletion;

    private Long textCachePromptWrite5M;

    private Long textCachePromptWrite1H;

    private Long audioPrompt;

    private Long audioCachePrompt;

    private Long audioCompletion;

    private Long reasoningCompletion;

    private Long imagePrompt;

    private Long imageCachePrompt;

    private Long imageCompletion;

    private Long request;

    private Long billDay;     // 当天零点毫秒

    private Long createTime;  // 最后更新时间
}
