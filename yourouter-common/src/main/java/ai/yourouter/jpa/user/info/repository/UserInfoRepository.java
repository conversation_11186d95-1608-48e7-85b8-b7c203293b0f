package ai.yourouter.jpa.user.info.repository;

import ai.yourouter.jpa.user.info.bean.UserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface UserInfoRepository extends JpaRepository<UserInfo,Long> {

    public UserInfo findUserInfoById(Long id);

    public Optional<UserInfo> findByAuth0Sub(String sub);

    public Optional<UserInfo> findUserInfoByEmail(String userEmail);
}
