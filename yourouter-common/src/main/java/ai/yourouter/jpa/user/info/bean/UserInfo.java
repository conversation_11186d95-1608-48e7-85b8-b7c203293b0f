package ai.yourouter.jpa.user.info.bean;

import jakarta.persistence.*;
import lombok.*;


@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_user_info", uniqueConstraints=@UniqueConstraint(columnNames="auth0_sub"))
public class UserInfo {

    @Id
    private Long id;

    @Column(name = "auth0_sub", nullable = false, length = 64)
    private String auth0Sub;

    private String nickname;

    private String email;

    private String name;

    private String picture;

    private Long createTime;

    private Integer statuses;
}
