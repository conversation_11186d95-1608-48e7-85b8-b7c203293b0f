package ai.yourouter.jpa.stripe.record.repository;

import ai.yourouter.jpa.stripe.record.bean.StripeChargeRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface StripeChargeRecordRepository extends JpaRepository<StripeChargeRecord, Long> {

    Optional<StripeChargeRecord> findStripeChargeRecordByStripeId(String stripeId);


    List<StripeChargeRecord> findByOrganizationIdAndStripeIdIn(Long organizationId, Collection<String> stripeIds);

    Optional<StripeChargeRecord> findByOrganizationIdAndStripeId(Long organizationId, String stripeId);
}
