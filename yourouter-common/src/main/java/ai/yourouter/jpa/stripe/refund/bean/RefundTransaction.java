package ai.yourouter.jpa.stripe.refund.bean;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;

/**
 * 退款流水。
 *
 * <p>一笔退款可来源于：
 *   1. EXTERNAL  →  第三方支付通道（如 Stripe Refund）原路返回；
 *   2. INTERNAL  →  仅在平台账上冲正，不回到用户原卡。
 *
 * <p>平台余额会在退款成功后减去 amount。</p>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_refund_transaction",
        indexes = {
                @Index(name = "idx_refund_org", columnList = "organizationId"),
                @Index(name = "idx_refund_charge", columnList = "stripeChargeId")
        })
public class RefundTransaction {

    /** 自增主键（内部唯一） */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 关联组织 */
    private Long organizationId;

    /** 退款金额（正数；最小货币单位，如分） */
    @Column(nullable = false)
    private Long amount;

    /**
     * 退款来源类型：
     *  - EXTERNAL：第三方（Stripe）原路退回
     *  - INTERNAL：仅平台内部冲正（不经外部渠道）
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 16, nullable = false)
    private RefundType type;

    /** 第三方退款 Id（type=EXTERNAL 时必填，如 re_3MtvYk…） */
    private String stripeRefundId;

    /** 被退款的 Stripe Charge Id（如 ch_…），便于对账；可为空 */
    private String stripeChargeId;

    /** 原 BalanceTransaction 主键，追溯这笔退款对应哪一笔入账 */
    private Long balanceTransactionId;

    /**
     * 退款状态：
     *  - INITIATED：已创建待处理
     *  - PENDING  ：通道处理中
     *  - SUCCEEDED：成功
     *  - FAILED   ：失败
     *  - CANCELED ：主动取消
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 16, nullable = false)
    private RefundStatus status;

    /** 退款原因/备注（简短说明给运营或审计看） */
    private String reason;

    /** 业务自定义参考号（如售后单号） */
    private String referenceNo;

    /** 创建时间 */
    private Instant created;

    /** 处理完成时间（成功/失败/取消 时写入） */
    private Instant processed;

    /** 其他扩展数据（JSON） */
    @JdbcTypeCode(SqlTypes.JSON)
    private String metadata;

    /* ---------- 枚举 ---------- */

    public enum RefundType {
        EXTERNAL,
        INTERNAL
    }

    public enum RefundStatus {
        INITIATED,
        PENDING,
        SUCCEEDED,
        FAILED,
        CANCELED
    }
}