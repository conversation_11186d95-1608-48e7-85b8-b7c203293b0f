package ai.yourouter.jpa.stripe.record.bean;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.math.BigDecimal;
import java.time.Instant;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_platform_stripe_charge_record")
public class StripeChargeRecord {


    @Id
    private Long id;

    private Long organizationId;

    @Column(columnDefinition = "text")
    private String stripeId;


    /** 充值金额（最小货币单位） */
    @Column(precision = 29, scale = 18 ,nullable = false)
    private BigDecimal amount;

    /** 货币三字码，例 usd、cny */
    @Column(length = 3, nullable = false)
    private String currency;

    /** capture/refund 相关字段 */
    @Column(precision = 29, scale = 18)
    private BigDecimal amountCaptured;

    @Column(precision = 29, scale = 18)
    private BigDecimal amountRefunded;

    private Boolean refunded;

    /** 状态 succeeded / pending / failed 等 */
    @Column(columnDefinition = "text")
    private String status;

    /** 关联的 PaymentIntent、Customer、PaymentMethod… */
    @Column(columnDefinition = "text")
    private String paymentIntentId;

    @Column(columnDefinition = "text")
    private String customerId;
    @Column(columnDefinition = "text")
    private String paymentMethodId;

    /** 收据邮箱 / URL */
    @Column(columnDefinition = "text")
    private String receiptEmail;
    @Column(columnDefinition = "text")
    private String receiptUrl;

    private String last4;

    private String brand;

    private Long expMonth;

    private Long expYear;

    /** 创建时间 */
    private Instant created;

    /** 自定义元数据（JSON） */
    @JdbcTypeCode(SqlTypes.JSON)
    private String metadata;
}