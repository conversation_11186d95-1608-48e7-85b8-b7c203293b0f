package ai.yourouter.jpa.organization.secret.bean;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_secret")
public class OrganizationSecret {

    @Id
    private Long id;

    private Long organizationId;

    private String secretKey;

    private Long createTime;

    //0不可用 1可用
    private Integer statuses;

}
