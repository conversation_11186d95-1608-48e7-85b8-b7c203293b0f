package ai.yourouter.jpa.organization.transaction.balance.bean;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_platform_organization_transaction_balance")
public class BalanceTransaction {

    @Id
    private Long id;

    /** 关联组织 */
    private Long organizationId;

    /**
     * 流水类型：
     *   RECHARGE   = 用户主动充值
     *   GIFT       = 系统/运营赠送
     *   ADJUSTMENT = 人工调账
     */
    @Enumerated(EnumType.STRING)
    @Column(length = 16, nullable = false)
    private TransactionType type;

    /**
     * 入账金额（永远为正数，最小货币单位）
     */

    @Column(precision = 29, scale = 18)
    private BigDecimal amount;


    /** capture/refund 相关字段 */

    @Column(precision = 29, scale = 18)
    private BigDecimal amountCaptured;

    @Column(precision = 29, scale = 18)
    private BigDecimal amountRefunded;

    private Boolean refunded;


    /** 关联 Stripe Charge Id（仅 type=RECHARGE 时使用） */
    @Column(columnDefinition = "text")
    private String stripeChargeId;

    /** 创建时间 */
    private Long created;

    /** 业务自定义字段（订单号、操作人等，可选） */
    @Column(columnDefinition = "text")
    private String referenceNo;

    /** 备注 */
    @Column(columnDefinition = "text")
    private String memo;

    /* ---- 枚举 ---- */
    public enum TransactionType {
        RECHARGE,
        GIFT,
        ADJUSTMENT
    }
}