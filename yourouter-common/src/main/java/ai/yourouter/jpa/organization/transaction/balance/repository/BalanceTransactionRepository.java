package ai.yourouter.jpa.organization.transaction.balance.repository;

import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

@Repository
public interface BalanceTransactionRepository extends JpaRepository<BalanceTransaction, Long> {

    List<BalanceTransaction> findBalanceTransactionsByOrganizationId(Long organizationId);

    boolean existsByOrganizationIdAndType(Long organizationId, BalanceTransaction.TransactionType type);

    @Query(value = """
    SELECT COALESCE(SUM(amount), 0)
      FROM sp_platform_transaction_balance
     WHERE organization_id = :orgId
    """, nativeQuery = true)
    Long sumAmountByOrganizationId(@Param("orgId") Long orgId);


    @Query(value = """
    SELECT COALESCE(SUM(amount), 0)
      FROM balance_transaction
     WHERE organization_id = :orgId
       AND type IN ('GIFT', 'ADJUSTMENT')
    """, nativeQuery = true)
    Long sumGiftAndAdjustmentAmount(@Param("orgId") Long organizationId);



    // 扣款时用到的“可用余额”查询（先前已有）
    @Query("""
        SELECT b FROM BalanceTransaction b
        WHERE b.organizationId = :orgId
          AND b.type IN :types
          AND (b.amount > COALESCE(b.amountCaptured, 0))
        ORDER BY b.created ASC
    """)
    List<BalanceTransaction> findAvailableBalances(@Param("orgId") Long organizationId,
                                                   @Param("types") List<BalanceTransaction.TransactionType> types);

    // 账单统计窗口查询：严格使用 (startTime, endTime] 防止跨次重复统计
    @Query("""
        SELECT b FROM BalanceTransaction b
        WHERE b.organizationId = :orgId
          AND b.created > :start
          AND b.created <= :end
    """)
    List<BalanceTransaction> findByOrgIdAndCreatedInWindow(@Param("orgId") Long organizationId,
                                                           @Param("start") Long startExclusive,
                                                           @Param("end") Long endInclusive);

    @Query("""
        SELECT b FROM BalanceTransaction b
        WHERE b.organizationId = :orgId
          AND b.type = ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction.TransactionType.RECHARGE
        ORDER BY b.created DESC
    """)
    List<BalanceTransaction> findRechargeTransactionsDesc(@Param("orgId") Long organizationId);


    /** 按类型汇总 amount */
    @Query("""
        SELECT COALESCE(SUM(b.amount), 0)
        FROM BalanceTransaction b
        WHERE b.organizationId = :orgId
          AND b.type = :type
    """)
    BigDecimal sumAmountByOrgAndType(@Param("orgId") Long organizationId,
                                     @Param("type") BalanceTransaction.TransactionType type);

    /** 汇总所有类型的 amountCaptured */
    @Query("""
        SELECT COALESCE(SUM(b.amountCaptured), 0)
        FROM BalanceTransaction b
        WHERE b.organizationId = :orgId
    """)
    BigDecimal sumCapturedByOrg(@Param("orgId") Long organizationId);

    /** 汇总所有类型的 amountRefunded（通常只有 RECHARGE 有值） */
    @Query("""
        SELECT COALESCE(SUM(b.amountRefunded), 0)
        FROM BalanceTransaction b
        WHERE b.organizationId = :orgId
    """)
    BigDecimal sumRefundedByOrg(@Param("orgId") Long organizationId);

    Boolean existsByStripeChargeId(String sessionId);

    List<BalanceTransaction> findByOrganizationIdAndTypeInOrderByCreatedDesc(Long orgId, List<BalanceTransaction.TransactionType> types);
}
