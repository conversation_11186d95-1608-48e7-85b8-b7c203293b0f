package ai.yourouter.jpa.organization.transaction.balance.bean;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_platform_organization_billing_statement")
public class OrganizationBillingStatement {

    @Id
    private Long id;

    private Long organizationId;

    private Long startTime;

    private Long endTime;

    /** 累计充值金额（RECHARGE 类型） */
    @Column(precision = 29, scale = 18)
    private BigDecimal totalRecharge;

    /** 累计赠送金额（GIFT 类型） */
    @Column(precision = 29, scale = 18)
    private BigDecimal totalGift;

    /** 累计调账金额（ADJUSTMENT 类型） */
    @Column(precision = 29, scale = 18)
    private BigDecimal totalAdjustment;

    /** 累计消费金额（amountCaptured） */
    @Column(precision = 29, scale = 18)
    private BigDecimal totalCaptured;

    /** 累计退款金额（amountRefunded） */
    @Column(precision = 29, scale = 18)
    private BigDecimal totalRefunded;

    /** 净入账金额 = 充值 + 赠送 + 调账 - 消费 - 退款 */
    @Column(precision = 29, scale = 18)
    private BigDecimal netBalanceChange;

    private Instant createdAt;
}
