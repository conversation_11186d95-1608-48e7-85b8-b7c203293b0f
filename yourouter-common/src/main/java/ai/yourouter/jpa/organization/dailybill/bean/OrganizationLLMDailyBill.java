package ai.yourouter.jpa.organization.dailybill.bean;


import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Cacheable(value = true)
@Table(name = "sp_platform_organization_llm_daily_bill")
public class OrganizationLLMDailyBill {

    @Id
    private Long id;

    private Long organizationId;

    private String modelName;

    private Long textPrompt;

    private Long textCachePrompt;

    private Long textCompletion;

    private Long textCachePromptWrite5M;

    private Long textCachePromptWrite1H;

    private Long audioPrompt;

    private Long audioCachePrompt;

    private Long audioCompletion;

    private Long reasoningCompletion;

    private Long imagePrompt;

    private Long imageCachePrompt;

    private Long imageCompletion;

    private Long request;

    private Long searchTool;

    @Column(precision = 29, scale = 18)
    private BigDecimal quota;

    private Long billDay;     // 当天零点毫秒

    private Long createTime;  // 最后更新时间
}
