package ai.yourouter.jpa.organization.dailybill.bean;

import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Cacheable(value = true)
@Table(name = "sp_platform_organization_search_daily_bill")
public class OrganizationSearchDailyBill {

    @Id
    private Long id;

    private Long organizationId;

    private String modelName;

    private Long call;

    private Long request;

    @Column(precision = 29, scale = 18)
    private BigDecimal quota;

    private Long billDay;     // 当天零点毫秒
    private Long createTime;  // 最后更新时间
}
