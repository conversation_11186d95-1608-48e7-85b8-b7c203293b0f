package ai.yourouter.jpa.organization.info.bean;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_info")
public class OrganizationInfo {

    @Id
    private Long id;

    private String organizationName;

    private String customerId;

    private Long createTime;

    private Integer statuses;

}
