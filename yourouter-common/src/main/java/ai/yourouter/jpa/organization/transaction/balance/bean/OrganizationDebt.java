package ai.yourouter.jpa.organization.transaction.balance.bean;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.Instant;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_platform_organization_debt")
public class OrganizationDebt {

    public enum DebtStatus {
        PENDING, PARTIALLY_CLEARED, CLEARED
    }

    @Id
    private Long id;

    private Long organizationId;

    @Column(precision = 29, scale = 18)
    private BigDecimal originalAmount;     // 生成时欠费金额

    @Column(precision = 29, scale = 18)
    private BigDecimal debtAmount;         // 当前未清余额

    @Enumerated(EnumType.STRING)
    private DebtStatus status;

    private Instant createdAt;
    private Instant clearedAt;

}
