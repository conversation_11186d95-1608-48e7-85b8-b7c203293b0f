package ai.yourouter.jpa.organization.log.bean;


import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Entity
@Table(name = "sp_platform_organization_request_record")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrganizationRequestRecord {

    @Id
    private Long id;

    @Column(nullable = false, length = 45)
    private String ip;

    @Column(name = "organizationId", nullable = false, length = 64)
    private Long organizationId;

    @Column(name = "user_agent", length = 512)
    private String userAgent;

    @Column(name = "cf_connecting_ip", length = 45)
    private String cfConnectingIp;

    @Column(name = "cf_ip_country", length = 10)
    private String cfIpCountry;

    @Column(name = "cf_ray", length = 64)
    private String cfRay;

    @Column(name = "request_time", nullable = false)
    private LocalDateTime requestTime;

    @PrePersist
    protected void onCreate() {
        requestTime = LocalDateTime.now();
    }
}
