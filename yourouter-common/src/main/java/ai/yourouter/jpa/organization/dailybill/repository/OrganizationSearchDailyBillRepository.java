package ai.yourouter.jpa.organization.dailybill.repository;

import ai.yourouter.jpa.organization.dailybill.bean.OrganizationSearchDailyBill;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface OrganizationSearchDailyBillRepository extends JpaRepository<OrganizationSearchDailyBill,Long> {

    @Modifying
    @Query(value = """
        INSERT INTO sp_platform_organization_search_daily_bill
            (id, organization_id, model_name, bill_day, create_time,
             request, call, quota)
        VALUES
            (:id, :orgId, :modelName, :billDay, :nowMillis,
             :req, :callCnt, :quota)
        ON CONFLICT (organization_id, model_name, bill_day)
        DO UPDATE
          SET create_time      = EXCLUDED.create_time,
              request          = sp_platform_organization_search_daily_bill.request          + EXCLUDED.request,
              call             = sp_platform_organization_search_daily_bill.call             + EXCLUDED.call,
              quota            = sp_platform_organization_search_daily_bill.quota            + EXCLUDED.quota
        """, nativeQuery = true)
    void upsertDaily(@Param("id") Long id, @Param("orgId") Long orgId,
                     @Param("modelName") String modelName, @Param("billDay") Long billDay,
                     @Param("nowMillis") Long nowMillis,
                     @Param("req") Long req, @Param("callCnt") Long callCnt,
                     @Param("quota") BigDecimal quota);

}
