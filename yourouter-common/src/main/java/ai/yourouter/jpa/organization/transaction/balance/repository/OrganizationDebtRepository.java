package ai.yourouter.jpa.organization.transaction.balance.repository;

import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationDebt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface OrganizationDebtRepository extends JpaRepository<OrganizationDebt, Long> {

    List<OrganizationDebt> findByOrganizationIdAndStatusInOrderByCreatedAtAsc(
            Long organizationId,
            Collection<OrganizationDebt.DebtStatus> statuses
    );
}
