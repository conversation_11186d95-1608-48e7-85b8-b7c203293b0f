package ai.yourouter.jpa.organization.transaction.balance.bean;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_platform_organization_reconciliation_diff")
public class ReconciliationDiff {

    @Id
    private Long id;
    private LocalDate reconcileDate;
    private Long organizationId;
    private Long expected;
    private Long redisLive;
    private Long delta;
    private Instant createTime;

}
