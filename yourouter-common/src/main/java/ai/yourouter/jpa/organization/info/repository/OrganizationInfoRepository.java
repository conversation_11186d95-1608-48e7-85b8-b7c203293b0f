package ai.yourouter.jpa.organization.info.repository;

import ai.yourouter.jpa.organization.info.bean.OrganizationInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;

@Repository
public interface OrganizationInfoRepository extends JpaRepository<OrganizationInfo, Long> {


    /**
     * 使用nativequery根据OrganizationId修改usedAmount
     *
     * @param organizationId
     * @param usedAmount
     *
     * @return int
     **/
    @Modifying
    @Query(value = "UPDATE sp_platform_organization_info SET balance_amount = balance_amount - :usedAmount WHERE  id= :organizationId", nativeQuery = true)
    public int updateOrganizationWalletByOrganizationId(Long organizationId, Long usedAmount);

    @Query(value = "SELECT CASE WHEN (balance_amount - :usedAmount) <= 0 THEN 1 ELSE 0 END FROM sp_platform_organization_info WHERE  id= :organizationId", nativeQuery = true)
    public int checkBalanceAndReturn(Long organizationId, Long usedAmount);

    public OrganizationInfo findOrganizationInfoById(Long id);

    Collection<Object> findByIdIn(Collection<Long> ids);

    OrganizationInfo findOrganizationInfoByIdAndStatuses(Long id, Integer statuses);
}
