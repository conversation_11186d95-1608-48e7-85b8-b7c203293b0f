package ai.yourouter.jpa.organization.cycle.bean;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;


@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_search_cycle")
public class OrganizationSearchCycle {

    @Id
    private Long id;

    private Long organizationId;

    private Long systemModelId;

    @ColumnDefault("0")
    private Long call;

    @ColumnDefault("0")
    private Long request;

    @Column(precision = 29, scale = 18)
    private BigDecimal quota;
    

    private Long bucketStart;

    @ColumnDefault("0")
    private Integer statuses;


}
