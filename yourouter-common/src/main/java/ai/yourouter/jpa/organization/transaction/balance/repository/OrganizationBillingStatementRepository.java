package ai.yourouter.jpa.organization.transaction.balance.repository;

import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationBillingStatement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface OrganizationBillingStatementRepository extends JpaRepository<OrganizationBillingStatement, Long> {

    public Optional<OrganizationBillingStatement> findOrganizationBillingStatementByOrganizationId(Long organizationId);
}
