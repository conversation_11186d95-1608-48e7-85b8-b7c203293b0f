package ai.yourouter.jpa.organization.team.bean;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_team")
public class OrganizationTeam {

    @Id
    private Long id;

    private Long userId;

    private Long organizationId;

    private Long createTime;

    /**
     * 0  成员
     * 1 admin
     */
    private Integer permission;

    //1可用 0不可用
    private Integer statuses;

}
