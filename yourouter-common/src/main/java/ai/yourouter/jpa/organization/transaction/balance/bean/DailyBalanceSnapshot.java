package ai.yourouter.jpa.organization.transaction.balance.bean;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "sp_platform_organization_daily_balance_snapshot")
public class DailyBalanceSnapshot {

    @Id
    private Long id;

    private LocalDate snapshotDate;

    private Long organizationId;

    private Long balanceNanos;
}
