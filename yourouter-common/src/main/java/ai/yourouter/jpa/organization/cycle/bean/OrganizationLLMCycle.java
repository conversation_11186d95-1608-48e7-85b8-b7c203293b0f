package ai.yourouter.jpa.organization.cycle.bean;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.ColumnDefault;

import java.math.BigDecimal;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_llm_cycle")
public class OrganizationLLMCycle {

    @Id
    private Long id;

    private Long organizationId;

    private Long systemModelId;

    @ColumnDefault("0")
    private Long textPrompt;

    @ColumnDefault("0")
    private Long textCachePrompt;

    @ColumnDefault("0")
    private Long textCompletion;

    @ColumnDefault("0")
    private Long textCachePromptWrite5M;

    @ColumnDefault("0")
    private Long textCachePromptWrite1H;

    @ColumnDefault("0")
    private Long audioPrompt;

    @ColumnDefault("0")
    private Long audioCachePrompt;

    @ColumnDefault("0")
    private Long audioCompletion;

    @ColumnDefault("0")
    private Long reasoningCompletion;

    @ColumnDefault("0")
    private Long imagePrompt;

    @ColumnDefault("0")
    private Long imageCachePrompt;

    @ColumnDefault("0")
    private Long imageCompletion;

    @ColumnDefault("0")
    private Long request;

    @ColumnDefault("0")
    private Long searchTool;

    @Column(precision = 29, scale = 18)
    private BigDecimal quota;

    private Long bucketStart;

    @ColumnDefault("1")
    private Integer type;

    @ColumnDefault("0")
    private Integer statuses;
}
