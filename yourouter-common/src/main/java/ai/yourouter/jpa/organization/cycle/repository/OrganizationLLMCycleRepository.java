package ai.yourouter.jpa.organization.cycle.repository;


import ai.yourouter.jpa.organization.cycle.bean.OrganizationLLMCycle;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface OrganizationLLMCycleRepository extends JpaRepository<OrganizationLLMCycle,Long> {


    @Modifying
    @Query(value = """
        INSERT INTO sp_platform_organization_llm_cycle
            (id, organization_id, system_model_id, bucket_start,
             text_prompt, text_cache_prompt, text_cache_prompt_write5m,
             text_cache_prompt_write1h, text_completion,
             audio_prompt, audio_cache_prompt, audio_completion,
             reasoning_completion,
             image_prompt, image_cache_prompt, image_completion,
             request,search_tool, quota, statuses)
        VALUES
            (:id, :orgId, :modelId, :bucketStart,
             :tp, :tcp, :tcp5, :tcp1h, :tc,
             :ap, :acp, :ac, :rc,
             :ip, :icp, :ic,
             :req, :search_tool :quota, 0)
        ON CONFLICT (organization_id, system_model_id, bucket_start)
        DO UPDATE SET
            text_prompt                    = sp_platform_organization_llm_cycle.text_prompt                    + EXCLUDED.text_prompt,
            text_cache_prompt              = sp_platform_organization_llm_cycle.text_cache_prompt              + EXCLUDED.text_cache_prompt,
            text_cache_prompt_write5m      = sp_platform_organization_llm_cycle.text_cache_prompt_write5m      + EXCLUDED.text_cache_prompt_write5m,
            text_cache_prompt_write1h      = sp_platform_organization_llm_cycle.text_cache_prompt_write1h      + EXCLUDED.text_cache_prompt_write1h,
            text_completion                = sp_platform_organization_llm_cycle.text_completion                + EXCLUDED.text_completion,
            audio_prompt                   = sp_platform_organization_llm_cycle.audio_prompt                   + EXCLUDED.audio_prompt,
            audio_cache_prompt             = sp_platform_organization_llm_cycle.audio_cache_prompt             + EXCLUDED.audio_cache_prompt,
            audio_completion               = sp_platform_organization_llm_cycle.audio_completion               + EXCLUDED.audio_completion,
            reasoning_completion           = sp_platform_organization_llm_cycle.reasoning_completion           + EXCLUDED.reasoning_completion,
            image_prompt                   = sp_platform_organization_llm_cycle.image_prompt                   + EXCLUDED.image_prompt,
            image_cache_prompt             = sp_platform_organization_llm_cycle.image_cache_prompt             + EXCLUDED.image_cache_prompt,
            image_completion               = sp_platform_organization_llm_cycle.image_completion               + EXCLUDED.image_completion,
            request                        = sp_platform_organization_llm_cycle.request                        + EXCLUDED.request,
            search_tool                    = sp_platform_organization_llm_cycle.search_tool                    + EXCLUDED.search_tool,
            quota                          = sp_platform_organization_llm_cycle.quota                          + EXCLUDED.quota
        """, nativeQuery = true)
    void upsertCycle(@Param("id") Long id, @Param("orgId") Long orgId,
                     @Param("modelId") Long modelId, @Param("bucketStart") Long bucketStart,
                     @Param("tp") Long tp, @Param("tcp") Long tcp,
                     @Param("tcp5") Long tcp5, @Param("tcp1h") Long tcp1h,
                     @Param("tc") Long tc, @Param("ap") Long ap,
                     @Param("acp") Long acp, @Param("ac") Long ac,
                     @Param("rc") Long rc, @Param("ip") Long ip,
                     @Param("icp") Long icp, @Param("ic") Long ic,
                     @Param("req") Long req,@Param("st") Long st, @Param("quota") BigDecimal quota);

}
