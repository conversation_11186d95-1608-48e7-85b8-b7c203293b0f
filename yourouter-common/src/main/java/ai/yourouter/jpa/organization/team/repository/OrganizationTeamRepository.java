package ai.yourouter.jpa.organization.team.repository;

import ai.yourouter.jpa.organization.team.bean.OrganizationTeam;
import ai.yourouter.jpa.user.info.bean.UserInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrganizationTeamRepository extends JpaRepository<OrganizationTeam, String> {


    OrganizationTeam findOrganizationTeamByUserIdAndOrganizationIdAndStatuses(Long userId, Long organizationId, Integer statuses);

    List<OrganizationTeam> findOrganizationTeamsByUserIdAndStatuses(Long userId, Integer statuses);

    OrganizationTeam findOrganizationTeamByOrganizationIdAndUserId(Long organizationId, Long userId);

    List<OrganizationTeam> findOrganizationTeamsByOrganizationIdAndStatuses(Long organizationId, Integer statuses);

    List<OrganizationTeam> findOrganizationTeamsByOrganizationIdAndPermissionAndStatuses(Long organizationId, Integer permission, Integer statuses);

    Boolean existsByUserId(Long userId);


    @Query(value = """
        SELECT CASE WHEN EXISTS (
          SELECT 1
          FROM sp_platform_organization_team t
          JOIN sp_platform_user_info u ON u.id = t.user_id
          WHERE t.organization_id = :orgId
            AND COALESCE(t.statuses, 1) = 1
            AND COALESCE(u.statuses, 1) = 1
            AND lower(split_part(u.email,'@',2)) = lower(split_part(:email,'@',2))
        ) THEN TRUE ELSE FALSE END
        """, nativeQuery = true)
    boolean existsMemberWithSameEmailDomain(@Param("orgId") Long orgId,
                                            @Param("email") String email);


    @Query(value = """
        SELECT u.*
        FROM sp_platform_organization_team t
        JOIN sp_platform_user_info u ON u.id = t.user_id
        WHERE t.organization_id = :orgId
          AND COALESCE(t.statuses, 1) = 1
          AND COALESCE(u.statuses, 1) = 1
          AND lower(split_part(u.email,'@',2)) = lower(split_part(:email,'@',2))
        """, nativeQuery = true)
    List<UserInfo> findMembersWithSameEmailDomain(@Param("orgId") Long orgId,
                                                  @Param("email") String email);

}
