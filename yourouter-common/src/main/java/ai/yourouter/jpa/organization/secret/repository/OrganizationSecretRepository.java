package ai.yourouter.jpa.organization.secret.repository;

import ai.yourouter.jpa.organization.secret.bean.OrganizationSecret;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrganizationSecretRepository extends JpaRepository<OrganizationSecret, Long> {

    List<OrganizationSecret> findOrganizationSecretsByOrganizationIdAndStatusesIn(Long organizationId, List<Integer> statuses);

    Page<OrganizationSecret> findOrganizationSecretsByOrganizationIdAndStatusesIn(Long organizationId, List<Integer> statuses, Pageable pageable);

    public OrganizationSecret findOrganizationSecretByOrganizationIdAndId(Long organizationId, Long id);

    public Integer countOrganizationSecretsByOrganizationIdAndStatuses(Long organizationId, Integer statuses);

    public List<OrganizationSecret> findOrganizationSecretsByStatuses( Integer statuses);
}
