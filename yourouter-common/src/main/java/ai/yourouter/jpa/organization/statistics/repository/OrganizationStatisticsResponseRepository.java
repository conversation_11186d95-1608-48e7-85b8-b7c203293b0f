package ai.yourouter.jpa.organization.statistics.repository;

import ai.yourouter.jpa.organization.statistics.bean.OrganizationLLMStatistics;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;

@Repository
public interface OrganizationStatisticsResponseRepository extends JpaRepository<OrganizationLLMStatistics,Long> {

    @Query(value = """
    SELECT COALESCE(SUM(quota), 0) as totalQuota
    FROM (
        SELECT quota
        FROM sp_platform_organization_llm_statistics
        WHERE organization_id = :orgId
          AND createTime BETWEEN :startDay AND :endDay

        UNION ALL

        SELECT quota
        FROM sp_platform_organization_search_statistics
        WHERE organization_id = :orgId
          AND createTime BETWEEN :startDay AND :endDay
    ) AS combined
    """, nativeQuery = true)
    BigDecimal sumTotalQuotaInRange(@Param("orgId") Long orgId,
                                    @Param("startDay") Long startDay,
                                    @Param("endDay") Long endDay);
}
