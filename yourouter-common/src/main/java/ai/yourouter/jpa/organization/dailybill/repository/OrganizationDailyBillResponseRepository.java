package ai.yourouter.jpa.organization.dailybill.repository;

import ai.yourouter.jpa.organization.dailybill.bean.OrganizationLLMDailyBill;
import ai.yourouter.response.organization.ModelDailyQuota;
import ai.yourouter.response.organization.OrganizationDailyBillResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrganizationDailyBillResponseRepository extends JpaRepository<OrganizationLLMDailyBill,Long> {

    /**
     * 一次查两张表
     */
    @Query(value = """
        SELECT
            bill_day                 AS billDay,
            organization_id          AS organizationId,
            model_name               AS modelName,
            text_prompt              AS textPrompt,
            text_cache_prompt        AS textCachePrompt,
            text_completion          AS textCompletion,
            text_cache_prompt_write1h  AS textCachePromptWrite,
            text_cache_prompt_write5m  AS textCachePromptWrite5M,
            audio_prompt             AS audioPrompt,
            audio_cache_prompt       AS audioCachePrompt,
            audio_completion         AS audioCompletion,
            reasoning_completion     AS reasoningCompletion,
            image_prompt             AS imagePrompt,
            image_cache_prompt       AS imageCachePrompt,
            image_completion         AS imageCompletion,
            NULL                     AS call,
            request                  AS request,
            0                        AS modelType,
            quota                    AS quota,
            search_tool                AS searchTool
        FROM
            sp_platform_organization_llm_daily_bill
        WHERE
            organization_id = :orgId
            AND bill_day BETWEEN :startDay AND :endDay

        UNION ALL

        SELECT
            bill_day                 AS billDay,
            organization_id          AS organizationId,
            model_name               AS modelName,
            NULL                     AS textPrompt,
            NULL                     AS textCachePrompt,
            NULL                     AS textCompletion,
            NULL                     AS textCachePromptWrite,
            NULL                     AS audioPrompt,
            NULL                     AS audioCachePrompt,
            NULL                     AS audioCompletion,
            NULL                     AS reasoningCompletion,
            NULL                     AS imagePrompt,
            NULL                     AS imageCachePrompt,
            NULL                     AS imageCompletion,
            call                     AS call,
            request                  AS request,
            1                        AS modelType,
            quota                    AS quota,
            NULL                     AS searchTool
        FROM
            sp_platform_organization_search_daily_bill
        WHERE
            organization_id = :orgId
            AND bill_day BETWEEN :startDay AND :endDay
        """, nativeQuery = true)
    List<OrganizationDailyBillResponse> findBills(@Param("orgId") Long orgId,
                                                  @Param("startDay") Long startDay,
                                                  @Param("endDay") Long endDay);



}
