package ai.yourouter.jpa.organization.transaction.failed.bean;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Cacheable(value = true)
@Table(name = "sp_platform_organization_transaction_failed")
public class FailedTransaction {

    @Id
    private Long id;

    private Long  organizationId;

    @Column(columnDefinition = "text")
    private String messages;

    @Column(columnDefinition = "text")
    private String errorMessage;

    private Long createTime;

    private Integer statuses;

}
