package ai.yourouter.response;

import lombok.Data;

import java.util.List;

@Data
public class PageResponse<T> {
    private List<T> content;
    private Long total;
    private Integer totalPages;

    public PageResponse(List<T> content, Long total, Integer totalPages) {
        this.content = content;
        this.total = total;
        this.totalPages = totalPages;
    }

    public static <T> PageResponse<T> empty() {
        return new PageResponse<>(List.of(), 0L, 0);
    }

    public List<T> getContent() {
        return content;
    }

    public Long getTotal() {
        return total;
    }

    public Integer getTotalPages() {
        return totalPages;
    }
}