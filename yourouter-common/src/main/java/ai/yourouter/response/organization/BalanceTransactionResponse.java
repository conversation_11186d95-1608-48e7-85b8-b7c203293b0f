package ai.yourouter.response.organization;

import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
public class BalanceTransactionResponse {

    private final BigDecimal amount;

    private final Long created;

    private final BalanceTransaction.TransactionType type;

    private final String invoiceUrl; // 可能为 null

}
