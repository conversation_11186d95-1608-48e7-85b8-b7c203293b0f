package ai.yourouter.response.organization;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DailyPerModelQuotaResponse {

    private String code;
    private String message;
    private boolean success;
    private Meta meta;
    private Map<String, List<ModelDailyQuota>> data;

    // getters/setters...

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Meta {
        private String organizationId;
        private int year;
        private int month;
        private String timezone; // 固定 "UTC"
        private Range range;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Range {
        private long startMs; // 月初(UTC0)
        private long endMs;   // 次月月初(UTC0)
        // getters/setters...
    }
}
