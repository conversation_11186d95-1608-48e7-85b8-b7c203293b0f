package ai.yourouter.response.system.model.info;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class ModelPriceInfoResponse {

    private String modelName;
    private String manufacturerName;
    private List<String> vendorNames;
    private Integer modelType;
    private Map<String, String> prices;

    /**
     * 格式化价格：
     * - null 或 0 → "-"
     * - 如果小数部分全部为零 → 只保留整数部分
     * - 否则去掉尾随零
     */
    public static String format(BigDecimal value) {
        if (value == null || BigDecimal.ZERO.compareTo(value) == 0) {
            return "-";
        }
        // 如果是整数（小数部分为 0）
        if (value.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
            // toBigIntegerString 保证无小数点
            return value.toBigInteger().toString();
        }
        // 否则去掉尾随零，并输出非科学计数法
        BigDecimal stripped = value.stripTrailingZeros();
        String plain = stripped.toPlainString();
        return plain;
    }


    /** 去尾随零并转为非科学计数法 */
    private static String stripZeros(BigDecimal v) {
        BigDecimal stripped = v.stripTrailingZeros();
        return stripped.toPlainString();
    }

    /** 格式化 LLM 价格：每 1M tokens */
    public static String formatLLM(BigDecimal value) {
        if (value == null || BigDecimal.ZERO.compareTo(value) == 0) {
            return "-";
        }
        return stripZeros(value) + "/MTokens";
    }

    /** 格式化 Search 价格：每 1K 调用 */
    public static String formatSearch(BigDecimal value) {
        if (value == null || BigDecimal.ZERO.compareTo(value) == 0) {
            return "-";
        }
        return stripZeros(value) + "/1KCall";
    }

}
