package ai.yourouter.common.utils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Arrays;

public class TimerUtils {

    public static long[] monthRange(int year, int month, ZoneId zone) {
        LocalDate firstDay   = LocalDate.of(year, month, 1);
        long startMillis = firstDay.atStartOfDay(zone).toInstant().toEpochMilli();
        long endMillis   = firstDay.plusMonths(1).atStartOfDay(zone).toInstant().toEpochMilli();
        return new long[]{ startMillis, endMillis };
    }

    public static void main(String[] args) {
        ZoneId zone = ZoneId.of("Asia/Shanghai");
        long[] range = monthRange(2025, 5, zone);
        System.out.println(Arrays.toString(range));
    }
}
