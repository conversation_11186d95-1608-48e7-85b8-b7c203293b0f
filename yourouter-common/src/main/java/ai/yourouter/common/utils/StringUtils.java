package ai.yourouter.common.utils;

import java.util.Arrays;

public class StringUtils {

    public static boolean areAnagrams(String str1, String str2) {
        // 如果两个字符串长度不同，则它们肯定不相等
        if (str1.length() != str2.length()) {
            return false;
        }

        // 将字符串转换为字符数组
        char[] arr1 = str1.toCharArray();
        char[] arr2 = str2.toCharArray();

        // 对字符数组进行排序
        Arrays.sort(arr1);
        Arrays.sort(arr2);

        // 比较排序后的字符数组是否相等
        return Arrays.equals(arr1, arr2);
    }

    public static String mask(String input) {
        if (input == null || input.length() <= 6) {
            // 如果字符串长度小于等于6，直接返回原字符串
            return input;
        }

        // 获取前3位和后3位
        String prefix = input.substring(0, 3);
        String suffix = input.substring(input.length() - 3);

        // 用"****"代替中间的部分
        String masked = prefix + "****" + suffix;

        return masked;
    }

}
