package ai.yourouter.common.utils;

import javax.naming.NamingEnumeration;
import javax.naming.directory.*;
import java.util.Hashtable;

public final class DnsUtils {
    private DnsUtils() {}

    public static boolean hasMxRecord(String domain) {
        try {
            DirContext ictx = new InitialDirContext(env());
            Attributes attrs = ictx.getAttributes("dns:/" + domain, new String[]{"MX"});
            Attribute mxAttr = attrs.get("MX");
            return mxAttr != null && mxAttr.size() > 0;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean hasARecord(String domain) {
        try {
            DirContext ictx = new InitialDirContext(env());
            Attributes attrs = ictx.getAttributes("dns:/" + domain, new String[]{"A"});
            Attribute aAttr = attrs.get("A");
            return aAttr != null && aAttr.size() > 0;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean hasDmarcRecord(String domain) {
        try {
            DirContext ictx = new InitialDirContext(env());
            String dmarcHost = "_dmarc." + domain;
            Attributes attrs = ictx.getAttributes("dns:/" + dmarcHost, new String[]{"TXT"});
            Attribute txt = attrs.get("TXT");
            if (txt == null) return false;
            NamingEnumeration<?> en = txt.getAll();
            while (en.hasMore()) {
                String v = en.next().toString();
                if (v.contains("v=DMARC1")) return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private static Hashtable<String, String> env() {
        Hashtable<String, String> env = new Hashtable<>();
        env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
        env.put("java.naming.provider.url", "dns:");
        env.put("com.sun.jndi.dns.timeout.initial", "2000"); // 2s
        env.put("com.sun.jndi.dns.timeout.retries", "1");
        return env;
    }
}