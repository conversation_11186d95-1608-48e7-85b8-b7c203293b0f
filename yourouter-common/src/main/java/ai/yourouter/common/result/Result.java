package ai.yourouter.common.result;

import ai.yourouter.common.constant.ErrorCodeEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import java.io.Serial;
import java.io.Serializable;

@Getter
@Builder
@AllArgsConstructor(access = AccessLevel.PRIVATE)     // 强制使用静态工厂/Builder
public class Result<T> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private final String code;
    private final String message;
    private final T data;            // 如确实不需要可直接删除

    // ----------- 工厂方法 -----------
    public static <T> Result<T> success() {
        return of(ErrorCodeEnum.SUCCESS, null);
    }

    public static <T> Result<T> success(T data) {
        return of(ErrorCodeEnum.SUCCESS, data);
    }

    public static <T> Result<T> fail(ErrorCodeEnum ec) {
        return of(ec, null);
    }

    public static <T> Result<T> of(ErrorCodeEnum ec, T data) {
        return new Result<>(ec.getCode(), ec.getDesc(), data);
    }

    // ----------- 实用方法 -----------
    public boolean isSuccess() {
        return ErrorCodeEnum.SUCCESS.getCode().equals(code);
    }
}