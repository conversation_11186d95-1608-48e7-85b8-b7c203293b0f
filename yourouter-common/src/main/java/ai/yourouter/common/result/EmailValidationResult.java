package ai.yourouter.common.result;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
public class EmailValidationResult {
    private boolean passed;
    private boolean isFreeDomain;
    private boolean isDisposableDomain;
    private boolean isRoleAccount;
    private boolean hasMx;
    private boolean hasDmarc;
    private boolean domainMatchesOrg;
    @Builder.Default
    private List<String> reasons = new ArrayList<>();

    public static EmailValidationResult fail(String reason) {
        return EmailValidationResult.builder().passed(false).reasons(List.of(reason)).build();
    }
}