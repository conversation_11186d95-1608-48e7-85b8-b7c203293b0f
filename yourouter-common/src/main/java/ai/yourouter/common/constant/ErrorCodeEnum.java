package ai.yourouter.common.constant;

public enum ErrorCodeEnum {

    SUCCESS("200", "success"),


    /** The user does not exist */
    USER_NOT_FOUND("20001", "User not found"),

    /** 需要人工审核 */
    NEEDS_REVIEW("10010", "Needs manual review"),

    /** 系统自动通过 */
    AUTO_APPROVED("10011", "Auto approved"),

    /** 参数非法 */
    PARAM_INVALID("40001", "Invalid parameters"),

    /** Insufficient permissions */
    PERMISSION_DENIED("10001", "Permission denied"),
    INVALID_AMOUNT("10002", "Amount must be a positive number of cents and a multiple of 100."),
    ORGANIZATION_NOT_FOUND("10003", "Organization not found"),
    MISSING_CUSTOMER_ID("10004", "Organization is missing Stripe customerId"),
    // 用户传入的退款金额无效
    INVALID_REFUND_AMOUNT("10005", "Refund amount must be a positive number of dollars."),
    // 组织没有可退款余额
    NO_REFUNDABLE_BALANCE("10006", "Organization has no refundable balance."),
    // 请求的退款金额超过可用余额
    REFUND_AMOUNT_EXCEEDS_BALANCE("10007", "Requested refund amount exceeds available refundable balance."),
    // ========== 参数/业务校验 ==========
    INVALID_ORGANIZATION("40001", "invalid organization"),

    REFUND_NEEDS_SINGLE_CHARGE("40005", "refund must be covered by a single charge"),

    // ========== Stripe 相关 ==========
    NO_STRIPE_CHARGE("40006", "stripe charge not found"),
    STRIPE_NOT_REFUNDABLE("40007", "stripe charge not refundable"),
    STRIPE_REFUND_FAILED("50001", "stripe refund failed");
    private String code;

    private String desc;

    ErrorCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" +
                "code='" + code + '\'' +
                ", desc='" + desc + '\'' +
                '}';
    }
}
