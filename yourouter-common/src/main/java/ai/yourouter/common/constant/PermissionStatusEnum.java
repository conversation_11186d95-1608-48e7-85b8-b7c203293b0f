package ai.yourouter.common.constant;

import lombok.Getter;

@Getter
public enum PermissionStatusEnum {

    UNAVAILABLE(0, "不可用"),
    AVAILABLE(1, "可用"),
    PENDING(2, "待审核"),
    SUSPENDED(3, "已暂停"),
    DISABLED(4, "已禁用");

    private final int code;
    private final String message;

    PermissionStatusEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static PermissionStatusEnum of(int code) {
        for (PermissionStatusEnum e : values()) {
            if (e.code == code) {
                return e;
            }
        }
        throw new IllegalArgumentException("Unknown permission status code: " + code);
    }
}
