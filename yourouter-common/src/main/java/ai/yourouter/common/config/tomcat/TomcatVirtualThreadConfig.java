//package ai.yourouter.common.config.tomcat;
//
//import org.springframework.boot.web.embedded.tomcat.TomcatProtocolHandlerCustomizer;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//import java.util.concurrent.Executors;
//
//@Configuration
//public class TomcatVirtualThreadConfig {
//
//    @Bean
//    public TomcatProtocolHandlerCustomizer<?> virtualThreadExecutor() {
//        return protocolHandler ->
//                // 为每个请求创建一条虚拟线程
//                protocolHandler.setExecutor(
//                        Executors.newVirtualThreadPerTaskExecutor());
//    }
//}