package ai.yourouter.common.config.redis;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

@Configuration
@EnableCaching
public class RedisTemplateConfiguration {


    @Bean                   // Bean 名默认叫 “redisTemplate”
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {

        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        // key、hashKey 都用字符串
        StringRedisSerializer keySer = StringRedisSerializer.UTF_8;
        template.setKeySerializer(keySer);
        template.setHashKeySerializer(keySer);

        // value、hashValue 统一用 JSON 序列化
        GenericJackson2JsonRedisSerializer valSer =
                new GenericJackson2JsonRedisSerializer();
        template.setValueSerializer(valSer);
        template.setHashValueSerializer(valSer);

        template.afterPropertiesSet();
        return template;
    }


    /** ========== 2. 只处理 String 值 ========== */
    @Bean("customStringRedisTemplate")               // 重命名避免与 Spring Boot 自带冲突
    @Primary                                         // 设为主要 bean，优先注入
    public RedisTemplate<String, String> customStringRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        StringRedisSerializer stringSer = StringRedisSerializer.UTF_8;
        template.setKeySerializer(stringSer);
        template.setHashKeySerializer(stringSer);
        template.setValueSerializer(stringSer);        // value 也用纯字符串
        template.setHashValueSerializer(stringSer);

        template.afterPropertiesSet();
        return template;
    }

    /** ========== 3. 只处理 Long 值 ========== */
    @Bean("longRedisTemplate")
    public RedisTemplate<String, Long> longRedisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Long> template = new RedisTemplate<>();
        template.setConnectionFactory(factory);

        StringRedisSerializer keySer = StringRedisSerializer.UTF_8;
        template.setKeySerializer(keySer);
        template.setHashKeySerializer(keySer);

        /*
         * GenericToStringSerializer 会把 Long → String，再按 UTF-8 存入 Redis；
         * 反序列化时把字符串转换回 Long，既简单又避免 JS 精度问题。
         */
        GenericToStringSerializer<Long> longSer = new GenericToStringSerializer<>(Long.class);
        template.setValueSerializer(longSer);
        template.setHashValueSerializer(longSer);

        template.afterPropertiesSet();
        return template;
    }

    /** 所有 Spring Cache 缓存统一 TTL＝60 秒 */
    @Bean
    public RedisCacheManager cacheManager(RedisConnectionFactory factory) {
        RedisCacheConfiguration cfg = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(1));
        return RedisCacheManager.builder(factory).cacheDefaults(cfg).build();
    }
}
