# Hispread AI API Router - UI设计风格指南

## 项目概述

**项目名称**: Hispread AI API Router Platform  
**技术栈**: React 19 + TypeScript + Vite + TailwindCSS 4.x  
**UI框架**: Radix UI + shadcn/ui (New York style)  
**状态管理**: Jo<PERSON>  
**路由**: React Router v7  

## 设计系统核心

### 1. 色彩系统

#### 主色调
- **Primary**: `#3C82F6` (蓝色) - 主要操作按钮、链接
- **Background**: 白色/深色模式自适应
- **Foreground**: 深灰色文字 `oklch(0.145 0 0)`

#### 语义色彩
- **Secondary**: 浅灰色背景 `oklch(0.97 0 0)`
- **Muted**: 次要文字颜色 `oklch(0.556 0 0)`
- **Destructive**: 红色 `oklch(0.577 0.245 27.325)` - 危险操作
- **Border**: 浅灰边框 `oklch(0.922 0 0)`

#### Zinc色系广泛应用
- `zinc-100` - 浅色背景
- `zinc-200` - 边框、分割线
- `zinc-500` - 次要文字
- `zinc-700` - 主要文字
- `zinc-800/900` - 深色按钮背景

### 2. 圆角系统

**基础圆角**: `--radius: 0.625rem` (10px)
- `--radius-sm`: 6px
- `--radius-md`: 8px  
- `--radius-lg`: 10px (默认)
- `--radius-xl`: 14px

### 3. 间距系统

遵循TailwindCSS标准间距，常用：
- `gap-2` (8px) - 小间距
- `gap-4` (16px) - 中等间距  
- `gap-6` (24px) - 大间距
- `px-4 py-2` - 标准内边距
- `px-8 py-4` - 页面级内边距

## 组件设计规范

### 1. 按钮系统 (CommonButton)

#### 变体设计
- **Primary**: 深色背景 `bg-zinc-800`，白色文字，带渐变覆盖层
- **Secondary**: 浅灰背景 `bg-zinc-200`，深色文字
- **Outline**: 透明背景，边框样式，悬停时浅色背景
- **Ghost**: 完全透明，仅悬停时显示背景
- **Destructive**: 红色背景，用于危险操作
- **Link**: 链接样式，蓝色文字，悬停下划线

#### 尺寸规格
- **xs**: `px-2 py-1 text-xs`
- **sm**: `px-2.5 py-1.5 text-xs`  
- **md**: `px-3 py-1.5 text-sm` (默认)
- **lg**: `px-4 py-2 text-sm`

#### 特殊效果
- 渐变覆盖层增强视觉层次
- 150ms过渡动画
- Focus ring样式
- Loading状态支持

### 2. 模态框设计

#### 布局特点
- 最大宽度通常为 `sm:max-w-md` 或 `sm:max-w-[45%]`
- 圆角 `rounded-xl`
- 阴影 `shadow-lg`
- 背景模糊覆盖层

#### 结构组成
- **Header**: 标题区域，可包含图标和描述
- **Content**: 主要内容区域，支持滚动
- **Footer**: 操作按钮区域，通常右对齐

#### 交互设计
- ESC键关闭（部分模态框禁用）
- 点击外部关闭（部分禁用）
- 动画进入/退出效果

### 3. 表单组件

#### Input输入框
- 边框颜色: `border-zinc-200`
- Focus状态: `focus:border-zinc-400`
- 高度: `h-11` (44px)
- 圆角: 标准圆角
- 错误状态: 红色边框

#### Select选择器
- 与Input保持一致的视觉风格
- 下拉箭头图标
- 选中项高亮

### 4. 布局组件

#### 侧边栏 (Sidebar)
- 宽度: `--sidebar-width` 变量控制
- 背景: `bg-sidebar`
- 可折叠设计
- 移动端抽屉式

#### 主内容区
- 背景: `bg-[#FAFAFA]` (顶部导航)
- 内边距: `px-8 py-4`
- 滚动区域: `ScrollArea`组件

## 页面设计模式

### 1. 登录页面
- **左右分栏布局**: 左侧图片+品牌信息，右侧登录表单
- **响应式**: 移动端隐藏左侧图片
- **品牌展示**: 大标题 + 标语描述
- **表单居中**: 最大宽度限制，垂直居中

### 2. 平台页面
- **统一页头**: 标题 + 描述 + 操作按钮
- **卡片容器**: 白色背景，圆角，阴影边框
- **分割线**: `Separator`组件分隔内容区域

### 3. 模态框页面
- **图标 + 标题**: 左侧图标，右侧标题和描述
- **分步骤**: Tab式导航（如创建组织）
- **操作确认**: 双重确认模式

## 动画与交互

### 1. 过渡动画
- **持续时间**: 150ms (快速), 200ms (标准)
- **缓动函数**: `ease-in-out`
- **属性**: 颜色、背景、边框、阴影

### 2. 悬停效果
- 按钮: 背景色变化
- 链接: 颜色变化 + 下划线
- 卡片: 轻微阴影增强

### 3. 焦点状态
- Ring样式: `focus-visible:ring-2`
- 颜色: 与主题色相关
- 偏移: `focus-visible:ring-offset-2`

## 响应式设计

### 1. 断点系统
- **sm**: 640px+
- **md**: 768px+  
- **lg**: 1024px+

### 2. 布局适配
- 侧边栏: 桌面端固定，移动端抽屉
- 网格: `grid-cols-1 md:grid-cols-2`
- 间距: 移动端减小内边距

## 图标系统

- **图标库**: Lucide React
- **尺寸**: 通常 `w-4 h-4` (16px) 或 `w-5 h-5` (20px)
- **颜色**: 继承文字颜色或使用语义色彩
- **位置**: 文字左侧，标准间距

## 数据展示

### 1. 表格
- **框架**: TanStack Table
- **样式**: 简洁边框，斑马纹可选
- **操作**: 行内操作按钮

### 2. 图表
- **库**: Recharts
- **色彩**: 使用chart-1到chart-5变量
- **响应式**: 容器查询适配

## 状态反馈

### 1. 加载状态
- **按钮**: Loader2图标 + "Loading..."文字
- **页面**: Loading组件，最小高度80vh
- **骨架屏**: 可选实现

### 2. 消息提示
- **库**: Sonner
- **位置**: 右上角
- **类型**: Success, Error, Info
- **样式**: 彩色图标 + 简洁文字

### 3. 表单验证
- **实时验证**: 输入时检查
- **错误显示**: 红色边框 + 错误文字
- **成功状态**: 绿色提示

## 可访问性

### 1. 键盘导航
- Tab顺序合理
- Focus指示清晰
- 快捷键支持

### 2. 屏幕阅读器
- 语义化HTML
- ARIA标签
- 描述性文字

### 3. 颜色对比
- 符合WCAG标准
- 深色模式支持
- 色盲友好

## 开发规范

### 1. 组件命名
- PascalCase: 组件名
- camelCase: 属性名
- kebab-case: CSS类名

### 2. 样式组织
- Tailwind优先
- 组件级样式
- 全局变量统一

### 3. 类型安全
- TypeScript严格模式
- 组件Props类型定义
- API响应类型

## 特色组件分析

### 1. 复制按钮 (CopyButton)
- **动画效果**: Framer Motion驱动的状态切换
- **视觉反馈**: 复制图标 → 绿色对勾动画
- **尺寸变体**: xs, sm, md, lg四种规格
- **交互**: 2秒后自动恢复初始状态

### 2. 团队切换器 (TeamSwitcher)
- **下拉选择**: 组织/团队切换
- **状态指示**: 当前选中项高亮
- **图标支持**: 左侧图标 + 文字标签

### 3. 面包屑导航 (AppBreadcrumb)
- **路径显示**: 当前页面层级
- **分隔符**: 标准斜杠或箭头
- **可点击**: 支持快速导航

## 业务组件模式

### 1. 钱包充值流程
- **预设金额**: 网格布局的快速选择按钮
- **自定义金额**: 带美元符号的数字输入
- **金额预览**: 突出显示的确认卡片
- **支付集成**: Stripe支付页面跳转

### 2. 团队成员管理
- **邮箱验证**: 实时格式检查
- **权限选择**: 下拉选择器
- **状态反馈**: 成功/失败消息提示
- **头像显示**: 默认头像 + 首字母

### 3. 密钥管理
- **创建流程**: 分步骤模态框
- **安全提示**: 信息图标 + 提示文字
- **操作确认**: 双重确认机制

## 数据格式化

### 1. 金额显示
```javascript
// 中文格式化
formatAmount = (amount) => amount.toLocaleString('zh-CN', {
  minimumFractionDigits: 2,
  maximumFractionDigits: 2
})

// 美元格式化
formatCurrency = (amount) => amount.toLocaleString('en-US', {
  style: 'currency',
  currency: 'USD'
})
```

### 2. 日期时间
- **库**: date-fns
- **格式**: 本地化显示
- **相对时间**: "2小时前"样式

## 错误处理设计

### 1. 表单错误
- **即时验证**: 失焦时检查
- **错误样式**: 红色边框 + 错误文字
- **错误位置**: 输入框下方

### 2. 网络错误
- **Toast提示**: 右上角消息
- **重试机制**: 操作失败后提供重试
- **降级处理**: 优雅的错误状态

### 3. 权限错误
- **页面级**: 403/404错误页面
- **组件级**: 禁用状态显示
- **提示信息**: 清晰的权限说明

## 性能优化

### 1. 代码分割
- **路由级**: React.lazy + Suspense
- **组件级**: 动态导入大型组件
- **第三方库**: 按需加载

### 2. 图片优化
- **格式**: WebP优先，PNG降级
- **尺寸**: 响应式图片
- **懒加载**: 视口内加载

### 3. 状态管理
- **Jotai**: 原子化状态
- **局部状态**: 组件内useState
- **缓存策略**: SWR/React Query模式

## 国际化考虑

### 1. 文字内容
- **硬编码**: 当前主要为英文
- **扩展性**: 预留i18n接口
- **RTL支持**: 布局适配准备

### 2. 数字格式
- **货币**: 美元为主
- **日期**: 本地化格式
- **数量**: 千分位分隔

## 浏览器兼容性

### 1. 现代浏览器
- **Chrome**: 90+
- **Firefox**: 88+
- **Safari**: 14+
- **Edge**: 90+

### 2. 特性支持
- **CSS Grid**: 完全支持
- **Flexbox**: 完全支持
- **CSS Variables**: 完全支持
- **ES6+**: Vite转译支持

## 部署与构建

### 1. 构建优化
- **Vite**: 快速构建工具
- **TypeScript**: 类型检查
- **ESLint**: 代码质量
- **Tree Shaking**: 死代码消除

### 2. 资源优化
- **CSS**: TailwindCSS purge
- **JS**: 代码分割 + 压缩
- **图片**: 自动优化
- **字体**: 子集化加载

---

**更新日期**: 2025-01-25
**版本**: v1.0
**维护者**: 设计开发团队

## 附录：关键文件结构

```
src/
├── app/                    # 页面组件
│   ├── login/             # 登录页面
│   ├── platform/          # 平台主要页面
│   └── 404/               # 错误页面
├── components/
│   ├── ui/                # 基础UI组件
│   ├── modals/            # 模态框组件
│   └── layout/            # 布局组件
├── lib/                   # 工具函数
├── hooks/                 # 自定义Hooks
├── state/                 # 状态管理
└── api/                   # API接口
```
