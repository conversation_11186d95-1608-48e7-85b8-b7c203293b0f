plugins {
    id 'java'
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'ai.yourouter'
version = '1.0-SNAPSHOT'

repositories {
    mavenCentral()
}


dependencies {
    implementation project(':yourouter-common')
    implementation 'io.vavr:vavr:0.10.4'
    implementation 'io.opentelemetry:opentelemetry-api:1.31.0'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    runtimeOnly 'io.micrometer:micrometer-registry-prometheus:1.14.3'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'

    implementation 'com.clickhouse:clickhouse-jdbc:0.9.0'

    // Lombok 依赖和注解处理器
    runtimeOnly 'org.postgresql:postgresql'
    implementation 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

}

tasks.named('test') {
    useJUnitPlatform()
}
