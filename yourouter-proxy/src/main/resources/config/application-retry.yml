# 重试配置示例
#
# 重试机制说明：
# 1. 重试覆盖整个请求流程：获取密钥 + 调用第三方 API
# 2. 使用 Reactor 的指数退避重试策略
# 3. 支持流式和非流式请求的重试
# 4. 重试过程中会记录详细日志
# 5. 重试计数会更新到 ChatContext 中

kmg:
  retry:
    # 是否启用重试，默认为 true
    enabled: true
    # 最大重试次数，默认为 3
    # 注意：这是重试次数，不包括首次请求，所以总共会尝试 max-attempts + 1 次
    max-attempts: 2
    # 初始延迟时间，默认为 1 秒
    # 第一次重试的延迟时间
    initial-delay: 20ms
    # 最大延迟时间，默认为 10 秒
    # 重试延迟的上限，防止延迟时间过长
    max-delay: 10s
    # 延迟倍数，默认为 2.0（指数退避）
    # 每次重试的延迟时间 = 上次延迟时间 * multiplier
    # 例如：1s -> 2s -> 4s -> 8s（但不会超过 max-delay）
    multiplier: 1.1