-- 测试表创建脚本
-- 用于验证修复后的表结构是否正确

-- 删除测试表（如果存在）
DROP TABLE IF EXISTS test_request_records;

-- 创建测试表
CREATE TABLE test_request_records (
    request_id       String,
    organization_id  UInt64,
    request_time     DateTime64(3, 'UTC'),
    response_time    DateTime64(3, 'UTC'),
    duration         UInt64,
    request_path     String CODEC(ZSTD(1)),
    http_method      LowCardinality(String),
    model_name       LowCardinality(String),
    real_model_name  LowCardinality(String),
    vendor           LowCardinality(String),
    key_id           String,
    key_name         String,
    is_stream        UInt8,
    request_body     String CODEC(ZSTD(3)),
    response_body    String CODEC(ZSTD(3)),
    request_headers  String CODEC(ZSTD(1)),
    response_status  UInt16,
    error_message    String,
    client_ip        String,
    user_agent       String CODEC(ZSTD(1)),
    cf_connecting_ip String,
    cf_ip_country    LowCardinality(String),
    cf_ray           String,
    retry_count      UInt32,
    is_success       UInt8,
    access_token     String,
    created_at       DateTime64(3, 'UTC') DEFAULT now64(3)
) ENGINE = MergeTree
PARTITION BY toYYYYMM(request_time)
ORDER BY (request_time, organization_id)
SETTINGS index_granularity = 8192;

-- 插入测试数据
INSERT INTO test_request_records (
    request_id, organization_id, request_time, response_time, duration,
    request_path, http_method, model_name, real_model_name, vendor,
    key_id, key_name, is_stream, request_body, response_body,
    request_headers, response_status, error_message, client_ip,
    user_agent, cf_connecting_ip, cf_ip_country, cf_ray,
    retry_count, is_success, access_token
) VALUES (
    'test-123', 1, now64(3), now64(3), 1500,
    '/v1/chat/completions', 'POST', 'gpt-4', 'gpt-4-0613', 'openai',
    'key-123', 'test-key', 0, '{"test": "request"}', '{"test": "response"}',
    '{"user-agent": "test"}', 200, '', '127.0.0.1',
    'test-agent', '127.0.0.1', 'CN', 'test-ray',
    0, 1, 'sk-test-1234567890abcdef'
);

-- 验证数据插入和时区显示
SELECT 
    request_id,
    request_time as utc_time,
    toTimeZone(request_time, 'Asia/Shanghai') as beijing_time,
    created_at as created_utc,
    toTimeZone(created_at, 'Asia/Shanghai') as created_beijing
FROM test_request_records;

-- 清理测试表
-- DROP TABLE test_request_records;
