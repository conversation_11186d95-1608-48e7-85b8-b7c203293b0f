spring.data.redis.timeout=5000
spring.data.redis.connect-timeout=5000
spring.data.redis.repositories.enabled=false
spring.data.redis.lettuce.pool.enabled=true
spring.data.redis.lettuce.pool.max-active=200
spring.data.redis.lettuce.pool.max-wait=-1
spring.data.redis.lettuce.pool.max-idle=100
spring.data.redis.lettuce.pool.min-idle=10

spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.hikari.auto-commit=true
spring.datasource.hikari.pool-name=pgsql
spring.datasource.hikari.minimum-idle=100
spring.datasource.hikari.maximum-pool-size=2000
spring.datasource.hikari.connection-timeout=10000
spring.datasource.hikari.allow-pool-suspension=false
spring.datasource.hikari.isolate-internal-queries=false
spring.datasource.hikari.read-only=false
spring.datasource.hikari.register-mbeans=false
spring.datasource.hikari.validation-timeout=1000
spring.datasource.hikari.leak-detection-threshold=500000

spring.jpa.show-sql=false
spring.jpa.open-in-view=false
spring.jpa.properties.hibernate.hbm2ddl.auto=none
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.cache.use_second_level_cache=false
spring.jpa.properties.hibernate.cache.use_query_cache=false
spring.jpa.properties.jakarta.persistence.sharedCache.mode=NONE
spring.jpa.properties.hibernate.jdbc.time_zone=UTC


spring.data.redis.url=redis://Hispread0719.@192.168.0.143:6379
spring.data.redis.jedis.pool.max-active=100
spring.data.redis.jedis.pool.max-wait=-1
spring.data.redis.jedis.pool.max-idle=10
spring.data.redis.jedis.pool.min-idle=10

# ???????????????????
 spring.datasource.url=************************************************************************
 spring.datasource.username=hispread
 spring.datasource.password=Hispread0719.

server.port=80
kmg.service.base-url:http://192.168.0.46:80

# ClickHouse ??
clickhouse.enabled=true
ck.datasource.url=*****************************************************************************
ck.datasource.username=admin
ck.datasource.password=Hispread0719.
ck.datasource.pool-size=100
ck.datasource.socket-timeout=300000
ck.datasource.connection-timeout=30000