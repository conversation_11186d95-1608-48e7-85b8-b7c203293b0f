package ai.yourouter.search.remote;

import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.usage.ChatUsage;
import ai.yourouter.common.remote.BaseRemoteService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SearchRemoteService extends BaseRemoteService {

    public SearchRemoteService(KmgRemoteService kmgRemoteService) {
        super(kmgRemoteService);
    }

    /**
     * Search模块特有的使用量记录方法
     */
    protected void recordUsage(ChatContext chatContext) {
        chatContext.setChatUsage(new ChatUsage(1, 0));
    }
}
