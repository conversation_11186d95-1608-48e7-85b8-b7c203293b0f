package ai.yourouter.search.remote;


import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.HashMap;
import java.util.Optional;

@Slf4j
@Component
public class AzureBingSearchRemoteService extends SearchRemoteService {

    private final WebClient httpWebClient;

    public AzureBingSearchRemoteService(KmgRemoteService kmgRemoteService,
                                        @Qualifier("httpWebClient") WebClient httpWebClient) {
        super(kmgRemoteService);
        this.httpWebClient = httpWebClient;
    }

    public Mono<Object> search(HashMap<String, Object> params, HashMap<String, Object> passHeaders, ChatContext chatContext) {
        var keyInfo = chatContext.getKeyInfo();
        return Mono.just(params)
                .doOnNext(hashMap -> log.info("Bing Search 请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                        chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getKeyInfo().getToken()))
                .flatMap(key ->
                        httpWebClient.get().uri(chatContext.getKeyInfo().getDomain() + "/v7.0/search?" + StringUtils.mapToParams(params))
                                .header("Ocp-Apim-Subscription-Key", chatContext.getKeyInfo().getToken())
                                .headers(headers -> convertHeaders(passHeaders, headers))
                                .retrieve()
                                .bodyToMono(Object.class)
                                .doOnNext(x -> recordUsage(chatContext))
                                .publishOn(Schedulers.boundedElastic())
                                .doOnError(throwable -> handleWebClientError(throwable, keyInfo, chatContext))
                                .doOnSuccess(result -> handleApiResult(keyInfo, chatContext, true))
                );
    }


    public Mono<Object> searchImages(HashMap<String, Object> params, HashMap<String, Object> passHeaders, ChatContext chatContext) {
        var keyInfo = chatContext.getKeyInfo();
        return Mono.just(params)
                .doOnNext(b -> {
                    log.info("Bing 开始Search Image 请求 | 用户ID: {} | 模型: {} | 密钥: {}",
                            chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getKeyInfo().getToken());
                }).flatMap(key ->
                        httpWebClient.get().uri(chatContext.getKeyInfo().getDomain() + "/v7.0/images/search?" + StringUtils.mapToParams(params))
                                .header("Ocp-Apim-Subscription-Key", chatContext.getKeyInfo().getToken())
                                .headers(headers -> {
                                    convertHeaders(passHeaders, headers);
                                })
                                .retrieve()
                                .bodyToMono(Object.class)
                                .doOnNext(x -> recordUsage(chatContext))
                                .publishOn(Schedulers.boundedElastic())
                                .doOnError(throwable -> handleWebClientError(throwable, keyInfo, chatContext))
                                .doOnSuccess(result -> handleApiResult(keyInfo, chatContext, true))
                );
    }

    private static void convertHeaders(HashMap<String, Object> passHeaders, HttpHeaders headers) {
        // 传递 Accept 头部
        Optional.ofNullable(passHeaders.get("accept")).ifPresent(accept -> headers.add("Accept", accept.toString()));

        // 传递 X-MSEdge-ClientID
        Optional.ofNullable(passHeaders.get("x-msedge-clientid")).ifPresent(clientId -> headers.add("X-MSEdge-ClientID", clientId.toString()));

        // 传递 X-MSEdge-ClientIP
        Optional.ofNullable(passHeaders.get("x-msedge-clientip")).ifPresent(clientIp -> headers.add("X-MSEdge-ClientIP", clientIp.toString()));

        // 传递 X-Search-Location
        Optional.ofNullable(passHeaders.get("x-search-location")).ifPresent(searchLocation -> headers.add("X-Search-Location", searchLocation.toString()));

        // 传递其他可能需要的头部
        Optional.ofNullable(passHeaders.get("accept-language")).ifPresent(acceptLanguage -> headers.add("Accept-Language", acceptLanguage.toString()));
        Optional.ofNullable(passHeaders.get("user-agent")).ifPresent(userAgent -> headers.add("User-Agent", userAgent.toString()));
        Optional.ofNullable(passHeaders.get("pragma")).ifPresent(referer -> headers.add("Pragma", referer.toString()));
    }

}
