package ai.yourouter.search.remote;

import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.common.context.ChatContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URI;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/23 15:30
 */
@Slf4j
@Service
public class GoogleSearchRemoteService extends SearchRemoteService {

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    // 手动定义构造函数，调用父类构造函数
    public GoogleSearchRemoteService(KmgRemoteService kmgRemoteService,
                                     @Qualifier("httpWebClient") WebClient httpWebClient) {
        super(kmgRemoteService);
        this.httpWebClient = httpWebClient;
    }

    public Mono<String> customSearch(Map<String, String> allParams, ChatContext chatContext) {
        var key = chatContext.getKeyInfo();
        return Mono.just(allParams)
                .doOnNext(b -> {
                    log.info("customSearch | 用户ID: {} | 模型: {} | 密钥: {}", chatContext.getChatUserInfo().getCharactersId(), chatContext.apiModelName(), chatContext.getKeyInfo().getToken());
                })
                .flatMap(x -> httpWebClient.get()
                        .uri(uriBuilder -> {
                                    String baseUrl = chatContext.getKeyInfo().getDomain();
                                    UriComponentsBuilder builder = UriComponentsBuilder
                                            .fromUriString(baseUrl + "/customsearch/v1")
                                            .queryParam("key", chatContext.getKeyInfo().getToken())
                                            .queryParam("cx", "f2cd76abe64834fa5");
                                    allParams.forEach(builder::queryParam);
                                    URI uri = builder.build().toUri();
                                    log.info("GOOGLE请求URI: {}", uri);
                                    return uri;
                                }
                        )
                        .retrieve()
                        .bodyToMono(String.class)
                        .doOnNext(result -> recordUsage(chatContext))
                        .publishOn(Schedulers.boundedElastic())
                        .doOnError(throwable -> handleWebClientError(throwable, key, chatContext))
                        .doOnSuccess(result -> handleApiResult(key, chatContext, true))
                );
    }
}
