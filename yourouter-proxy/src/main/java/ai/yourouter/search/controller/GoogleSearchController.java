package ai.yourouter.search.controller;

import ai.yourouter.search.service.GoogleSearchService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;

@RestController
@RequestMapping("")
@RequiredArgsConstructor
public class GoogleSearchController {

    private final GoogleSearchService myHispreadSearchService;

    @GetMapping(value = "/customsearch/v1", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<String> search(@RequestParam Map<String, String> allParams) {
        return myHispreadSearchService.search(allParams).cast(String.class);
    }
}
