package ai.yourouter.search.controller;

import ai.yourouter.search.service.AzureService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class AzureController {

    private final AzureService azureService;

    @GetMapping("/v7.0/search")
    public Mono<Object> bingSearch(@RequestParam Map<String, Object> allParams, @RequestHeader Map<String, Object> allHeader) {
        return azureService.bingSearch(allParams, allHeader);
    }

    @GetMapping("/v7.0/images/search")
    public Mono<Object> bingSearchImage(@RequestParam Map<String, Object> allParams, @RequestHeader Map<String, Object> allHeader) {
        return azureService.bingSearchImage(allParams, allHeader);
    }
}
