package ai.yourouter.search.service;

import ai.yourouter.calculate.search.OrganizationSearchCalculatePrice;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.service.AbstractRemoteServiceTemplate;
import ai.yourouter.common.service.AsyncUsageStatisticsService;
import ai.yourouter.common.utils.SearchStatisticsConverter;
import ai.yourouter.search.remote.AzureBingSearchRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AzureService extends AbstractRemoteServiceTemplate {

    private final AzureBingSearchRemoteService azureBingSearchRemoteService;
    private final OrganizationSearchCalculatePrice organizationSearchCalculatePrice;
    private final AsyncUsageStatisticsService asyncUsageStatisticsService;

    public AzureService(RetryConfig retryConfig,
                       KmgRemoteService kmgRemoteService,
                       AzureBingSearchRemoteService azureBingSearchRemoteService,
                       OrganizationSearchCalculatePrice organizationSearchCalculatePrice,
                       AsyncUsageStatisticsService asyncUsageStatisticsService) {
        super(retryConfig, kmgRemoteService);
        this.azureBingSearchRemoteService = azureBingSearchRemoteService;
        this.organizationSearchCalculatePrice = organizationSearchCalculatePrice;
        this.asyncUsageStatisticsService = asyncUsageStatisticsService;
    }

    public Mono<Object> bingSearch(Map<String, Object> allParams, Map<String, Object> allHeader) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");

            Mono<Object> operation = getAvailableKey(chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return azureBingSearchRemoteService.search(new HashMap<>(allParams), convert(allHeader), chatContext);
                    });

            return executeWithRetry(operation, "azure search", chatContext);
        });
    }

    public Mono<Object> bingSearchImage(Map<String, Object> allParams, Map<String, Object> allHeader) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");

            Mono<Object> operation = getAvailableKey(chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return azureBingSearchRemoteService.searchImages(new HashMap<>(allParams), convert(allHeader), chatContext);
                    });

            return executeWithRetry(operation, "azure search", chatContext);
        });
    }

    private HashMap<String, Object> convert(Map<String, Object> allHeader) {
        Map<String, Object> lowerCaseMap = new HashMap<>();
        for (Map.Entry<String, Object> entry : allHeader.entrySet()) {
            lowerCaseMap.put(entry.getKey().toLowerCase(), entry.getValue());
        }
        return new HashMap<>(lowerCaseMap);
    }

    @Override
    protected void publishUsageStatistics(ChatContext chatContext) {
        try {
            organizationSearchCalculatePrice.organizationListen(SearchStatisticsConverter.convert(chatContext));
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布使用量统计详细错误信息", e);
        }
    }

    @Override
    protected void publishUsageStatisticsAsync(ChatContext chatContext) {
        // 使用智能选择方法，根据配置自动决定异步或同步处理
        asyncUsageStatisticsService.publishSearchUsageStatisticsIntelligent(
                chatContext,
                () -> publishUsageStatistics(chatContext)
        );
    }
}
