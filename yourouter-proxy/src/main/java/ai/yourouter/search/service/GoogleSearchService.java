package ai.yourouter.search.service;

import ai.yourouter.calculate.search.OrganizationSearchCalculatePrice;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.service.AbstractRemoteServiceTemplate;
import ai.yourouter.common.service.AsyncUsageStatisticsService;
import ai.yourouter.common.utils.SearchStatisticsConverter;
import ai.yourouter.search.remote.GoogleSearchRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Map;

@Slf4j
@Service
public class GoogleSearchService extends AbstractRemoteServiceTemplate {

    private final GoogleSearchRemoteService googleSearchRemoteService;
    private final OrganizationSearchCalculatePrice organizationSearchCalculatePrice;
    private final AsyncUsageStatisticsService asyncUsageStatisticsService;

    public GoogleSearchService(RetryConfig retryConfig,
                              KmgRemoteService kmgRemoteService,
                              GoogleSearchRemoteService googleSearchRemoteService,
                              OrganizationSearchCalculatePrice organizationSearchCalculatePrice,
                              AsyncUsageStatisticsService asyncUsageStatisticsService) {
        super(retryConfig, kmgRemoteService);
        this.googleSearchRemoteService = googleSearchRemoteService;
        this.organizationSearchCalculatePrice = organizationSearchCalculatePrice;
        this.asyncUsageStatisticsService = asyncUsageStatisticsService;
    }

    public Mono<String> search(Map<String, String> allParams) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = contextView.get("chatContext");

            Mono<String> operation = getAvailableKey(chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return googleSearchRemoteService.customSearch(allParams, chatContext);
                    });

            return executeWithRetry(operation, "google search", chatContext);
        });
    }

    @Override
    protected void publishUsageStatistics(ChatContext chatContext) {
        try {
            organizationSearchCalculatePrice.organizationListen(SearchStatisticsConverter.convert(chatContext));
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布使用量统计详细错误信息", e);
        }
    }

    @Override
    protected void publishUsageStatisticsAsync(ChatContext chatContext) {
        // 使用智能选择方法，根据配置自动决定异步或同步处理
        asyncUsageStatisticsService.publishSearchUsageStatisticsIntelligent(
                chatContext,
                () -> publishUsageStatistics(chatContext)
        );
    }
}
