package ai.yourouter.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据源配置
 * 确保JPA只使用PostgreSQL，排除ClickHouse
 */
@Configuration
public class DataSourceConfig {

    /**
     * PostgreSQL数据源属性
     */
    @Bean
    @Primary
    @ConfigurationProperties("spring.datasource")
    public DataSourceProperties postgresDataSourceProperties() {
        return new DataSourceProperties();
    }

    /**
     * PostgreSQL数据源
     */
    @Bean
    @Primary
    public DataSource postgresDataSource() {
        return postgresDataSourceProperties()
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * 自定义Hibernate属性，确保JPA只使用PostgreSQL
     */
    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer() {
        return (properties) -> {
            // 明确指定PostgreSQL方言
            properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
            // 禁用schema验证，避免访问ClickHouse
            properties.put("hibernate.hbm2ddl.auto", "none");
            properties.put("hibernate.temp.use_jdbc_metadata_defaults", "false");
        };
    }

    /**
     * PostgreSQL EntityManagerFactory
     * 明确指定扫描包路径，排除ClickHouse相关实体
     */
    @Bean
    @Primary
    public LocalContainerEntityManagerFactoryBean entityManagerFactory(
            EntityManagerFactoryBuilder builder,
            @Qualifier("postgresDataSource") DataSource dataSource) {
        
        Map<String, Object> properties = new HashMap<>();
        properties.put("hibernate.dialect", "org.hibernate.dialect.PostgreSQLDialect");
        properties.put("hibernate.hbm2ddl.auto", "update");
        
        return builder
                .dataSource(dataSource)
                .packages("ai.yourouter.jpa") // 只扫描PostgreSQL相关的包
                .persistenceUnit("postgresql")
                .properties(properties)
                .build();
    }

    /**
     * PostgreSQL事务管理器
     */
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(
            @Qualifier("entityManagerFactory") LocalContainerEntityManagerFactoryBean entityManagerFactory) {
        return new JpaTransactionManager(entityManagerFactory.getObject());
    }
}
