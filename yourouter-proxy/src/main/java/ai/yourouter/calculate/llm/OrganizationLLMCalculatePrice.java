package ai.yourouter.calculate.llm;


import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.utils.*;
import ai.yourouter.jpa.organization.statistics.bean.OrganizationLLMStatistics;
import ai.yourouter.jpa.organization.statistics.repository.OrganizationLLMStatisticsRepository;
import ai.yourouter.jpa.organization.transaction.failed.bean.FailedTransaction;
import ai.yourouter.jpa.organization.transaction.failed.repository.FailedTransactionRepository;
import ai.yourouter.jpa.system.model.log.bean.SystemModelRequestLog;
import ai.yourouter.jpa.system.model.log.repository.SystemModelRequestLogRepository;
import ai.yourouter.jpa.system.model.price.bean.SystemLLMPrice;
import ai.yourouter.jpa.system.model.price.repository.SystemLLMPriceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrganizationLLMCalculatePrice {

    private final ObjectMapper objectMapper;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationBalanceUtils organizationBalanceUtils;

    private final SystemLLMPriceRepository systemLLMPriceRepository;

    private final FailedTransactionRepository failedTransactionRepository;

    private final SystemModelRequestLogRepository systemModelRequestLogRepository;

    private final OrganizationLLMStatisticsRepository organizationLLMStatisticsRepository;




    @Transactional(rollbackOn = {Throwable.class})
    public void organizationListen(OrganizationLLMStatistics organizationLLMStatistics,String modelName) {
        try{
            if (!(organizationLLMStatistics.getReasoningCompletion() == 0 &&
                    organizationLLMStatistics.getTextCompletion() == 0 &&
                    organizationLLMStatistics.getAudioCompletion() == 0 &&
                    organizationLLMStatistics.getImageCompletion() == 0)) {
                SystemLLMPrice systemLLMPrice = systemLLMPriceRepository.findSystemLLMPriceBySystemModelIdAndStatuses(organizationLLMStatistics.getSystemModelId(), PermissionStatusEnum.AVAILABLE.getCode());
                if (!Objects.isNull(systemLLMPrice)) {
                    //这里计费
                    long nanosUSD = calculateQuota(organizationLLMStatistics, systemLLMPrice);
                    BigDecimal quota = BillingUtils.nanosToUSD(nanosUSD);
                    organizationLLMStatistics.setQuota(quota);
                    organizationLLMStatistics.setStatuses(PermissionStatusEnum.UNAVAILABLE.getCode());
                    organizationLLMStatistics.setId(snowflakeIdGenerator.nextId());
                    log.info("organizationLLMStatistics:{}", JsonUtils.toJSONString(organizationLLMStatistics));
                    organizationLLMStatisticsRepository.saveAndFlush(organizationLLMStatistics);
                    organizationBalanceUtils.deductBalance(organizationLLMStatistics.getOrganizationId(),nanosUSD);
                }
            }

            systemModelRequestLogRepository.save(new SystemModelRequestLog(snowflakeIdGenerator.nextId(),modelName,organizationLLMStatistics.getSystemVendor(), ThroughputUtils.tokensPerSecond(organizationLLMStatistics.getRequestTime(),organizationLLMStatistics.getResponseTime(),organizationLLMStatistics.getTextCompletion()), SystemClock.now()));
        }catch (Exception e) {
            log.error("Failed to process organizationListen for organizationId: {}", organizationLLMStatistics.getOrganizationId(), e);
            // 可选择记录到数据库，或其他地方以便后续恢复
            recordFailedTransaction(organizationLLMStatistics, e);
            throw e; // 重新抛出异常，保证事务回滚
        }
    }

    private Long calculateQuota(OrganizationLLMStatistics statistics, SystemLLMPrice price) {

        BigDecimal textPrompt = partCostNanos(statistics.getTextPrompt(),price.getTextPrompt());
        BigDecimal textCachePrompt = partCostNanos(statistics.getTextCachePrompt(),price.getTextCachePrompt());
        BigDecimal textCachePromptWrite1H =partCostNanos(statistics.getTextCachePromptWrite1H(),price.getTextCachePromptWrite1H());
        BigDecimal textCachePromptWrite5M =partCostNanos(statistics.getTextCachePromptWrite5M(),price.getTextCachePromptWrite5M());
        BigDecimal textCompletion = partCostNanos(statistics.getTextCompletion(),price.getTextCompletion());
        BigDecimal audioPrompt = partCostNanos(statistics.getAudioPrompt(),price.getAudioPrompt());
        BigDecimal audioCachePrompt = partCostNanos(statistics.getAudioCachePrompt(),price.getAudioCachePrompt());
        BigDecimal audioCompletion = partCostNanos(statistics.getAudioCompletion(),price.getAudioCompletion());
        BigDecimal reasoningCompletion = partCostNanos(statistics.getReasoningCompletion(),price.getReasoningCompletion());
        BigDecimal imagePrompt = partCostNanos(statistics.getImagePrompt(),price.getImagePrompt());
        BigDecimal imageCachePrompt = partCostNanos(statistics.getImageCachePrompt(),price.getImageCachePrompt());
        BigDecimal imageCompletion = partCostNanos(statistics.getImageCompletion(),price.getImageCompletion());
        BigDecimal searchTool = partToolCostNanos(statistics.getSearchTool(),price.getSearchTool());
        return textPrompt
                .add(textCachePrompt)
                .add(textCachePromptWrite1H)
                .add(textCachePromptWrite5M)
                .add(textCompletion)
                .add(audioPrompt)
                .add(audioCachePrompt)
                .add(audioCompletion)
                .add(reasoningCompletion)
                .add(imagePrompt)
                .add(imageCachePrompt)
                .add(imageCompletion)
                .add(searchTool).longValueExact();
    }


    /** 计算某一部分 token 的 nano-dollar 费用 */
    private static BigDecimal partCostNanos(Long tokens, BigDecimal priceUSDperM) {
        if (tokens == null || tokens == 0 || priceUSDperM == null || priceUSDperM.signum() == 0) return BigDecimal.ZERO;

        // 单价 USD → nano-dollar
        BigDecimal nanoPricePerM = priceUSDperM.multiply(BigDecimal.valueOf(SystemConstant.NANOS_PER_DOLLAR));

        // (token × nanoPricePerM) / 1_000_000  ⇒ nano-dollar
        return nanoPricePerM
                .multiply(BigDecimal.valueOf(tokens))
                .divide(BigDecimal.valueOf(SystemConstant.TOKENS_PER_MILLION), 0, RoundingMode.HALF_UP);
    }


    private static BigDecimal partToolCostNanos(Long tokens, BigDecimal priceUSDperM) {
        if (tokens == 0 || priceUSDperM.signum() == 0) return BigDecimal.ZERO;

        // 单价 USD → nano-dollar
        BigDecimal nanoPricePerM = priceUSDperM.multiply(BigDecimal.valueOf(SystemConstant.NANOS_PER_DOLLAR));

        // (token × nanoPricePerM) / 1_000_000  ⇒ nano-dollar
        return nanoPricePerM
                .multiply(BigDecimal.valueOf(tokens))
                .divide(BigDecimal.valueOf(SystemConstant.CALL_PER_KILO), 0, RoundingMode.HALF_UP);
    }

    // 记录失败交易的辅助方法
    @SneakyThrows
    private void recordFailedTransaction(OrganizationLLMStatistics organizationLLMStatistics, Exception e) {
        // 这里可以将相关信息记录到一个专门用于记录失败交易的表中
        FailedTransaction failedTransaction = new FailedTransaction();
        failedTransaction.setId(snowflakeIdGenerator.nextId());
        failedTransaction.setOrganizationId(organizationLLMStatistics.getOrganizationId());
        failedTransaction.setMessages(objectMapper.writeValueAsString(organizationLLMStatistics));
        failedTransaction.setErrorMessage(e.getMessage());
        failedTransaction.setCreateTime(organizationLLMStatistics.getCreateTime());
        failedTransactionRepository.saveAndFlush(failedTransaction);
    }

}
