package ai.yourouter.calculate.model;


import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import ai.yourouter.jpa.system.model.log.repository.SystemModelRequestLogRepository;
import ai.yourouter.jpa.system.model.price.repository.SystemLLMPriceRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SystemModelRequestLogCalculate {

    private final ObjectMapper objectMapper;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final RedisTemplate<String, String> redisTemplate;

    private final SystemLLMPriceRepository systemLLMPriceRepository;

    private final SystemModelInfoRepository systemModelInfoRepository;

    private final SystemModelRequestLogRepository systemModelRequestLogRepository;



    public void saveSystemModelRequestLogCalculate(String modelName,String systemVendor,Double tps) {



    }
}
