package ai.yourouter.common.service;

import ai.yourouter.calculate.llm.OrganizationLLMCalculatePrice;
import ai.yourouter.calculate.search.OrganizationSearchCalculatePrice;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.config.UsageStatisticsConfig;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.utils.CharacterLLMStatisticsConverter;
import ai.yourouter.common.utils.SearchStatisticsConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 异步 Usage 统计服务
 * 负责异步处理 LLM 使用量统计和计费
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncUsageStatisticsService {

    private final OrganizationLLMCalculatePrice organizationLLMCalculatePrice;
    private final OrganizationSearchCalculatePrice organizationSearchCalculatePrice;
    private final UsageStatisticsConfig usageStatisticsConfig;

    /**
     * 异步发布 LLM 使用量统计
     * 
     * @param chatContext 聊天上下文
     * @return CompletableFuture<Void>
     */
    @Async("usageStatisticsExecutor")
    public CompletableFuture<Void> publishLLMUsageStatisticsAsync(ChatContext chatContext) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                if (usageStatisticsConfig.isDebugLoggingEnabled()) {
                    log.debug("开始异步处理 LLM 使用量统计 | requestId: {} | modelName: {}",
                            chatContext.getRequestId(), chatContext.apiModelName());
                }

                // 执行统计计算和存储
                organizationLLMCalculatePrice.organizationListen(
                        CharacterLLMStatisticsConverter.convert(chatContext),
                        chatContext.apiModelName()
                );

                // 记录成功日志
                ChatLogUtils.logUsageStatistics(chatContext, true);

                // 记录成功指标
                long processingTime = System.currentTimeMillis() - startTime;

                if (usageStatisticsConfig.isDebugLoggingEnabled()) {
                    log.debug("LLM 使用量统计异步处理完成 | requestId: {} | modelName: {} | 耗时: {}ms",
                            chatContext.getRequestId(), chatContext.apiModelName(), processingTime);
                }

            } catch (Exception e) {
                // 记录失败日志
                ChatLogUtils.logUsageStatistics(chatContext, false);

                log.error("LLM 使用量统计异步处理失败 | requestId: {} | modelName: {} | 错误信息: {}",
                        chatContext.getRequestId(), chatContext.apiModelName(), e.getMessage(), e);

                // 这里可以添加重试逻辑或者将失败的统计任务发送到死信队列
                handleStatisticsFailure(chatContext, e);
            }
        });
    }

    /**
     * 处理统计失败的情况
     * 可以实现重试逻辑或者记录到失败队列
     * 
     * @param chatContext 聊天上下文
     * @param exception 异常信息
     */
    private void handleStatisticsFailure(ChatContext chatContext, Exception exception) {
        // TODO: 可以在这里实现以下逻辑：
        // 1. 将失败的统计任务写入 Redis 队列，稍后重试
        // 2. 发送告警通知
        // 3. 记录到专门的失败日志文件
        
        log.warn("Usage 统计失败处理 | requestId: {} | 将来可以实现重试机制", 
                chatContext.getRequestId());
    }

    /**
     * 同步发布 LLM 使用量统计（保留原有逻辑作为备用）
     * 
     * @param chatContext 聊天上下文
     */
    public void publishLLMUsageStatisticsSync(ChatContext chatContext) {
        try {
            organizationLLMCalculatePrice.organizationListen(
                    CharacterLLMStatisticsConverter.convert(chatContext),
                    chatContext.apiModelName()
            );
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布 LLM 使用量统计详细错误信息", e);
        }
    }

    /**
     * 异步发布搜索使用量统计
     *
     * @param chatContext 聊天上下文
     * @return CompletableFuture<Void>
     */
    @Async("usageStatisticsExecutor")
    public CompletableFuture<Void> publishSearchUsageStatisticsAsync(ChatContext chatContext) {
        return CompletableFuture.runAsync(() -> {
            long startTime = System.currentTimeMillis();
            try {
                if (usageStatisticsConfig.isDebugLoggingEnabled()) {
                    log.debug("开始异步处理搜索使用量统计 | requestId: {} | modelName: {}",
                            chatContext.getRequestId(), chatContext.apiModelName());
                }

                // 执行搜索统计计算和存储
                organizationSearchCalculatePrice.organizationListen(
                        SearchStatisticsConverter.convert(chatContext)
                );

                // 记录成功日志
                ChatLogUtils.logUsageStatistics(chatContext, true);

                // 记录成功指标
                long processingTime = System.currentTimeMillis() - startTime;

                if (usageStatisticsConfig.isDebugLoggingEnabled()) {
                    log.debug("搜索使用量统计异步处理完成 | requestId: {} | modelName: {} | 耗时: {}ms",
                            chatContext.getRequestId(), chatContext.apiModelName(), processingTime);
                }

            } catch (Exception e) {
                // 记录失败日志
                ChatLogUtils.logUsageStatistics(chatContext, false);

                log.error("搜索使用量统计异步处理失败 | requestId: {} | modelName: {} | 错误信息: {}",
                        chatContext.getRequestId(), chatContext.apiModelName(), e.getMessage(), e);

                // 这里可以添加重试逻辑或者将失败的统计任务发送到死信队列
                handleStatisticsFailure(chatContext, e);
            }
        });
    }

    /**
     * 同步发布搜索使用量统计（保留原有逻辑作为备用）
     *
     * @param chatContext 聊天上下文
     */
    public void publishSearchUsageStatisticsSync(ChatContext chatContext) {
        try {
            organizationSearchCalculatePrice.organizationListen(SearchStatisticsConverter.convert(chatContext));
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布搜索使用量统计详细错误信息", e);
        }
    }


    /**
     * 智能选择统计处理方式
     * 根据配置决定使用异步还是同步处理
     *
     * @param chatContext 聊天上下文
     * @param llmProcessor LLM 同步处理器
     */
    public void publishLLMUsageStatisticsIntelligent(ChatContext chatContext, Runnable llmProcessor) {
        if (usageStatisticsConfig.isAsyncEnabled()) {
            try {
                var future = publishLLMUsageStatisticsAsync(chatContext);

            } catch (Exception e) {
                if (usageStatisticsConfig.isFallbackToSyncOnFailure()) {
                    log.warn("异步 LLM Usage 统计提交失败，降级到同步处理 | requestId: {} | 错误: {}",
                            chatContext.getRequestId(), e.getMessage());
                    llmProcessor.run();
                } else {
                    log.error("异步 LLM Usage 统计提交失败且未启用降级 | requestId: {} | 错误: {}",
                            chatContext.getRequestId(), e.getMessage());
                }
            }
        } else {
            llmProcessor.run();
        }
    }

    /**
     * 智能选择搜索统计处理方式
     * 根据配置决定使用异步还是同步处理
     *
     * @param chatContext 聊天上下文
     * @param searchProcessor 搜索同步处理器
     */
    public void publishSearchUsageStatisticsIntelligent(ChatContext chatContext, Runnable searchProcessor) {
        if (usageStatisticsConfig.isAsyncEnabled()) {
            try {
                var future = publishSearchUsageStatisticsAsync(chatContext);
            } catch (Exception e) {
                if (usageStatisticsConfig.isFallbackToSyncOnFailure()) {
                    log.warn("异步搜索 Usage 统计提交失败，降级到同步处理 | requestId: {} | 错误: {}",
                            chatContext.getRequestId(), e.getMessage());
                    searchProcessor.run();
                } else {
                    log.error("异步搜索 Usage 统计提交失败且未启用降级 | requestId: {} | 错误: {}",
                            chatContext.getRequestId(), e.getMessage());
                }
            }
        } else {
            searchProcessor.run();
        }
    }
}
