package ai.yourouter.common.exception.error;

public class ErrorConst {

    public static final OpenAIError SERVICE_UNAVAILABLE = OpenAIError.serviceUnavailable();

    public static final OpenAIError RATE_LIMIT_EXCEEDED = new OpenAIError(OpenAIError.ErrorDetails.builder()
            .type("rate_limit_error")
            .message("Rate Limit Exceeded")
            .code("rate_limit_exceeded")
            .build());

    public static final OpenAIError INVALID_REQUEST = new OpenAIError(OpenAIError.ErrorDetails.builder()
            .type("invalid_request_error")
            .message("Invalid Request")
            .code("invalid_request")
            .build());

    public static final OpenAIError ACCOUNT_DEACTIVATED = new OpenAIError(OpenAIError.ErrorDetails.builder()
            .type("invalid_request_error")
            .message("Account Deactivated")
            .code("account_deactivated")
            .build());

    public static final OpenAIError API_KEY_INVALID = new OpenAIError(OpenAIError.ErrorDetails.builder()
            .type("invalid_request_error")
            .message("API Key Invalid")
            .code("api_key_invalid")
            .build());

    public static final OpenAIError InsufficientFunds = new OpenAIError(OpenAIError.ErrorDetails.builder()
            .type("insufficient_quota")
            .message("You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://docs.myhispread.com/docs/guides/error-codes/api-errors.")
            .code("insufficient_quota")
            .build());


    public static final String InsufficientFundsString ="{\n" +
            "    \"error\": {\n" +
            "        \"message\": \"You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://docs.myhispread.com/docs/guides/error-codes/api-errors.\",\n" +
            "        \"type\": \"insufficient_quota\",\n" +
            "        \"param\": null,\n" +
            "        \"code\": \"insufficient_quota\"\n" +
            "    }\n" +
            "}";

}
