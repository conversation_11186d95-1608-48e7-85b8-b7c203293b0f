package ai.yourouter.common.exception.error;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
public class OpenAIError {

    private final ErrorDetails error;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ErrorDetails {
        private String message;
        private String type;
        private String param;
        private String code;
    }

    @Override
    public String toString() {
        return String.format("OpenAI Error: %s (Code: %s)",
                error.getMessage(), error.getCode());
    }

    public static OpenAIError serviceUnavailable() {
        return new OpenAIError(ErrorDetails.builder()
                .type("invalid_request_error")
                .message("Service Unavailable")
                .code("service_unavailable")
                .build()
        );
    }

    public static OpenAIError invalidRequest() {
        return new OpenAIError(ErrorDetails.builder()
                .type("invalid_request_error")
                .message("Invalid Request")
                .code("invalid_request")
                .build()
        );
    }
}