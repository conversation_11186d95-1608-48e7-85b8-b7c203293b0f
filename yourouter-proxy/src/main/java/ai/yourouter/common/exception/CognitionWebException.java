package ai.yourouter.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatus;

@Data
@EqualsAndHashCode(callSuper = true)
public class CognitionWebException extends RuntimeException {

    private HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;


    public CognitionWebException(HttpStatus status, String msg) {
        super(msg);
        this.status = status;
    }

    public CognitionWebException(HttpStatus status, String message, Throwable cause) {
        super(message, cause);
        this.status = status;
    }


    public CognitionWebException(String message) {
        super(message);
    }

    public CognitionWebException() {
    }

}
