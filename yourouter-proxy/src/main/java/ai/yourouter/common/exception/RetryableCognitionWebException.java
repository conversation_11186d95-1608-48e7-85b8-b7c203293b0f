package ai.yourouter.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatus;

/**
 * 可重试的认知Web异常
 * 继承自CognitionWebException，增加了重试相关的信息
 * 支持多种API提供商的错误处理
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RetryableCognitionWebException extends CognitionWebException {

    /**
     * 是否可以重试
     */
    private final boolean retryable;

    /**
     * 分析原因
     */
    private final String analysisReason;

    public RetryableCognitionWebException(HttpStatus status, String message,
                                          boolean retryable, String analysisReason) {
        super(status, message);
        this.retryable = retryable;
        this.analysisReason = analysisReason;
    }

    public RetryableCognitionWebException(HttpStatus status, String message,
                                          boolean retryable, String analysisReason, Throwable cause) {
        super(status, message, cause);
        this.retryable = retryable;
        this.analysisReason = analysisReason;
    }

    public RetryableCognitionWebException(String message, boolean retryable, String analysisReason) {
        super(message);
        this.retryable = retryable;
        this.analysisReason = analysisReason;
    }
}
