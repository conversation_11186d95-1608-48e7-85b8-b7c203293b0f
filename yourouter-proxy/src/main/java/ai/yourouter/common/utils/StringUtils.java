package ai.yourouter.common.utils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class StringUtils {

    public static boolean isBlank(String str) {
        int strLen;
        if (str == null || (strLen = str.length()) == 0) {
            return true;
        }
        for (int i = 0; i < strLen; i++) {
            if ((!Character.isWhitespace(str.charAt(i)))) {
                return false;
            }
        }
        return true;
    }

    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }

    public static String mapToQueryString(Map<String, Object> params) {
        StringBuilder queryString = new StringBuilder();
        Set<Map.Entry<String, Object>> entrySet = params.entrySet();

        for (Map.Entry<String, Object> entry : entrySet) {
            String key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8);
            String value = URLEncoder.encode(String.valueOf(entry.getValue()), StandardCharsets.UTF_8);
            if (!queryString.isEmpty()) {
                queryString.append("&");
            }
            queryString.append(key).append("=").append(value);
        }
        return queryString.toString();
    }

    /**
     * 将 Map<String, Object> 转换为 URL 参数字符串，不进行编码。
     *
     * @param params 要转换的参数 Map
     * @return 转换后的 URL 参数字符串
     */
    public static String mapToParams(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return ""; // 返回空字符串以防止空参数
        }

        return params.entrySet()
                .stream()
                .filter(entry -> entry.getKey() != null && entry.getValue() != null) // 忽略 null 键和值
                .map(entry -> entry.getKey() + "=" + entry.getValue())
                .collect(Collectors.joining("&")); // 使用 & 连接参数
    }

}
