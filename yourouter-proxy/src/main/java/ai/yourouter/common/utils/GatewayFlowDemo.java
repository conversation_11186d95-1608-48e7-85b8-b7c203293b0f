//package ai.yourouter.common.utils;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import ai.yourouter.common.constant.SystemConstant;
//
//import ai.yourouter.common.model.ModelMeta;
//import ai.yourouter.common.model.VendorModel;
//import ai.yourouter.jpa.channel.key.bean.ChannelKey;
//import org.springframework.data.domain.Range;
//import org.springframework.data.redis.connection.BitFieldSubCommands;
//import org.springframework.data.redis.connection.ExpirationOptions;
//import org.springframework.data.redis.connection.Limit;
//import org.springframework.data.redis.connection.zset.Aggregate;
//import org.springframework.data.redis.connection.zset.Weights;
//import org.springframework.data.redis.core.*;
//import org.springframework.data.redis.core.types.Expiration;
//import org.springframework.data.redis.core.types.Expirations;
//import org.springframework.http.HttpHeaders;
//import org.springframework.util.LinkedMultiValueMap;
//
//import java.time.Duration;
//import java.time.Instant;
//import java.util.Collection;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.TimeUnit;
//
//@SuppressWarnings("all")
//public class GatewayFlowDemo {
//
//    public static void main(String[] args) throws Exception {
//        /* ─────────────────────────────────────────────────────────────── */
//        /* 0.  准备假数据                                                    */
//        /* ─────────────────────────────────────────────────────────────── */
//        // ── 0.1 ObjectMapper
//        ObjectMapper om = new ObjectMapper();
//
//        // ── 0.2 假 Redis：用 Map 代替
//        Map<String, String> kv = new java.util.HashMap<>();
//        Map<String, String> hz = new java.util.HashMap<>();     // Hash(zKey+":value")
//
//        // system model = gpt-4o
//        String systemModelName = "gpt-4o";
//        VendorModel vmOpenAI = new VendorModel("openai", "gpt-4o","1");
//
//        VendorModel vmClaude = new VendorModel("claude", "claude-3-opus","2");
//
//        ModelMeta meta = new ModelMeta("1001", "gpt-4o",
//                List.of(vmOpenAI, vmClaude));
//
//        kv.put(SystemConstant.PROXY_MODEL_KEY + "gpt-4o",
//                om.writeValueAsString(meta));
//
//        // openai 延迟 20ms；claude 延迟 50ms
//        String zKey = SystemConstant.PROXY_MODEL_KEY + "openai" + systemModelName;
//        kv.put(zKey, "");  // zset 使用 score，这里仅演示
//        kv.put(zKey + ":value", "");  // hash key 前缀
//
//        // score + value
//        kv.put("score::" + zKey + "::" + "gpt-4o", "20");       // openai
//        kv.put("score::" + zKey.replace("openai", "claude")
//                + "::" + "claude-3-opus", "50"); // claude
//
//        ChannelKey ckOpenAI = new ChannelKey();
//        ckOpenAI.setId(1L);
//        ckOpenAI.setChannelKey("ck-openai");
//        ckOpenAI.setSecretKey("sec-OpenAI");
//        hz.put(zKey + ":value::gpt-4o", om.writeValueAsString(ckOpenAI));
//
//        /* ─────────────────────────────────────────────────────────────── */
//        /* 1.  构造工具类                                                   */
//        /* ─────────────────────────────────────────────────────────────── */
//        StubRedisTemplate redis = new StubRedisTemplate(kv, hz);
//        VendorPickerUtils picker = new VendorPickerUtils(redis, om);
//        GatewayHelperUtils gateway   = new GatewayHelperUtils(redis, om, picker);
//
//        /* ─────────────────────────────────────────────────────────────── */
//        /* 2.  假请求 JSON + Header                                         */
//        /* ─────────────────────────────────────────────────────────────── */
//        String reqJson = """
//            {
//              "model": "gpt-4o",
//              "messages": [
//                {"role":"user","content":"hi"}
//              ]
//            }""";
//        var reqNode = om.readTree(reqJson);
//
//        HttpHeaders incoming = new HttpHeaders(new LinkedMultiValueMap<>());
//        incoming.set("x-request-id", "abc-123");
//
//        /* ─────────────────────────────────────────────────────────────── */
//        /* 3.  挑 vendor & 组 header                                        */
//        /* ─────────────────────────────────────────────────────────────── */
//        var sel = gateway.pickVendor(reqNode, incoming);          // 自动挑 openai
//        System.out.println("picked -> " + sel.vendorModel().getVendorName());
//
//        HttpHeaders out = gateway.buildVendorHeaders(incoming, sel.channelKey());
//        System.out.println("outgoing Authorization = " + out.getFirst(HttpHeaders.AUTHORIZATION));
//
//        /* ─────────────────────────────────────────────────────────────── */
//        /* 4.  假下游返回体 → 注入 vendor/id                                 */
//        /* ─────────────────────────────────────────────────────────────── */
//        String rawBody = """
//            {
//              "id":"blah-origin-id",
//              "object":"chat.completion",
//              "choices":[{"index":0,"message":{"role":"assistant","content":"Hello"}}]
//            }""";
//
//        String resp = gateway.injectVendorAndId(rawBody,
//                sel.vendorModel().getVendorName(),
//                incoming.getFirst("x-request-id"));
//
//        System.out.println("\n=== response after inject ===");
//        System.out.println(resp);
//
//        /**
//         *
//         *
//         * picked -> openai
//         * Authorization -> Bearer ck-openai
//         *
//         * {"id":"your-abc-123","choices":[{"message":{"content":"hello"}}],"vendor":"openai"}
//         */
//    }
//
//    /* ---------------------------------------------------- */
//    /* ------------  VERY thin Redis stub ----------------- */
//    /* ---------------------------------------------------- */
//    static class StubRedisTemplate extends RedisTemplate<String,String> {
//        private final Map<String,String> kv;
//        private final Map<String,String> hz;
//
//        StubRedisTemplate(Map<String,String> kv, Map<String,String> hz){
//            this.kv = kv; this.hz = hz;
//        }
//
//        /* --- Value ops --- */
//        @Override
//        public ValueOperations<String, String> opsForValue() {
//            return new ValueOperations<>() {
//                @Override
//                public void set(String key, String value) {
//
//                }
//
//                @Override
//                public String setGet(String key, String value, long timeout, TimeUnit unit) {
//                    return "";
//                }
//
//                @Override
//                public String setGet(String key, String value, Duration duration) {
//                    return "";
//                }
//
//                @Override
//                public void set(String key, String value, long timeout, TimeUnit unit) {
//
//                }
//
//                @Override
//                public Boolean setIfAbsent(String key, String value) {
//                    return null;
//                }
//
//                @Override
//                public Boolean setIfAbsent(String key, String value, long timeout, TimeUnit unit) {
//                    return null;
//                }
//
//                @Override
//                public Boolean setIfPresent(String key, String value) {
//                    return null;
//                }
//
//                @Override
//                public Boolean setIfPresent(String key, String value, long timeout, TimeUnit unit) {
//                    return null;
//                }
//
//                @Override
//                public void multiSet(Map<? extends String, ? extends String> map) {
//
//                }
//
//                @Override
//                public Boolean multiSetIfAbsent(Map<? extends String, ? extends String> map) {
//                    return null;
//                }
//
//                @Override public String get(Object key){ return kv.get(key); }
//
//                @Override
//                public String getAndDelete(String key) {
//                    return "";
//                }
//
//                @Override
//                public String getAndExpire(String key, long timeout, TimeUnit unit) {
//                    return "";
//                }
//
//                @Override
//                public String getAndExpire(String key, Duration timeout) {
//                    return "";
//                }
//
//                @Override
//                public String getAndPersist(String key) {
//                    return "";
//                }
//
//                @Override
//                public String getAndSet(String key, String value) {
//                    return "";
//                }
//
//                @Override
//                public List<String> multiGet(Collection<String> keys) {
//                    return List.of();
//                }
//
//                @Override
//                public Long increment(String key) {
//                    return 0L;
//                }
//
//                @Override
//                public Long increment(String key, long delta) {
//                    return 0L;
//                }
//
//                @Override
//                public Double increment(String key, double delta) {
//                    return 0.0;
//                }
//
//                @Override
//                public Long decrement(String key) {
//                    return 0L;
//                }
//
//                @Override
//                public Long decrement(String key, long delta) {
//                    return 0L;
//                }
//
//                @Override
//                public Integer append(String key, String value) {
//                    return 0;
//                }
//
//                @Override
//                public String get(String key, long start, long end) {
//                    return "";
//                }
//
//                @Override
//                public void set(String key, String value, long offset) {
//
//                }
//
//                @Override
//                public Long size(String key) {
//                    return 0L;
//                }
//
//                @Override
//                public Boolean setBit(String key, long offset, boolean value) {
//                    return null;
//                }
//
//                @Override
//                public Boolean getBit(String key, long offset) {
//                    return null;
//                }
//
//                @Override
//                public List<Long> bitField(String key, BitFieldSubCommands subCommands) {
//                    return List.of();
//                }
//
//                @Override
//                public RedisOperations<String, String> getOperations() {
//                    return null;
//                }
//                /* 其它方法省略 */
//            };
//        }
//
//        /* --- ZSet ops --- */
//        @Override
//        public ZSetOperations<String, String> opsForZSet() {
//            return new ZSetOperations<>() {
//                @Override
//                public Boolean add(String key, String value, double score) {
//                    return null;
//                }
//
//                @Override
//                public Boolean addIfAbsent(String key, String value, double score) {
//                    return null;
//                }
//
//                @Override
//                public Long add(String key, Set<TypedTuple<String>> typedTuples) {
//                    return 0L;
//                }
//
//                @Override
//                public Long addIfAbsent(String key, Set<TypedTuple<String>> typedTuples) {
//                    return 0L;
//                }
//
//                @Override
//                public Long remove(String key, Object... values) {
//                    return 0L;
//                }
//
//                @Override
//                public Double incrementScore(String key, String value, double delta) {
//                    return 0.0;
//                }
//
//                @Override
//                public String randomMember(String key) {
//                    return "";
//                }
//
//                @Override
//                public Set<String> distinctRandomMembers(String key, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public List<String> randomMembers(String key, long count) {
//                    return List.of();
//                }
//
//                @Override
//                public TypedTuple<String> randomMemberWithScore(String key) {
//                    return null;
//                }
//
//                @Override
//                public Set<TypedTuple<String>> distinctRandomMembersWithScore(String key, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public List<TypedTuple<String>> randomMembersWithScore(String key, long count) {
//                    return List.of();
//                }
//
//                @Override
//                public Long rank(String key, Object o) {
//                    return 0L;
//                }
//
//                @Override
//                public Long reverseRank(String key, Object o) {
//                    return 0L;
//                }
//
//                @Override
//                public Set<String> range(String key, long start, long end) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> rangeWithScores(String key, long start, long end) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<String> rangeByScore(String key, double min, double max) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> rangeByScoreWithScores(String key, double min, double max) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<String> rangeByScore(String key, double min, double max, long offset, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> rangeByScoreWithScores(String key, double min, double max, long offset, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<String> reverseRange(String key, long start, long end) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> reverseRangeWithScores(String key, long start, long end) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<String> reverseRangeByScore(String key, double min, double max) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> reverseRangeByScoreWithScores(String key, double min, double max) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<String> reverseRangeByScore(String key, double min, double max, long offset, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> reverseRangeByScoreWithScores(String key, double min, double max, long offset, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public Long count(String key, double min, double max) {
//                    return 0L;
//                }
//
//                @Override
//                public Long lexCount(String key, Range<String> range) {
//                    return 0L;
//                }
//
//                @Override
//                public TypedTuple<String> popMin(String key) {
//                    return null;
//                }
//
//                @Override
//                public Set<TypedTuple<String>> popMin(String key, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public TypedTuple<String> popMin(String key, long timeout, TimeUnit unit) {
//                    return null;
//                }
//
//                @Override
//                public TypedTuple<String> popMax(String key) {
//                    return null;
//                }
//
//                @Override
//                public Set<TypedTuple<String>> popMax(String key, long count) {
//                    return Set.of();
//                }
//
//                @Override
//                public TypedTuple<String> popMax(String key, long timeout, TimeUnit unit) {
//                    return null;
//                }
//
//                @Override
//                public Long size(String key) {
//                    return 0L;
//                }
//
//                @Override
//                public Long zCard(String key) {
//                    return 0L;
//                }
//
//                @Override public Double score(String key, Object member){
//                    return kv.containsKey("score::"+key+"::"+member)
//                            ? Double.valueOf(kv.get("score::"+key+"::"+member))
//                            : null;
//                }
//
//                @Override
//                public List<Double> score(String key, Object... o) {
//                    return List.of();
//                }
//
//                @Override
//                public Long removeRange(String key, long start, long end) {
//                    return 0L;
//                }
//
//                @Override
//                public Long removeRangeByLex(String key, Range<String> range) {
//                    return 0L;
//                }
//
//                @Override
//                public Long removeRangeByScore(String key, double min, double max) {
//                    return 0L;
//                }
//
//                @Override
//                public Set<String> difference(String key, Collection<String> otherKeys) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> differenceWithScores(String key, Collection<String> otherKeys) {
//                    return Set.of();
//                }
//
//                @Override
//                public Long differenceAndStore(String key, Collection<String> otherKeys, String destKey) {
//                    return 0L;
//                }
//
//                @Override
//                public Set<String> intersect(String key, Collection<String> otherKeys) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> intersectWithScores(String key, Collection<String> otherKeys) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> intersectWithScores(String key, Collection<String> otherKeys, Aggregate aggregate, Weights weights) {
//                    return Set.of();
//                }
//
//                @Override
//                public Long intersectAndStore(String key, String otherKey, String destKey) {
//                    return 0L;
//                }
//
//                @Override
//                public Long intersectAndStore(String key, Collection<String> otherKeys, String destKey) {
//                    return 0L;
//                }
//
//                @Override
//                public Long intersectAndStore(String key, Collection<String> otherKeys, String destKey, Aggregate aggregate, Weights weights) {
//                    return 0L;
//                }
//
//                @Override
//                public Set<String> union(String key, Collection<String> otherKeys) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> unionWithScores(String key, Collection<String> otherKeys) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<TypedTuple<String>> unionWithScores(String key, Collection<String> otherKeys, Aggregate aggregate, Weights weights) {
//                    return Set.of();
//                }
//
//                @Override
//                public Long unionAndStore(String key, String otherKey, String destKey) {
//                    return 0L;
//                }
//
//                @Override
//                public Long unionAndStore(String key, Collection<String> otherKeys, String destKey) {
//                    return 0L;
//                }
//
//                @Override
//                public Long unionAndStore(String key, Collection<String> otherKeys, String destKey, Aggregate aggregate, Weights weights) {
//                    return 0L;
//                }
//
//                @Override
//                public Cursor<TypedTuple<String>> scan(String key, ScanOptions options) {
//                    return null;
//                }
//
//                @Override
//                public Set<String> rangeByLex(String key, Range<String> range, Limit limit) {
//                    return Set.of();
//                }
//
//                @Override
//                public Set<String> reverseRangeByLex(String key, Range<String> range, Limit limit) {
//                    return Set.of();
//                }
//
//                @Override
//                public Long rangeAndStoreByLex(String srcKey, String dstKey, Range<String> range, Limit limit) {
//                    return 0L;
//                }
//
//                @Override
//                public Long reverseRangeAndStoreByLex(String srcKey, String dstKey, Range<String> range, Limit limit) {
//                    return 0L;
//                }
//
//                @Override
//                public Long rangeAndStoreByScore(String srcKey, String dstKey, Range<? extends Number> range, Limit limit) {
//                    return 0L;
//                }
//
//                @Override
//                public Long reverseRangeAndStoreByScore(String srcKey, String dstKey, Range<? extends Number> range, Limit limit) {
//                    return 0L;
//                }
//
//                @Override
//                public RedisOperations<String, String> getOperations() {
//                    return null;
//                }
//                /* 其它方法省略 */
//            };
//        }
//
//        /* --- Hash ops --- */
//        @Override
//        public HashOperations<String, String, String> opsForHash() {
//            return new HashOperations<>() {
//                @Override
//                public Long delete(String key, Object... hashKeys) {
//                    return 0L;
//                }
//
//                @Override
//                public Boolean hasKey(String key, Object hashKey) {
//                    return null;
//                }
//
//                @Override public String get(String key, Object hashKey){
//                    return hz.get(key+"::"+hashKey);
//                }
//
//                @Override
//                public List<String> multiGet(String key, Collection<String> hashKeys) {
//                    return List.of();
//                }
//
//                @Override
//                public Long increment(String key, String hashKey, long delta) {
//                    return 0L;
//                }
//
//                @Override
//                public Double increment(String key, String hashKey, double delta) {
//                    return 0.0;
//                }
//
//                @Override
//                public String randomKey(String key) {
//                    return "";
//                }
//
//                @Override
//                public Map.Entry<String, String> randomEntry(String key) {
//                    return null;
//                }
//
//                @Override
//                public List<String> randomKeys(String key, long count) {
//                    return List.of();
//                }
//
//                @Override
//                public Map<String, String> randomEntries(String key, long count) {
//                    return Map.of();
//                }
//
//                @Override
//                public Set<String> keys(String key) {
//                    return Set.of();
//                }
//
//                @Override
//                public Long lengthOfValue(String key, String hashKey) {
//                    return 0L;
//                }
//
//                @Override
//                public Long size(String key) {
//                    return 0L;
//                }
//
//                @Override
//                public void putAll(String key, Map<? extends String, ? extends String> m) {
//
//                }
//
//                @Override
//                public void put(String key, String hashKey, String value) {
//
//                }
//
//                @Override
//                public Boolean putIfAbsent(String key, String hashKey, String value) {
//                    return null;
//                }
//
//                @Override
//                public List<String> values(String key) {
//                    return List.of();
//                }
//
//                @Override
//                public Map<String, String> entries(String key) {
//                    return Map.of();
//                }
//
//                @Override
//                public Cursor<Map.Entry<String, String>> scan(String key, ScanOptions options) {
//                    return null;
//                }
//
//                @Override
//                public ExpireChanges<String> expire(String key, Duration timeout, Collection<String> hashKeys) {
//                    return null;
//                }
//
//                @Override
//                public ExpireChanges<String> expireAt(String key, Instant expireAt, Collection<String> hashKeys) {
//                    return null;
//                }
//
//                @Override
//                public ExpireChanges<String> expire(String key, Expiration expiration, ExpirationOptions options, Collection<String> hashKeys) {
//                    return null;
//                }
//
//                @Override
//                public ExpireChanges<String> persist(String key, Collection<String> hashKeys) {
//                    return null;
//                }
//
//                @Override
//                public Expirations<String> getTimeToLive(String key, TimeUnit timeUnit, Collection<String> hashKeys) {
//                    return null;
//                }
//
//                @Override
//                public RedisOperations<String, ?> getOperations() {
//                    return null;
//                }
//                /* 其它方法省略 */
//            };
//        }
//    }
//}
