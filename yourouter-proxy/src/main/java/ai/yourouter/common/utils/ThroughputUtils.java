package ai.yourouter.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class ThroughputUtils {

    private ThroughputUtils() {
        // 工具类禁止实例化
    }

    /**
     * 计算吞吐量，返回 {@code double}，保留两位小数。
     *
     * @param requestTimeMs   请求开始时间（Epoch ms）
     * @param responseTimeMs  完成时间（Epoch ms）
     * @param completeTokens  已完成的 tokens 数
     * @return 平均每秒吞吐的 tokens，若耗时 <=0 或 tokens <=0 则返回 0
     */
    public static double tokensPerSecond(long requestTimeMs,
                                         long responseTimeMs,
                                         long completeTokens) {

        long durationMs = responseTimeMs - requestTimeMs;
        if (durationMs <= 0 || completeTokens <= 0) {
            return 0.0;
        }
        // 使用 BigDecimal 避免 double 舍入误差
        BigDecimal tokens   = BigDecimal.valueOf(completeTokens);
        BigDecimal seconds  = BigDecimal.valueOf(durationMs)
                .divide(BigDecimal.valueOf(1000), 6, RoundingMode.HALF_UP);
        return tokens.divide(seconds, 2, RoundingMode.HALF_UP).doubleValue();
    }
}
