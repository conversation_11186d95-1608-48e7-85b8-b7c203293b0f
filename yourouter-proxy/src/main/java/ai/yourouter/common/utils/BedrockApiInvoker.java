package ai.yourouter.common.utils;



import ai.yourouter.chat.channel.aws.req.BedrockRequest;
import ai.yourouter.chat.channel.aws.req.BedrockResponse;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;

public class BedrockApiInvoker {

//    public static void main(String[] args) {
//        String timestamp = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'"));
//        BedrockRequest request = new BedrockRequest(
//                "********************",
//                "QL6sdSbJIPB3DqXVqeo5+v/7Sb+qs/QNl8NfLyL7",
//                "us-west-2",
//                "bedrock",
//                "{\"top_p\":1,\"max_tokens\":100,\"top_k\":0,\"temperature\":0.8,\"messages\":[{\"role\":\"user\",\"content\":\"你的知识库截止时间是？\"}],\"anthropic_version\":\"bedrock-2023-05-31\"}",
//                timestamp,
//                UUID.randomUUID().toString()
//        );
//        String url = "https://bedrock-runtime." + request.getRegion() + ".amazonaws.com/model/anthropic.claude-3-sonnet-20240229-v1%3A0/invoke-with-response-stream";
//        BedrockResponse response = invokeBedrockApi(request, url);
//        System.out.println(response);
//    }

//    public static void main(String[] args) {
//        String input = "AWS4-HMAC-SHA256 Credential=your_access_key_id/20241203/global/bedrock/aws4_request, SignedHeaders=amz-sdk-invocation-id;amz-sdk-request;content-length;content-type;host;x-amz-content-sha256;x-amz-date, Signature=8cc1b5af5b1bc969e422fb7ab31f95bb579cf15c2f423e5ba829ee69a53b79cb";
//
//        // Regular expression to match the access key ID
//        String regex = "Credential=([^/]+)/";
//        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
//        java.util.regex.Matcher matcher = pattern.matcher(input);
//
//        if (matcher.find()) {
//            String accessKeyId = matcher.group(1); // Extract the first group
//            System.out.println("Access Key ID: " + accessKeyId);
//        } else {
//            System.out.println("Access Key ID not found.");
//        }
//    }

    public static String createSignatureKey(String secretKey, String body, String outerTimestamp, String invokeId, String url,
                                            String signedHeaders
    ) {
        var request = new BedrockRequest(
                secretKey,
                secretKey,
                "global",
                "bedrock",
                body,
                outerTimestamp,
                invokeId
        );

        String timestamp = request.getTimestamp();
        String date = timestamp.substring(0, 8);

        String payloadHash = hashString("SHA-256", request.getRequestBody());
        String invocationId = request.getInvocationId();

        var content_length = Integer.toString(request.getRequestBody().getBytes(StandardCharsets.UTF_8).length);
        String canonicalRequest = createCanonicalRequestWithDynamicSignedHeaders(url, timestamp, payloadHash, invocationId, content_length, Arrays.stream(signedHeaders.split(";")).toList());
        String stringToSign = createStringToSign(timestamp, request.getRegion(), request.getService(), canonicalRequest);
        String signature = calculateSignature(request.getSecretKey(), date, request.getRegion(), request.getService(), stringToSign);
        return createAuthorizationHeaderWithFixedHeader(request.getAccessKey(), date, request.getRegion(), request.getService(), signature, signedHeaders);
    }

    public static BedrockResponse invokeBedrockApi(BedrockRequest request, String url) {

        String timestamp = request.getTimestamp();
        String date = timestamp.substring(0, 8);

        String payloadHash = hashString("SHA-256", request.getRequestBody());
        String invocationId = request.getInvocationId();

        var content_length = Integer.toString(request.getRequestBody().getBytes(StandardCharsets.UTF_8).length);
        String canonicalRequest = createCanonicalRequest(url, timestamp, payloadHash, invocationId, content_length);
        String stringToSign = createStringToSign(timestamp, request.getRegion(), request.getService(), canonicalRequest);
        String signature = calculateSignature(request.getSecretKey(), date, request.getRegion(), request.getService(), stringToSign);

        String authorizationHeader = createAuthorizationHeader(request.getAccessKey(), date, request.getRegion(), request.getService(), signature);

        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", authorizationHeader);
        headers.put("X-Amz-Date", timestamp);
        headers.put("X-Amz-Content-Sha256", payloadHash);
        headers.put("Content-Type", "application/json");
        headers.put("Content-Length", content_length);
        headers.put("Host", URI.create(url).getHost());
        headers.put("Amz-Sdk-Invocation-Id", invocationId);
        headers.put("Amz-Sdk-Request", "attempt=1; max=4");

        // 这里应该添加实际的HTTP请求逻辑，但为了简化示例，我们只返回headers
        return new BedrockResponse(headers, "模拟的响应内容");
    }

    public static String createCanonicalRequest(String url, String timestamp, String payloadHash, String invocationId, String content_length) {
        URI uri = URI.create(url);
        String canonicalUri = urlEncodeIgnoreSlashes(uri.normalize().getRawPath());
        String canonicalQueryString = ""; // If there are query parameters, they need to be sorted and encoded
        String canonicalHeaders = "amz-sdk-invocation-id:" + invocationId + "\n" +
                "amz-sdk-request:attempt=1; max=4\n" +
                "content-length:" + content_length + "\n" +
                "content-type:application/json\n" +
                "host:" + uri.getHost() + "\n" +
                "x-amz-content-sha256:" + payloadHash + "\n" +
                "x-amz-date:" + timestamp + "\n";
        String signedHeaders = "amz-sdk-invocation-id;amz-sdk-request;content-length;content-type;host;x-amz-content-sha256;x-amz-date";

        return "POST\n" + canonicalUri + "\n" + canonicalQueryString + "\n" + canonicalHeaders + "\n" + signedHeaders + "\n" + payloadHash;
    }

    public static String createCanonicalRequestWithDynamicSignedHeaders(
            String url,
            String timestamp,
            String payloadHash,
            String invocationId,
            String content_length,
            List<String> signedHeadersList) {

        URI uri = URI.create(url);
        String canonicalUri = urlEncodeIgnoreSlashes(uri.normalize().getRawPath());
        String canonicalQueryString = ""; // 如果有查询参数，这里需要排序和编码

        // 构造canonicalHeaders，确保顺序与signedHeadersList一致
        Map<String, String> headersMap = new LinkedHashMap<>();
        headersMap.put("amz-sdk-invocation-id", invocationId);
        headersMap.put("amz-sdk-request", "attempt=1; max=4");
        headersMap.put("content-length", content_length);
        headersMap.put("content-type", "application/json");
        headersMap.put("host", uri.getHost());
        headersMap.put("x-amz-content-sha256", payloadHash);
        headersMap.put("x-amz-date", timestamp);

        // 过滤并构造 canonicalHeaders
        StringBuilder canonicalHeadersBuilder = new StringBuilder();
        for (String header : signedHeadersList) {
            String value = headersMap.get(header);
            if (value != null) {
                canonicalHeadersBuilder.append(header).append(":").append(value).append("\n");
            }
        }
        String canonicalHeaders = canonicalHeadersBuilder.toString();

        // 构造signedHeaders字符串
        String signedHeaders = String.join(";", signedHeadersList);

        return "POST\n" +
                canonicalUri + "\n" +
                canonicalQueryString + "\n" +
                canonicalHeaders + "\n" +
                signedHeaders + "\n" +
                payloadHash;
    }

    public static String urlEncodeIgnoreSlashes(String path) {
        StringBuilder result = new StringBuilder();
        for (char ch : path.toCharArray()) {
            if ((ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9') || ch == '_' || ch == '-' || ch == '~' || ch == '.') {
                result.append(ch);
            } else if (ch == '/') {
                result.append(ch);
            } else {
                result.append(byteToHexUTF8(ch));
            }
        }
        return result.toString();
    }

    private static String byteToHexUTF8(char c) {
        StringBuilder stringBuffer = new StringBuilder();
        try {
            byte[] bytes = String.valueOf(c).getBytes("UTF-8");
            for (byte b : bytes) {
                stringBuffer.append("%").append(String.format("%02X", b & 0xFF));
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return stringBuffer.toString();
    }

    public static String createStringToSign(String timestamp, String region, String service, String canonicalRequest) {
        String algorithm = "AWS4-HMAC-SHA256";
        String credentialScope = timestamp.substring(0, 8) + "/" + region + "/" + service + "/aws4_request";
        String hashedCanonicalRequest = hashString("SHA-256", canonicalRequest);

        return algorithm + "\n" + timestamp + "\n" + credentialScope + "\n" + hashedCanonicalRequest;
    }

    public static String calculateSignature(String secretKey, String date, String region, String service, String stringToSign) {
        try {
            byte[] kSecret = ("AWS4" + secretKey).getBytes(StandardCharsets.UTF_8);
            byte[] kDate = hmacSHA256(kSecret, date);
            byte[] kRegion = hmacSHA256(kDate, region);
            byte[] kService = hmacSHA256(kRegion, service);
            byte[] kSigning = hmacSHA256(kService, "aws4_request");

            return toHex(hmacSHA256(kSigning, stringToSign));
        } catch (Exception e) {
            throw new RuntimeException("Error calculating signature", e);
        }
    }

    public static String createAuthorizationHeader(String accessKey, String date, String region, String service, String signature) {
        String credentialScope = date + "/" + region + "/" + service + "/aws4_request";
        String signedHeaders = "amz-sdk-invocation-id;amz-sdk-request;content-length;content-type;host;x-amz-content-sha256;x-amz-date";

        return "AWS4-HMAC-SHA256 Credential=" + accessKey + "/" + credentialScope + ", SignedHeaders=" + signedHeaders + ", Signature=" + signature;
    }

    public static String createAuthorizationHeaderWithFixedHeader(String accessKey, String date, String region, String service, String signature, String signedHeaders) {
        String credentialScope = date + "/" + region + "/" + service + "/aws4_request";

        return "AWS4-HMAC-SHA256 Credential=" + accessKey + "/" + credentialScope + ", SignedHeaders=" + signedHeaders + ", Signature=" + signature;
    }

    // Helper functions

    public static String hashString(String type, String input) {
        try {
            MessageDigest md = MessageDigest.getInstance(type);
            byte[] bytes = md.digest(input.getBytes(StandardCharsets.UTF_8));
            return toHex(bytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Error hashing string", e);
        }
    }

    public static byte[] hmacSHA256(byte[] key, String data) throws NoSuchAlgorithmException, InvalidKeyException {
        String algorithm = "HmacSHA256";
        Mac mac = Mac.getInstance(algorithm);
        mac.init(new SecretKeySpec(key, algorithm));
        return mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
    }

    public static String toHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    public static String getAccessId(String input) {
        // Regular expression to match the access key ID
        String regex = "Credential=([^/]+)/";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String accessKeyId = matcher.group(1); // Extract the first group
            System.out.println("Access Key ID: " + accessKeyId);
            return accessKeyId;
        } else {
            System.out.println("Access Key ID not found.");
            throw new RuntimeException("Access Key ID not found.");
        }
    }

    public static String headers(String input) {
        // Regular expression to match the SignedHeaders value
        String regex = "SignedHeaders=([^,]+)";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
        java.util.regex.Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            String signedHeaders = matcher.group(1); // Extract the first group
            System.out.println("SignedHeaders: " + signedHeaders);
            return signedHeaders;
        } else {
            System.out.println("SignedHeaders not found.");
            throw new RuntimeException();
        }
    }


}
