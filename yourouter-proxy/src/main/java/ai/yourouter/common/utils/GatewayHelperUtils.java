package ai.yourouter.common.utils;

import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.model.ModelMeta;
import ai.yourouter.common.model.VendorModel;
import ai.yourouter.jpa.channel.key.bean.ChannelKey;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;

@Slf4j
@Component
@RequiredArgsConstructor
public class GatewayHelperUtils {

    private final RedisTemplate<String,String> redisTemplate;

    private final VendorPickerUtils vendorPickerUtils;


    /**
     * 允许 header 指定 vendor；否则自动挑选延迟最低的。
     */
    public VendorPickerUtils.SelectedVendor pickVendor(JsonNode req,
                                                       HttpHeaders hdr) throws Exception {

        String model = req.get("model").asText();
        String metaRaw = redisTemplate.opsForValue()
                .get(SystemConstant.PROXY_MODEL_KEY + model);
        if (metaRaw == null) {
            throw new IllegalStateException("Model meta not found: " + model);
        }

        ModelMeta meta = JsonUtils.parseObject(metaRaw, ModelMeta.class);

        /* ① header 显式指定 vendorName 时优先使用 */
        String designated = hdr.getFirst("vendor");          // 自定义 header：vendor
        if (StringUtils.hasText(designated)) {

            // 找到指定 vendor 对应的 VendorModel
            VendorModel vm = meta.getVendorModelList().stream()
                    .filter(v -> designated.equalsIgnoreCase(v.getVendorName()))
                    .findFirst()
                    .orElseThrow(() -> new IllegalStateException(
                            "Vendor '" + designated + "' not available for model " + model));

            // 延迟（score） 和 channelKey
            String zKey = SystemConstant.PROXY_MODEL_KEY
                    + vm.getVendorName()
                    + meta.getSystemModelName();

            double latency = redisTemplate.opsForZSet()
                    .score(zKey, vm.getAsModelName());

            ChannelKey ck = fetchChannelKey(zKey, vm.getAsModelName());

            return new VendorPickerUtils.SelectedVendor(vm, latency, ck);
        }

        /* ② 未指定 → 调 VendorPickerUtils 自动挑选 */
        return vendorPickerUtils.pickBestVendor(meta);
    }

    /** 读取 Hash(zKey+":value") 中序列化的 ChannelKey */
    private ChannelKey fetchChannelKey(String zKey, String member) throws Exception {
        String json = redisTemplate.<String,String>opsForHash()
                .get(zKey + ":value", member);

        if (!StringUtils.hasText(json)) {
            throw new IllegalStateException("channelKey JSON missing for " + member);
        }
        return JsonUtils.parseObject(json, ChannelKey.class);
    }


    /**
     * 复制所有客户端 Header → 移除旧鉴权 → 注入新的 Bearer 口令
     */
    public HttpHeaders buildVendorHeaders(HttpHeaders incoming,
                                          ChannelKey   ck) {

        HttpHeaders out = new HttpHeaders();

        /* 1. 透传全部 Header */
        if (!CollectionUtils.isEmpty(incoming)) {
            incoming.forEach((k, v) -> out.put(k, new ArrayList<>(v)));
        }

        /* 2. 移除客户端鉴权信息 */
        out.remove(HttpHeaders.AUTHORIZATION);
        out.remove("x-api-key");

        /* 3. 注入 vendor channelKey 作为 Bearer */
        if (ck != null && StringUtils.hasText(ck.getChannelKey())) {
            out.setBearerAuth(ck.getChannelKey());
        }
        return out;
    }


    /**
     * 在 JSON body 中：
     * <ul>
     *   <li>替换官方 id → 以我们自己的 requestId</li>
     *   <li>新增 vendor 字段</li>
     * </ul>
     * <p>若 body 不是 JSON 或解析失败，则退化为包裹结构。</p>
     */
    public static String injectVendorAndId(String body,
                                    String vendor,
                                    String requestId) {

        try {
            JsonNode node = JsonUtils.parseJson(body);
            if (node.isObject()) {
                ObjectNode obj = (ObjectNode) node;
                obj.put("id", "your-" + requestId);
                obj.put("vendor", vendor);
                return JsonUtils.toJSONString(obj);
            }
        } catch (Exception e) {
            /* ignore & fallback */
            log.debug("Body is not JSON, wrap manually, err={}", e.getMessage());
        }

        /* fallback：包装成新 JSON */
        return """
               {
                 "id"     : "%s",
                 "vendor" : "%s",
                 "data"   : %s
               }
               """.formatted(requestId, vendor, body);
    }


    /**
     * 对 Claude / OpenAI 流式返回的 `data: ...` 片段：
     * <ul>
     *   <li>过滤掉 "ping" / "[DONE]" 等非内容数据</li>
     *   <li>在有效 json 追加 vendor 字段</li>
     * </ul>
     *
     * @param payload  单行 "xxx"
     * @param vendor   当前使用的厂商名
     */
    public static String handleSseLine(String payload, String vendor,String requestId) {

        if ("[DONE]".equalsIgnoreCase(payload)) return payload;
        if (payload.toLowerCase().contains("ping"))   return null;
        if (payload.isBlank())                        return null;

        try {
            JsonNode node = JsonUtils.parseJson(payload);
            if (node.isObject()) {
                ((ObjectNode) node).put("id", "your-" + requestId);
                ((ObjectNode) node).put("vendor", vendor);
                return JsonUtils.toJSONString(node);
            }
        } catch (Exception ignore) { }

        return payload;
    }

    /** 请求体是否是 JSON */
    public static boolean isJson(MediaType mt){
        return mt != null && MediaType.APPLICATION_JSON.includes(mt);
    }


}
