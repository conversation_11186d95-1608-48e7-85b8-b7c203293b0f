package ai.yourouter.common.utils;

import ai.yourouter.common.context.ChatContext;
import ai.yourouter.jpa.organization.statistics.bean.OrganizationSearchStatistics;

import java.time.Instant;

public class SearchStatisticsConverter {


    public static OrganizationSearchStatistics convert(ChatContext chatContext) {
        var now = Instant.now().toEpochMilli();
        return OrganizationSearchStatistics.builder()
                .call(1L)
                .systemModelId(chatContext.getChatModelInfo().getSystemModelId())
                .organizationId(chatContext.getChatUserInfo().getCharactersId())
                .createTime(Instant.now().toEpochMilli())
                .requestTime(chatContext.getChatRequestStatistic().getStartRequestTime())
                .responseTime(now)
                .durationMs(now - chatContext.getChatRequestStatistic().getStartRequestTime())
                .systemVendor(chatContext.getKeyInfo().getChannel())
                .channelKeysId(chatContext.getKeyInfo().getId())
                .build();
    }
}
