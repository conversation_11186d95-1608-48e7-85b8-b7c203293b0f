package ai.yourouter.common.utils;

import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.RedisKey;
import ai.yourouter.jpa.organization.info.bean.OrganizationInfo;
import ai.yourouter.jpa.organization.info.repository.OrganizationInfoRepository;
import ai.yourouter.jpa.organization.log.bean.OrganizationRequestRecord;
import ai.yourouter.jpa.organization.log.repository.OrganizationRequestRecordRepository;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Iterator;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class IpKeyMonitorUtil {

    private static final int THRESHOLD_ORG_COUNT = 3; // 同一IP允许OrgId数量阈值


    private final RedisTemplate<String, String> redisTemplate;

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    private final OrganizationRequestRecordRepository organizationRequestRecordRepository;

    private final BalanceTransactionRepository  balanceTransactionRepository;

    private final OrganizationInfoRepository  organizationInfoRepository;



    /**
     * 记录IP与OrgId关系，每分钟为key
     */

    @Async
    @Transactional
    public void recordAndCheckAbnormal(OrganizationRequestRecord organizationRequestRecord) {
        String minuteKey = getMinuteKey(organizationRequestRecord.getIp());

        redisTemplate.opsForSet().add(minuteKey, String.valueOf(organizationRequestRecord.getOrganizationId()));
        redisTemplate.expire(minuteKey, 2, TimeUnit.MINUTES);

        Long orgCount = redisTemplate.opsForSet().size(minuteKey);

        organizationRequestRecordRepository.save(organizationRequestRecord);

        if (orgCount != null && orgCount > THRESHOLD_ORG_COUNT){
            //有问题的组织 简单一些 直接找出来
            // 获取所有异常组织ID
            Set<String> abnormalOrgIds = redisTemplate.opsForSet().members(minuteKey);
            Iterator<String> iterator = abnormalOrgIds.iterator();
            while (iterator.hasNext()) {
                String abnormalOrgId = iterator.next();
                Boolean isRecharge = balanceTransactionRepository.existsByOrganizationIdAndType(Long.valueOf(abnormalOrgId), BalanceTransaction.TransactionType.RECHARGE);
                if (!isRecharge) {
                    //未充值 需要处理掉 禁用
                    OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(Long.valueOf(abnormalOrgId));
                    organizationInfo.setStatuses(PermissionStatusEnum.DISABLED.getCode());
                    organizationInfoRepository.save(organizationInfo);
                    redisTemplate.opsForValue().set(RedisKey.ORGANIZATION_PERMISSION_STATUS.getKey(Long.valueOf(abnormalOrgId)), String.valueOf(PermissionStatusEnum.DISABLED.getCode()));
                }
            }
        }

    }

    private String getMinuteKey(String ip) {
        String time = LocalDateTime.now().format(FORMATTER);
        return "ip:" + ip + ":" + time;
    }

}
