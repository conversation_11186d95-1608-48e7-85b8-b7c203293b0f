package ai.yourouter.common.utils;

import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.model.ModelMeta;
import ai.yourouter.common.model.VendorModel;
import ai.yourouter.jpa.channel.key.bean.ChannelKey;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Set;

/**
 * 根据 Redis ZSET 分值(延迟) 选出在线且延迟最小的 vendor-model 同时解析出 ChannelKey JSON
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class VendorPickerUtils {

    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;


    /** 返回载体：vendor + latency + channelKey */
    public record SelectedVendor(VendorModel vendorModel,
                                 double      latencyMs,
                                 ChannelKey  channelKey) {}

    /** 自动选择延迟最低的 Vendor */
    public SelectedVendor pickBestVendor(ModelMeta meta) {

        SelectedVendor best = null;

        for (VendorModel vm : meta.getVendorModelList()) {

            String zKey = SystemConstant.PROXY_MODEL_KEY
                    + vm.getVendorName()
                    + meta.getSystemModelName();

            /* ① 取延迟 */
            Double latency = redisTemplate.opsForZSet()
                    .score(zKey, vm.getAsModelName());
            if (latency == null) continue;

            /* ② 取该 vendor 的最优 channelKey */
            ChannelKey ck = findFirstChannelKey(zKey, vm.getAsModelName());

            /* ③ 比较 */
            if (best == null || latency < best.latencyMs())
                best = new SelectedVendor(vm, latency, ck);
        }

        if (best == null)
            throw new IllegalStateException("No vendor currently serves model "
                    + meta.getSystemModelName());

        return best;
    }

    /** 读取 zset 中延迟最低成员，再从 “:value” HASH 反序列化 ChannelKey */
    private ChannelKey findFirstChannelKey(String zKey, String memberName) {

        Set<ZSetOperations.TypedTuple<String>> tuple =
                redisTemplate.opsForZSet().rangeWithScores(zKey, 0, 0);
        if (tuple == null || tuple.isEmpty())
            throw new IllegalStateException("No member in zset " + zKey);

        String json = redisTemplate.<String,String>opsForHash()
                .get(zKey + ":value", memberName);

        if (!StringUtils.hasText(json))
            throw new IllegalStateException("channelKey JSON missing for "
                    + memberName);

        try {
            return objectMapper.readValue(json, ChannelKey.class);
        } catch (Exception e) {
            throw new IllegalStateException("Bad channelKey JSON: " + json, e);
        }
    }
}
