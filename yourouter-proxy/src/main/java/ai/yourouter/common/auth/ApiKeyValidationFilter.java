package ai.yourouter.common.auth;

import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.RedisKey;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.constant.UnpaidStatus;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.ChatModelInfo;
import ai.yourouter.common.context.ChatUserInfo;
import ai.yourouter.common.utils.IpKeyMonitorUtil;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.TraceUtils;
import ai.yourouter.jpa.organization.log.bean.OrganizationRequestRecord;
import ai.yourouter.jpa.organization.secret.bean.OrganizationSecret;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.*;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * 处理 GET /v1/models（无 body）仍然鉴权；
 * 仅当 model 非空时才做模型权限校验，避免 null 引发 500。
 */
@Slf4j
@Component
@RequiredArgsConstructor
@Order(Ordered.HIGHEST_PRECEDENCE)
@SuppressWarnings({"NullableProblems"})
public class ApiKeyValidationFilter implements WebFilter {

    private static final String BEARER_PREFIX = "Bearer ";
    private static final String DASHBOARD_URL = "https://platform.yourouter.ai";

    private static final String ATTR_URI = "REQUEST_URI";
    private static final String ATTR_BODY = "REQUEST_BODY";
    private static final String ATTR_MODEL = "MODEL";
    private static final String ATTR_ORG_ID = "ORGANIZATION_ID";

    public static final String geminiPrefix = "/v1/projects/cognition/locations/us/publishers/google/models/";
    private static final Pattern MODELS_LIST_PATH = Pattern.compile("^/v\\d+/models/?$");

    private final ObjectMapper objectMapper;
    private final IpKeyMonitorUtil ipKeyMonitorUtil;
    private final RedisTemplate<String, String> redisTemplate;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
        ServerHttpRequest req = exchange.getRequest();
        ServerHttpResponse resp = exchange.getResponse();
        String uri = req.getURI().toString();
        String traceId = TraceUtils.traceId();
        resp.getHeaders().add("x-request-id", traceId);

        var chatContext = ChatContext.create(new HashMap<>(req.getHeaders().asSingleValueMap()), traceId);
        String path = req.getPath().value();
        // 添加请求路径到ChatContext中
        chatContext.getChatRequestStatistic().getRequestHeaders().put("request-path", path);
        exchange.getAttributes().put(ATTR_URI, uri);

        // 1) 无 body 的场景：例如 GET /v1/models
        if (!hasBody(req)) {
            // 尝试从 path 解析 model（大部分 GET 不会有）
            String model = resolveModelFromPathOrBody(req, path, null);

            // 列表接口不做模型权限校验；其他 GET 若能解析出 model 则做
            boolean enforceModelPermission = StringUtils.hasText(model) && !isModelsListPath(path);
            if (StringUtils.hasText(model)) {
                exchange.getAttributes().put(ATTR_MODEL, model);
                log.debug("Request model (no-body): {}", model);
            }

            return authenticate(exchange, model, enforceModelPermission, resp, chain, chatContext)
                    .contextWrite(ctx -> ctx.put("exchange", exchange).put("chatContext", chatContext))
                    .onErrorResume(e -> {
                        log.error("ApiKeyValidationFilter failed (no-body)", e);
                        return writeError(resp, HttpStatus.BAD_REQUEST, e.getMessage());
                    });
        }

        // 2) 有 body 的场景：POST/PUT/PATCH...
        return DataBufferUtils.join(req.getBody())
                .defaultIfEmpty(resp.bufferFactory().allocateBuffer(0))
                .doFinally(signalType -> {
                    // 确保在所有情况下都释放资源
                    log.debug("DataBuffer processing completed with signal: {}", signalType);
                })
                .flatMap(buf -> {
                    try {
                        byte[] bytes = new byte[buf.readableByteCount()];
                        buf.read(bytes);
                        String body = new String(bytes, StandardCharsets.UTF_8);
                        exchange.getAttributes().put(ATTR_BODY, body);

                        String model = resolveModelFromPathOrBody(req, path, body);
                        boolean enforceModelPermission = StringUtils.hasText(model) && !isModelsListPath(path);

                        if (StringUtils.hasText(model)) {
                            exchange.getAttributes().put(ATTR_MODEL, model);
                            log.debug("Request model: {}", model);
                        }

                        // 重放 body
                        Flux<DataBuffer> cachedBody = Flux.defer(() -> Mono.just(resp.bufferFactory().wrap(bytes)));
                        ServerHttpRequest wrapped = new ServerHttpRequestDecorator(req) {
                            @Override
                            public Flux<DataBuffer> getBody() {
                                return cachedBody;
                            }
                        };

                        return authenticate(exchange.mutate().request(wrapped).build(), model, enforceModelPermission, resp, chain, chatContext)
                                .contextWrite(ctx -> ctx.put("exchange", exchange).put("chatContext", chatContext));
                    } finally {
                        // 确保在所有情况下都释放 DataBuffer
                        DataBufferUtils.release(buf);
                    }
                })
                .onErrorResume(e -> {
                    log.error("ApiKeyValidationFilter failed (with-body):{}", e.getMessage());
                    return writeError(resp, HttpStatus.BAD_REQUEST, e.getMessage());
                });
    }

    private boolean isModelsListPath(String path) {
        return MODELS_LIST_PATH.matcher(path).matches();
    }

    private boolean hasBody(ServerHttpRequest req) {
        HttpMethod m = req.getMethod();
        if (m == null) return false;
        // GET/HEAD/OPTIONS 通常无 body；若客户端强行带了，也兼容头判断
        if (m == HttpMethod.GET || m == HttpMethod.HEAD || m == HttpMethod.OPTIONS) {
            return req.getHeaders().getContentLength() > 0
                    || req.getHeaders().containsKey(HttpHeaders.TRANSFER_ENCODING);
        }
        // 其他方法（POST/PUT/PATCH/DELETE）按头判断
        return req.getHeaders().getContentLength() > 0
                || req.getHeaders().containsKey(HttpHeaders.TRANSFER_ENCODING);
    }

    private String resolveModelFromPathOrBody(ServerHttpRequest req, String path, @Nullable String body) {
        if (path.startsWith("/v7.0")) {
            var tier = Optional.ofNullable(req.getHeaders().getFirst("x-bing-search-tier")).orElse("s2");
            return "bing-search-" + tier;
        } else if (path.startsWith("/customsearch/v1")) {
            return "google-search";
        } else if (path.startsWith(geminiPrefix)) {
            // /v1/.../models/{model}:generateContent
            String remaining = path.substring(geminiPrefix.length());
            int colonIndex = remaining.indexOf(':');
            return colonIndex > 0 ? remaining.substring(0, colonIndex) : null;
        } else {
            MediaType ct = req.getHeaders().getContentType();
            boolean isJson = (ct != null) && (MediaType.APPLICATION_JSON.includes(ct)
                    || MediaType.APPLICATION_JSON.isCompatibleWith(ct));
            if (isJson && StringUtils.hasText(body)) {
                try {
                    JsonNode root = objectMapper.readTree(body);
                    if (root.has("model") && root.get("model").isTextual()) {
                        return root.get("model").asText();
                    }
                } catch (Exception e) {
                    log.debug("Request body is not valid JSON", e);
                }
            }
        }
        return null;
    }

    @SneakyThrows
    private Mono<Void> authenticate(ServerWebExchange ex,
                                    @Nullable String model,
                                    boolean enforceModelPermission,
                                    ServerHttpResponse resp,
                                    WebFilterChain chain,
                                    ChatContext chatContext) {
        // 1) API Key 基本校验
        String apiKey = resolveApiKey(ex.getRequest().getHeaders());
        if (!StringUtils.hasText(apiKey)) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Missing or invalid API key. Please subscribe at " + DASHBOARD_URL + ".");
        }

        String json = redisTemplate.opsForValue().get(SystemConstant.PROXY_ORGANIZATION_KEY + apiKey);
        if (!StringUtils.hasText(json)) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Invalid API key. Please subscribe at " + DASHBOARD_URL + ".");
        }

        OrganizationSecret org;
        try {
            org = objectMapper.readValue(json, OrganizationSecret.class);
        } catch (Exception e) {
            log.error("Failed to deserialize OrganizationSecret", e);
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Invalid API key data. Please contact support.");
        }

        if (org == null || org.getStatuses() == PermissionStatusEnum.UNAVAILABLE.getCode()) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "API key disabled. Please subscribe at " + DASHBOARD_URL + ".");
        }

        String permission = redisTemplate.opsForValue().get(RedisKey.ORGANIZATION_PERMISSION_STATUS.getKey(org.getOrganizationId()));
        if (!String.valueOf(PermissionStatusEnum.AVAILABLE.getCode()).equals(permission)) {
            return writeError(resp, HttpStatus.UNAUTHORIZED, "Your organization has been banned. <NAME_EMAIL>.");
        }

        String unpaid = redisTemplate.opsForValue().get(RedisKey.ORGANIZATION_UNPAID_STATUS.getKey(org.getOrganizationId()));
        if (!String.valueOf(UnpaidStatus.UNAVAILABLE).equals(unpaid)) {
            return writeError(resp, HttpStatus.TOO_MANY_REQUESTS, "Insufficient balance. Please top up at " + DASHBOARD_URL + ".");
        }

        // 2) 仅当需要时做模型权限校验（避免 model=null 导致 500）
        String systemModelId = null;
        if (enforceModelPermission) {
            if (Boolean.FALSE.equals(redisTemplate.opsForSet().isMember(SystemConstant.PROXY_MODEL_LIST_KEY, model))) {
                return writeError(resp, HttpStatus.NOT_FOUND, "Permission denied for model '" + model + "'. Upgrade at " + DASHBOARD_URL + ".");
            }
            systemModelId = redisTemplate.opsForValue().get(SystemConstant.PROXY_MODEL_KEY + model);
            if (systemModelId == null) {
                return writeError(resp, HttpStatus.NOT_FOUND, "Permission denied for model '" + model + "'. Upgrade at " + DASHBOARD_URL + ".");
            }
        }

        // 3) 填充上下文、打标请求头
        chatContext.setChatUserInfo(new ChatUserInfo(org.getOrganizationId(), apiKey));
        if (enforceModelPermission && systemModelId != null) {
            chatContext.setChatModelInfo(new ChatModelInfo(Long.parseLong(systemModelId), model));
        }

        ServerHttpRequest.Builder reqBuilder = ex.getRequest()
                .mutate()
                .header("X-Organization-Id", String.valueOf(org.getOrganizationId()));
        if (enforceModelPermission && systemModelId != null) {
            reqBuilder.header("X-Model-Id", systemModelId);
        }
        ServerHttpRequest decorated = reqBuilder.build();

        ex.getAttributes().put(ATTR_ORG_ID, org.getOrganizationId());

        // 4) 记录请求来源并风控
        ServerHttpRequest req = ex.getRequest();
        String ip = getClientIp(req);
        String userAgent = req.getHeaders().getFirst("User-Agent");
        String cfConnectingIp = req.getHeaders().getFirst("CF-Connecting-IP");
        String cfIpCountry = req.getHeaders().getFirst("CF-IPCountry");
        String cfRay = req.getHeaders().getFirst("CF-Ray");

        OrganizationRequestRecord record = OrganizationRequestRecord.builder()
                .id(snowflakeIdGenerator.nextId())
                .ip(ip)
                .organizationId(org.getOrganizationId())
                .userAgent(userAgent)
                .cfConnectingIp(cfConnectingIp)
                .cfIpCountry(cfIpCountry)
                .cfRay(cfRay)
                .build();
        ipKeyMonitorUtil.recordAndCheckAbnormal(record);

        // 5) 继续过滤链
        return chain.filter(ex.mutate().request(decorated).build());
    }

    private static String resolveApiKey(HttpHeaders headers) {
        String auth = headers.getFirst(HttpHeaders.AUTHORIZATION);
        if (StringUtils.hasText(auth) && auth.startsWith(BEARER_PREFIX)) {
            return auth.substring(BEARER_PREFIX.length()).trim();
        }
        return headers.getFirst("x-api-key");
    }

    private Mono<Void> writeError(ServerHttpResponse resp, HttpStatus status, String message) {
        resp.setStatusCode(status);
        resp.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        return resp.writeWith(Mono.just(resp.bufferFactory().wrap(message.getBytes(StandardCharsets.UTF_8))));
    }

    private String getClientIp(ServerHttpRequest request) {
        String cfIp = request.getHeaders().getFirst("CF-Connecting-IP");
        if (cfIp != null) return cfIp;

        String forwardedIp = request.getHeaders().getFirst("X-Forwarded-For");
        if (forwardedIp != null) return forwardedIp.split(",")[0].trim();

        return request.getRemoteAddress() != null
                ? request.getRemoteAddress().getAddress().getHostAddress()
                : "unknown";
    }
}
