package ai.yourouter.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Usage 统计配置
 * 控制统计处理的行为
 */
@Data
@Component
@ConfigurationProperties(prefix = "yourouter.usage.statistics")
public class UsageStatisticsConfig {

    /**
     * 是否启用异步处理，默认启用
     */
    private boolean asyncEnabled = true;

    /**
     * 异步处理失败时是否降级到同步处理，默认启用
     */
    private boolean fallbackToSyncOnFailure = true;

    /**
     * 异步任务超时时间（秒），默认30秒
     */
    private int asyncTimeoutSeconds = 30;

    /**
     * 是否记录详细的调试日志，默认关闭
     */
    private boolean debugLoggingEnabled = false;
}
