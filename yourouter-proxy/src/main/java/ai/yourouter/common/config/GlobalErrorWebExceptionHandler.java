package ai.yourouter.common.config;

import ai.yourouter.common.exception.CognitionWebException;
import ai.yourouter.common.exception.error.ErrorConst;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.web.reactive.error.ErrorWebExceptionHandler;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
@Order(-2)
@SuppressWarnings("NullableProblems")
public class GlobalErrorWebExceptionHandler implements ErrorWebExceptionHandler {

    private final ObjectMapper objectMapper;

    public GlobalErrorWebExceptionHandler(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Mono<Void> handle(ServerWebExchange exchange, Throwable ex) {
        exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);

        if (ex instanceof CognitionWebException cogEx) {
            exchange.getResponse().setStatusCode(cogEx.getStatus());
            try {
                return exchange.getResponse().writeWith(Mono.just(exchange.getResponse().bufferFactory().wrap(cogEx.getMessage().getBytes())));
            } catch (Exception e) {
                return Mono.error(e);
            }
        } else {
            exchange.getResponse().setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
            try {
                byte[] data = objectMapper.writeValueAsBytes(ErrorConst.SERVICE_UNAVAILABLE);
                return exchange.getResponse().writeWith(
                        Mono.just(exchange.getResponse().bufferFactory().wrap(data))
                );
            } catch (Exception e) {
                return Mono.error(e);
            }
        }
    }
}