package ai.yourouter.common.remote;

import ai.yourouter.chat.channel.error.ErrorAnalyzer;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.exception.RetryableCognitionWebException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.WebClientResponseException;

/**
 * 远程服务基类
 * 提供通用的错误处理、API结果回调等功能
 * 被 LlmRemoteService 和 SearchRemoteService 继承
 */
@Slf4j
@RequiredArgsConstructor
public abstract class BaseRemoteService {

    protected final KmgRemoteService kmgRemoteService;
    
    @Autowired
    protected ErrorAnalyzer errorAnalyzer;

    /**
     * 处理API调用结果回调
     *
     * @param keyInfo     密钥信息
     * @param chatContext 聊天上下文
     * @param success     是否成功
     * @param needDeleted 是否需要删除密钥
     */
    protected void handleApiResult(BestKeyResponse keyInfo, ChatContext chatContext, boolean success, boolean needDeleted) {
        kmgRemoteService.result(keyInfo.getId().toString(), chatContext.apiModelName(), success, needDeleted)
                .subscribe(
                        result -> log.debug("API结果回调成功 | 密钥ID: {} | 模型: {} | 成功: {}",
                                keyInfo.getId(), chatContext.apiModelName(), success),
                        error -> log.warn("API结果回调失败 | 密钥ID: {} | 错误: {}",
                                keyInfo.getId(), error.getMessage())
                );
    }

    /**
     * 处理API调用结果回调（简化版本）
     *
     * @param keyInfo     密钥信息
     * @param chatContext 聊天上下文
     * @param success     是否成功
     */
    protected void handleApiResult(BestKeyResponse keyInfo, ChatContext chatContext, boolean success) {
        handleApiResult(keyInfo, chatContext, success, false);
    }

    /**
     * 处理需要删除密钥的API调用结果回调
     *
     * @param keyInfo     密钥信息
     * @param chatContext 聊天上下文
     */
    protected void handleApiResultNeedDelete(BestKeyResponse keyInfo, ChatContext chatContext) {
        handleApiResult(keyInfo, chatContext, false, true);
    }

    /**
     * 处理400错误，根据错误内容判断是否可以重试
     *
     * @param ex          WebClientResponseException
     * @param key         密钥信息
     * @param chatContext 聊天上下文
     * @throws RuntimeException 抛出相应的异常
     */
    protected void handle400Error(WebClientResponseException ex, BestKeyResponse key, ChatContext chatContext) {
        ErrorAnalyzer.ErrorAnalysisResult analysisResult = errorAnalyzer.analyze400Error(ex);

        log.warn("400错误分析 | 用户ID: {} | 模型: {} | 可重试: {} | 原因: {} | 响应: {}",
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                analysisResult.isRetryable(),
                analysisResult.getReason(),
                ex.getResponseBodyAsString());

        if (analysisResult.isRetryable()) {
            // 可重试的400错误，不调用失败回调，让重试机制处理
            handleApiResultNeedDelete(key, chatContext);
            throw new RetryableCognitionWebException(
                    "可重试的400错误: " + ex.getResponseBodyAsString(),
                    true,
                    analysisResult.getReason()
            );
        } else {
            throw new RetryableCognitionWebException(
                    HttpStatus.BAD_REQUEST,
                    ex.getResponseBodyAsString(),
                    false,
                    analysisResult.getReason()
            );
        }
    }

    /**
     * 通用的WebClientResponseException错误处理器
     * 集成了错误日志记录和错误处理逻辑
     *
     * @param throwable   异常
     * @param key         密钥信息
     * @param chatContext 聊天上下文
     */
    protected void handleWebClientError(Throwable throwable, BestKeyResponse key, ChatContext chatContext) {
        // 首先记录错误日志
        loggingError(throwable, chatContext);

        if (throwable instanceof WebClientResponseException ex) {
            if (ex.getStatusCode().value() == 400) {
                // 分析400错误并决定是否可以重试
                handle400Error(ex, key, chatContext);
            } else if (ex.getStatusCode().value() == 403 || ex.getStatusCode().value() == 401) {
                handleApiResultNeedDelete(key, chatContext);
            } else {
                handleApiResult(key, chatContext, false);
            }
        }
    }

    /**
     * 基础的错误日志记录方法
     * 提供通用的错误日志格式，子类可以重写以提供更详细的日志信息
     *
     * @param throwable   异常
     * @param chatContext 聊天上下文
     */
    protected void loggingError(Throwable throwable, ChatContext chatContext) {
        if (throwable instanceof WebClientResponseException ex) {
            log.error("请求错误 | 用户ID: {} | 模型: {} | 状态码: {} | 响应体: {} | URL: {}",
                    chatContext.getChatUserInfo().getCharactersId(),
                    chatContext.apiModelName(),
                    ex.getStatusCode(),
                    ex.getResponseBodyAsString(),
                    ex.getRequest() != null ? ex.getRequest().getURI() : null
            );
        } else {
            log.error("请求错误 | 用户ID: {} | 模型: {} | 错误信息: {}",
                    chatContext.getChatUserInfo().getCharactersId(),
                    chatContext.apiModelName(),
                    throwable.getMessage()
            );
        }
    }
}
