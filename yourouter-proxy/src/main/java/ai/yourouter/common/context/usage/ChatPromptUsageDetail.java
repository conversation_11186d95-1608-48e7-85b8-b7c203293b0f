package ai.yourouter.common.context.usage;

import ai.yourouter.chat.channel.response.gemini.GeminiUsageMetadata;
import ai.yourouter.chat.channel.response.openai.ChatCompletion;
import ai.yourouter.chat.channel.response.openai.ChatCompletionChunk;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;


@Data
@Slf4j
@NoArgsConstructor
public class ChatPromptUsageDetail {

    private Integer textTokens = 0;
    private Integer audioTokens = 0;
    private Integer imageTokens = 0;
    private Integer videoTokens = 0;
    private Integer cachedTextTokens = 0;
    private Integer cacheTextWriteTokens = 0;
    private Integer cachedAudioTokens = 0;
    private Integer cachedVideoTokens = 0;
    private Integer cachedImageTokens = 0;

    public static ChatPromptUsageDetail from(ChatCompletion.Usage usage, boolean ignoreCache) {
        ChatPromptUsageDetail chatPromptUsageDetail = new ChatPromptUsageDetail();

        if (usage == null) {
            return chatPromptUsageDetail;
        }

        Integer promptTokens = usage.getPromptTokens();
        Integer audioTokens = 0;
        Integer cachedTokens = 0;
        int imageTokens;
        int textTokens = 0;

        if (usage.getPromptTokensDetails() != null) {
            if (usage.getPromptTokensDetails().getAudioTokens() != null) {
                audioTokens = usage.getPromptTokensDetails().getAudioTokens();
                chatPromptUsageDetail.audioTokens = audioTokens;
            }

            if (usage.getPromptTokensDetails().getCachedTokens() != null) {
                cachedTokens = usage.getPromptTokensDetails().getCachedTokens();
                chatPromptUsageDetail.cachedTextTokens = cachedTokens;
            }
            if (usage.getPromptTokensDetails().getImageTokens() != null) {
                imageTokens = usage.getPromptTokensDetails().getImageTokens();
                chatPromptUsageDetail.imageTokens = imageTokens;
            }
            if (usage.getPromptTokensDetails().getTextTokens() != null) {
                textTokens = usage.getPromptTokensDetails().getTextTokens();
                chatPromptUsageDetail.textTokens = textTokens;
            }

        }

        if (textTokens == 0) {
            chatPromptUsageDetail.textTokens = promptTokens - audioTokens - cachedTokens;
        }

        if (ignoreCache) {
            chatPromptUsageDetail.cachedTextTokens = 0;
            chatPromptUsageDetail.cachedAudioTokens = 0;
            chatPromptUsageDetail.cachedImageTokens = 0;
        }

        return chatPromptUsageDetail;
    }

    public static ChatPromptUsageDetail from(ChatCompletionChunk.Usage usage, boolean ignoreCache) {
        ChatPromptUsageDetail chatPromptUsageDetail = new ChatPromptUsageDetail();

        if (usage == null) {
            return chatPromptUsageDetail;
        }

        // The Usage class in ChatCompletionChunk only has promptTokens, completionTokens and totalTokens
        // It doesn't have promptTokensDetails with audioTokens and cachedTokens
        Integer promptTokens = usage.getPromptTokens();

        if (promptTokens != null) {
            chatPromptUsageDetail.textTokens = promptTokens;
            // Since ChatCompletionChunk.Usage doesn't provide audio/cached tokens info,
            // we assume all tokens are text tokens
        }

        if (ignoreCache) {
            chatPromptUsageDetail.cachedTextTokens = 0;
            chatPromptUsageDetail.cachedAudioTokens = 0;
            chatPromptUsageDetail.cachedImageTokens = 0;
        }

        return chatPromptUsageDetail;
    }


    public ChatPromptUsageDetail(int textTokens) {
        this.textTokens = textTokens;
    }

    public static ChatPromptUsageDetail createImageUsageDetail(int textTokens, int imageTokens) {
        var promptDetails = new ChatPromptUsageDetail();
        promptDetails.textTokens = textTokens;
        promptDetails.imageTokens = imageTokens;
        return promptDetails;
    }

    public ChatPromptUsageDetail(int textTokens, int cachedTextTokens) {
        this.textTokens = textTokens;
        this.cachedTextTokens = cachedTextTokens;
    }

    public ChatPromptUsageDetail(int textTokens, int cachedTextTokens, int cacheTextWriteTokens) {
        this.textTokens = textTokens;
        this.cachedTextTokens = cachedTextTokens;
        this.cacheTextWriteTokens = cacheTextWriteTokens;
    }

    public ChatPromptUsageDetail(List<GeminiUsageMetadata.ModalityTokenCount> promptTokensDetails, List<GeminiUsageMetadata.ModalityTokenCount> cacheTokensDetails) {
        for (GeminiUsageMetadata.ModalityTokenCount tokenCount : promptTokensDetails) {
            switch (tokenCount.getModality()) {
                case "TEXT" -> textTokens = tokenCount.getTokenCount();
                case "AUDIO" -> audioTokens = tokenCount.getTokenCount();
                case "IMAGE" -> imageTokens = tokenCount.getTokenCount();
                case "VIDEO" -> videoTokens = tokenCount.getTokenCount();
                default -> log.warn("Unknown prompt modality: {}", tokenCount.getModality());
            }
        }
        for (GeminiUsageMetadata.ModalityTokenCount tokenCount : cacheTokensDetails) {
            switch (tokenCount.getModality()) {
                case "TEXT" -> cachedTextTokens = tokenCount.getTokenCount();
                case "AUDIO" -> cachedAudioTokens = tokenCount.getTokenCount();
                case "IMAGE" -> cachedImageTokens = tokenCount.getTokenCount();
                case "VIDEO" -> cachedVideoTokens = tokenCount.getTokenCount();
                default -> log.warn("Unknown cache modality: {}", tokenCount.getModality());
            }
        }
    }

}
