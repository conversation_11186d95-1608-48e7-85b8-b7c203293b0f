package ai.yourouter.common.context;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatModelInfo {

    private Long systemModelId;

    private String rawModelName;


    public String realModelName() {
        return this.rawModelName;
    }

    public String getModelName() {
        return this.rawModelName;
    }

    public boolean onXModel() {
        return rawModelName.contains("grok");
    }

    public boolean onClaude() {
        return rawModelName.contains("claude");
    }

    public boolean onGemini() {
        return rawModelName.contains("gemini");
    }
}
