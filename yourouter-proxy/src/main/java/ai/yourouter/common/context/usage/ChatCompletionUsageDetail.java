package ai.yourouter.common.context.usage;

import ai.yourouter.chat.channel.response.gemini.GeminiUsageMetadata;
import ai.yourouter.chat.channel.response.openai.ChatCompletion;
import ai.yourouter.chat.channel.response.openai.ChatCompletionChunk;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Data
@Slf4j
@NoArgsConstructor
public class ChatCompletionUsageDetail {

    private Integer textTokens = 0;
    private Integer audioTokens = 0;
    private Integer imageTokens = 0;
    private Integer videoTokens = 0;

    private Integer reasoningTokens = 0;
    private Integer acceptedPredictionTokens = 0;
    private Integer rejectedPredictionTokens = 0;

    public static ChatCompletionUsageDetail from(ChatCompletion.Usage usage) {
        ChatCompletionUsageDetail completionTokensDetails = new ChatCompletionUsageDetail();

        if (usage == null) {
            return completionTokensDetails;
        }

        if (usage.getCompletionTokensDetails() != null) {
            Integer audioTokens = usage.getCompletionTokensDetails().getAudioTokens();
            Integer completionTokens = usage.getCompletionTokens();

            // Calculate text tokens safely
            if (audioTokens != null && completionTokens != null) {
                completionTokensDetails.textTokens = completionTokens - audioTokens;
            } else if (completionTokens != null) {
                completionTokensDetails.textTokens = completionTokens;
            }

            // Set audio tokens
            if (audioTokens != null) {
                completionTokensDetails.audioTokens = audioTokens;
            }

            // Set reasoning tokens
            Integer reasoningTokens = usage.getCompletionTokensDetails().getReasoningTokens();
            if (reasoningTokens != null) {
                completionTokensDetails.reasoningTokens = reasoningTokens;
            }

            // Set accepted prediction tokens
            Integer acceptedTokens = usage.getCompletionTokensDetails().getAcceptedPredictionTokens();
            if (acceptedTokens != null) {
                completionTokensDetails.acceptedPredictionTokens = acceptedTokens;
            }

            // Set rejected prediction tokens
            Integer rejectedTokens = usage.getCompletionTokensDetails().getRejectedPredictionTokens();
            if (rejectedTokens != null) {
                completionTokensDetails.rejectedPredictionTokens = rejectedTokens;
            }
        } else if (usage.getCompletionTokens() != null) {
            completionTokensDetails.textTokens = usage.getCompletionTokens();
        }

        return completionTokensDetails;
    }

    public static ChatCompletionUsageDetail from(ChatCompletionChunk.Usage usage) {
        ChatCompletionUsageDetail completionTokensDetails = new ChatCompletionUsageDetail();

        if (usage == null) {
            return completionTokensDetails;
        }

        Integer completionTokens = usage.getCompletionTokens();

        if (completionTokens != null) {
            // Since ChatCompletionChunk.Usage doesn't provide detailed token breakdown,
            // we assume all tokens are text tokens
            completionTokensDetails.textTokens = completionTokens;
            completionTokensDetails.reasoningTokens = usage.getCompletionTokensDetails().getReasoningTokens();
        }

        return completionTokensDetails;
    }

    public ChatCompletionUsageDetail(int textTokens) {
        this.textTokens = textTokens;
    }

    public ChatCompletionUsageDetail(int textTokens, int reasoningTokens) {
        this.textTokens = textTokens;
        this.reasoningTokens = reasoningTokens;
    }


    public static ChatCompletionUsageDetail createOpenAIImageToken(int imageTokens) {
        var promptUsageDetail = new ChatCompletionUsageDetail();
        promptUsageDetail.setImageTokens(imageTokens);
        return promptUsageDetail;
    }

    public ChatCompletionUsageDetail(List<GeminiUsageMetadata.ModalityTokenCount> candidatesTokensDetails, Integer reasoningTokens) {
        for (GeminiUsageMetadata.ModalityTokenCount tokenCount : candidatesTokensDetails) {
            switch (tokenCount.getModality()) {
                case "TEXT" -> textTokens = tokenCount.getTokenCount();
                case "AUDIO" -> audioTokens = tokenCount.getTokenCount();
                case "IMAGE" -> imageTokens = tokenCount.getTokenCount();
                case "VIDEO" -> videoTokens = tokenCount.getTokenCount();
                default -> log.warn("Unknown completion modality: {}", tokenCount.getModality());
            }
        }
        this.reasoningTokens = reasoningTokens;
    }

}