package ai.yourouter.clickhouse.config;

import ai.yourouter.clickhouse.service.ClickHouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

/**
 * ClickHouse初始化器
 * 应用启动时自动初始化ClickHouse数据库表
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnBean(name = "ckJdbcTemplate")
public class ClickHouseInitializer implements ApplicationRunner {

    private final ClickHouseService clickHouseService;

    @Override
    public void run(ApplicationArguments args) {
        try {
            log.info("开始初始化ClickHouse数据库...");
            
            // 检查连接
            if (!clickHouseService.checkConnection()) {
                log.warn("ClickHouse连接失败，跳过初始化");
                return;
            }
            
            // 初始化数据库表
            clickHouseService.initializeDatabase();
            
            log.info("ClickHouse数据库初始化完成");
            
        } catch (Exception e) {
            log.error("ClickHouse数据库初始化失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
