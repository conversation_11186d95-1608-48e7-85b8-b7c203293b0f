package ai.yourouter.clickhouse.controller;

import ai.yourouter.clickhouse.service.ClickHouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * ClickHouse管理控制器
 * 提供ClickHouse相关的管理操作
 */
@Slf4j
@RestController
@RequestMapping("/admin/clickhouse")
@RequiredArgsConstructor
@ConditionalOnProperty(name = "clickhouse.enabled", havingValue = "true", matchIfMissing = true)
public class ClickHouseAdminController {

    private final ClickHouseService clickHouseService;

    /**
     * 检查ClickHouse连接状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> checkHealth() {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean isHealthy = clickHouseService.checkConnection();
            result.put("status", isHealthy ? "UP" : "DOWN");
            result.put("message", isHealthy ? "ClickHouse连接正常" : "ClickHouse连接失败");
            
            return isHealthy ? 
                ResponseEntity.ok(result) : 
                ResponseEntity.status(503).body(result);
                
        } catch (Exception e) {
            log.error("检查ClickHouse健康状态失败", e);
            result.put("status", "DOWN");
            result.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.status(503).body(result);
        }
    }

    /**
     * 初始化ClickHouse数据库表
     */
    @PostMapping("/init")
    public ResponseEntity<Map<String, Object>> initDatabase() {
        Map<String, Object> result = new HashMap<>();
        try {
            clickHouseService.initializeDatabase();
            result.put("status", "SUCCESS");
            result.put("message", "ClickHouse数据库表初始化成功");
            
            log.info("通过管理接口初始化ClickHouse数据库表成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("通过管理接口初始化ClickHouse数据库表失败", e);
            result.put("status", "ERROR");
            result.put("message", "初始化失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }

    /**
     * 迁移表结构到优化版本
     */
    @PostMapping("/migrate")
    public ResponseEntity<Map<String, Object>> migrateTable() {
        Map<String, Object> result = new HashMap<>();
        try {
            clickHouseService.migrateToOptimizedTable();
            result.put("status", "SUCCESS");
            result.put("message", "ClickHouse表结构迁移成功");
            
            log.info("通过管理接口迁移ClickHouse表结构成功");
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("通过管理接口迁移ClickHouse表结构失败", e);
            result.put("status", "ERROR");
            result.put("message", "迁移失败: " + e.getMessage());
            return ResponseEntity.status(500).body(result);
        }
    }
}
