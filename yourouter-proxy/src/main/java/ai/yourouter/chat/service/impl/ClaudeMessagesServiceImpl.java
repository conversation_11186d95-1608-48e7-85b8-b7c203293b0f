package ai.yourouter.chat.service.impl;

import ai.yourouter.calculate.llm.OrganizationLLMCalculatePrice;
import ai.yourouter.chat.channel.ClaudeRemoteService;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.service.AbstractRemoteServiceTemplate;
import ai.yourouter.common.service.AsyncUsageStatisticsService;
import ai.yourouter.common.utils.CharacterLLMStatisticsConverter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;

/**
 * Claude Messages 服务实现
 * 专门处理 Claude Messages API 的业务逻辑
 */
@Slf4j
@Service
public class ClaudeMessagesServiceImpl extends AbstractRemoteServiceTemplate {

    private final ClaudeRemoteService claudeRemoteService;
    private final OrganizationLLMCalculatePrice organizationLLMCalculatePrice;
    private final AsyncUsageStatisticsService asyncUsageStatisticsService;

    public ClaudeMessagesServiceImpl(RetryConfig retryConfig,
                                     KmgRemoteService kmgRemoteService,
                                     ClaudeRemoteService claudeRemoteService,
                                     OrganizationLLMCalculatePrice organizationLLMCalculatePrice,
                                     AsyncUsageStatisticsService asyncUsageStatisticsService) {
        super(retryConfig, kmgRemoteService);
        this.claudeRemoteService = claudeRemoteService;
        this.organizationLLMCalculatePrice = organizationLLMCalculatePrice;
        this.asyncUsageStatisticsService = asyncUsageStatisticsService;
    }

    /**
     * Claude 非流式消息完成
     */
    @SneakyThrows
    public Mono<Object> completion(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);
            String modelName = req.get("model").toString();

            Mono<Object> operation = getAvailableKey(modelName, chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return claudeRemoteService.nonStreamClaudeCompletion(req, chatContext);
                    });

            return executeWithRetry(operation, "Claude 非流式请求", chatContext);
        });
    }

    /**
     * Claude 流式消息完成
     */
    @SneakyThrows
    public Flux<ServerSentEvent<String>> streamCompletion(HashMap<String, Object> req) {
        return Flux.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);
            String modelName = req.get("model").toString();

            Flux<ServerSentEvent<String>> streamOperation = getAvailableKeyForStream(modelName, chatContext)
                    .flatMapMany(key -> {
                        chatContext.setKeyInfo(key);
                        return claudeRemoteService.streamClaudeCompletion(req, chatContext);
                    });

            return executeStreamWithClientManagement(streamOperation, "Claude 流式请求", chatContext);
        });
    }

    /**
     * 提取聊天上下文并设置请求信息
     */
    private ChatContext extractChatContext(reactor.util.context.ContextView contextView, HashMap<String, Object> req) {
        ChatContext chatContext = contextView.get("chatContext");
        chatContext.getChatRequestStatistic().setRawRequest(req);
        return chatContext;
    }

    @Override
    protected void publishUsageStatistics(ChatContext chatContext) {
        try {
            organizationLLMCalculatePrice.organizationListen(
                    CharacterLLMStatisticsConverter.convert(chatContext),
                    chatContext.apiModelName()
            );
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布 Claude 使用量统计详细错误信息", e);
        }
    }

    @Override
    protected void publishUsageStatisticsAsync(ChatContext chatContext) {
        // 使用智能选择方法，根据配置自动决定异步或同步处理
        asyncUsageStatisticsService.publishLLMUsageStatisticsIntelligent(
                chatContext,
                () -> publishUsageStatistics(chatContext)
        );
    }
}
