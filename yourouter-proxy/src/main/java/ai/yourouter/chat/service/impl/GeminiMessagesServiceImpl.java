package ai.yourouter.chat.service.impl;

import ai.yourouter.chat.channel.GeminiRemoteService;
import ai.yourouter.chat.config.RetryConfig;
import ai.yourouter.chat.remote.KmgRemoteService;
import ai.yourouter.chat.util.ChatLogUtils;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.service.AbstractRemoteServiceTemplate;
import ai.yourouter.common.service.AsyncUsageStatisticsService;
import ai.yourouter.calculate.llm.OrganizationLLMCalculatePrice;
import ai.yourouter.common.utils.CharacterLLMStatisticsConverter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Gemini 消息服务实现
 * 处理 Gemini 原生 API 的业务逻辑
 */
@Slf4j
@Service
public class GeminiMessagesServiceImpl extends AbstractRemoteServiceTemplate {

    private final GeminiRemoteService geminiRemoteService;
    private final OrganizationLLMCalculatePrice organizationLLMCalculatePrice;
    private final AsyncUsageStatisticsService asyncUsageStatisticsService;

    public GeminiMessagesServiceImpl(RetryConfig retryConfig,
                                   KmgRemoteService kmgRemoteService,
                                   GeminiRemoteService geminiRemoteService,
                                   OrganizationLLMCalculatePrice organizationLLMCalculatePrice,
                                   AsyncUsageStatisticsService asyncUsageStatisticsService) {
        super(retryConfig, kmgRemoteService);
        this.geminiRemoteService = geminiRemoteService;
        this.organizationLLMCalculatePrice = organizationLLMCalculatePrice;
        this.asyncUsageStatisticsService = asyncUsageStatisticsService;
    }

    /**
     * Gemini 非流式消息完成
     */
    @SneakyThrows
    public Mono<Object> completion(HashMap<String, Object> req) {
        return Mono.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);

            Mono<Object> operation = getAvailableKey(chatContext.apiModelName(), chatContext)
                    .flatMap(key -> {
                        chatContext.setKeyInfo(key);
                        return geminiRemoteService.nonStreamGeminiCompletion(req, chatContext);
                    });

            return executeWithRetry(operation, "Gemini 非流式请求", chatContext);
        });
    }

    /**
     * Gemini 流式消息完成
     */
    @SneakyThrows
    public Flux<ServerSentEvent<String>> streamCompletion(HashMap<String, Object> req) {
        return Flux.deferContextual(contextView -> {
            ChatContext chatContext = extractChatContext(contextView, req);

            Flux<ServerSentEvent<String>> streamOperation = getAvailableKeyForStream(chatContext.apiModelName(), chatContext)
                    .flatMapMany(key -> {
                        chatContext.setKeyInfo(key);
                        return geminiRemoteService.streamGeminiCompletion(req, chatContext);
                    });

            return executeStreamWithClientManagement(streamOperation, "Gemini 流式请求", chatContext);
        });
    }

    /**
     * 提取聊天上下文并设置请求信息
     */
    private ChatContext extractChatContext(reactor.util.context.ContextView contextView, HashMap<String, Object> req) {
        ChatContext chatContext = contextView.get("chatContext");
        chatContext.getChatRequestStatistic().setRawRequest(req);
        boolean hasGoogleSearch = false;
        if (req.containsKey("tools")) {
            Object toolsObj = req.get("tools");
            if (toolsObj instanceof List<?> toolsList) {
                for (Object tool : toolsList) {
                    if (tool instanceof Map<?, ?> toolMap) {
                        if (toolMap.containsKey("google_search")) {
                            hasGoogleSearch = true;
                            break;
                        }
                    }
                }
            }
        }
        if (hasGoogleSearch) {
            chatContext.getChatRequestStatistic().setSearchRequest(true);
        }

        return chatContext;
    }

    @Override
    protected void publishUsageStatistics(ChatContext chatContext) {
        try {
            organizationLLMCalculatePrice.organizationListen(
                    CharacterLLMStatisticsConverter.convert(chatContext),
                    chatContext.apiModelName()
            );
            ChatLogUtils.logUsageStatistics(chatContext, true);
        } catch (Exception e) {
            ChatLogUtils.logUsageStatistics(chatContext, false);
            log.error("发布 Gemini 使用量统计详细错误信息", e);
        }
    }

    @Override
    protected void publishUsageStatisticsAsync(ChatContext chatContext) {
        // 使用智能选择方法，根据配置自动决定异步或同步处理
        asyncUsageStatisticsService.publishLLMUsageStatisticsIntelligent(
                chatContext,
                () -> publishUsageStatistics(chatContext)
        );
    }
}
