package ai.yourouter.chat.controller;

import ai.yourouter.chat.service.impl.LLMChatCompletionServiceImpl;
import ai.yourouter.chat.util.RequestValidationUtils;
import ai.yourouter.chat.util.ResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.CorePublisher;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;


@Slf4j
@RestController
@RequestMapping("")
@RequiredArgsConstructor
@SuppressWarnings("ReactiveStreamsUnusedPublisher")
public class LLMChatCompletionController {

    private final LLMChatCompletionServiceImpl llmChatCompletionService;


    @PostMapping("/v1/chat/completions")
    public ResponseEntity<? extends CorePublisher<?>> chat(@RequestBody LinkedHashMap<String, Object> req, @RequestParam(required = false) Map<String, String> params) {
        // 验证请求参数
        if (RequestValidationUtils.isInvalidChatRequest(req)) {
            log.warn("无效的请求参数: {}", req);
            return ResponseUtils.createErrorResponse(HttpStatus.BAD_REQUEST);
        }

        boolean isStream = RequestValidationUtils.parseStreamParameter(req);

        if (isStream) {
            return buildStreamResponse(req, params);
        } else {
            return buildNonStreamResponse(req, params);
        }
    }

    /**
     * 构建流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildStreamResponse(LinkedHashMap<String, Object> req, Map<String, String> params) {
        // 创建请求副本并确保包含必要的默认值
        HashMap<String, Object> requestCopy = RequestValidationUtils.createRequestCopy(req);
        RequestValidationUtils.ensureDefaultValues(requestCopy, true);

        return ResponseUtils.createStreamResponse(llmChatCompletionService.streamCompletion(requestCopy, params));
    }

    /**
     * 构建非流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildNonStreamResponse(LinkedHashMap<String, Object> req, Map<String, String> params) {
        // 创建请求副本并确保包含必要的默认值
        HashMap<String, Object> requestCopy = RequestValidationUtils.createRequestCopy(req);
        RequestValidationUtils.ensureDefaultValues(requestCopy, false);

        return ResponseUtils.createNonStreamResponse(llmChatCompletionService.completion(requestCopy, params));
    }

    /**
     * OpenAI Response API 端点
     */
    @PostMapping("/v1/responses")
    public ResponseEntity<? extends CorePublisher<?>> responses(@RequestBody LinkedHashMap<String, Object> req, @RequestParam(required = false) Map<String, String> params) {
        // 验证请求参数
        if (RequestValidationUtils.isInvalidChatRequest(req)) {
            log.warn("无效的Response请求参数: {}", req);
            return ResponseUtils.createErrorResponse(HttpStatus.BAD_REQUEST);
        }

        boolean isStream = RequestValidationUtils.parseStreamParameter(req);

        if (isStream) {
            return buildStreamResponseForResponse(req, params);
        } else {
            return buildNonStreamResponseForResponse(req, params);
        }
    }

    /**
     * 构建Response API流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildStreamResponseForResponse(LinkedHashMap<String, Object> req, Map<String, String> params) {
        // 创建请求副本并确保包含必要的默认值
        HashMap<String, Object> requestCopy = RequestValidationUtils.createRequestCopy(req);
        RequestValidationUtils.ensureResponseDefaultValues(requestCopy, true);

        return ResponseUtils.createStreamResponse(llmChatCompletionService.streamResponseCompletion(requestCopy, params));
    }

    /**
     * 构建Response API非流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildNonStreamResponseForResponse(LinkedHashMap<String, Object> req, Map<String, String> params) {
        // 创建请求副本并确保包含必要的默认值
        HashMap<String, Object> requestCopy = RequestValidationUtils.createRequestCopy(req);
        RequestValidationUtils.ensureResponseDefaultValues(requestCopy, false);

        return ResponseUtils.createNonStreamResponse(llmChatCompletionService.responseCompletion(requestCopy, params));
    }
}
