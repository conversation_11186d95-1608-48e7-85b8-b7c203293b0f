package ai.yourouter.chat.controller;

import ai.yourouter.chat.service.impl.ClaudeMessagesServiceImpl;
import ai.yourouter.chat.util.ClaudeRequestValidationUtils;
import ai.yourouter.chat.util.ResponseUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.CorePublisher;

import java.util.HashMap;
import java.util.LinkedHashMap;

/**
 * Claude Messages API 控制器
 * 处理 Claude Messages API 的 HTTP 请求
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@SuppressWarnings("ReactiveStreamsUnusedPublisher")
public class ClaudeMessagesController {

    private final ClaudeMessagesServiceImpl claudeMessagesService;

    /**
     * Claude Messages API 端点
     * 支持流式和非流式请求
     */
    @PostMapping("/v1/messages")
    public ResponseEntity<? extends CorePublisher<?>> messages(@RequestBody LinkedHashMap<String, Object> req) {
        // 验证 Claude 请求参数
//        if (!ClaudeRequestValidationUtils.isValidClaudeMessagesRequest(req)) {
//            log.warn("无效的 Claude 请求参数: {}", req);
//            return ResponseUtils.createErrorResponse(HttpStatus.BAD_REQUEST);
//        }

        boolean isStream = ClaudeRequestValidationUtils.parseClaudeStreamParameter(req);

        if (isStream) {
            return buildClaudeStreamResponse(req);
        } else {
            return buildClaudeNonStreamResponse(req);
        }
    }

    /**
     * 构建 Claude 流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildClaudeStreamResponse(LinkedHashMap<String, Object> req) {
        // 创建请求副本并确保包含必要的默认值
        HashMap<String, Object> requestCopy = ClaudeRequestValidationUtils.createClaudeRequestCopy(req);
        ClaudeRequestValidationUtils.ensureClaudeDefaultValues(requestCopy, true);

        return ResponseUtils.createStreamResponse(claudeMessagesService.streamCompletion(requestCopy));
    }

    /**
     * 构建 Claude 非流式响应
     */
    private ResponseEntity<? extends CorePublisher<?>> buildClaudeNonStreamResponse(LinkedHashMap<String, Object> req) {
        // 创建请求副本并确保包含必要的默认值
        HashMap<String, Object> requestCopy = ClaudeRequestValidationUtils.createClaudeRequestCopy(req);
        ClaudeRequestValidationUtils.ensureClaudeDefaultValues(requestCopy, false);

        return ResponseUtils.createNonStreamResponse(claudeMessagesService.completion(requestCopy));
    }

}
