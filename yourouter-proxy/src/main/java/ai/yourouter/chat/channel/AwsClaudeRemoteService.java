package ai.yourouter.chat.channel;

import ai.yourouter.chat.channel.aws.req.BedrockRequest;
import ai.yourouter.chat.channel.response.claude.ClaudeMessageTokenStats;
import ai.yourouter.chat.remote.response.BestKeyResponse;
import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.context.usage.ChatUsage;
import ai.yourouter.common.exception.CognitionWebException;
import ai.yourouter.common.exception.error.OpenAIError;
import ai.yourouter.common.utils.BedrockApiInvoker;
import ai.yourouter.common.utils.JsonUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.codec.ServerSentEvent;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;

/**
 * AWS Bedrock Claude 远程服务
 * 专门处理AWS Bedrock Claude API的通信
 */
@Slf4j
@Service
@RequiredArgsConstructor
@SuppressWarnings("ALL")
public class AwsClaudeRemoteService {

    // AWS Bedrock 相关常量
    private static final String AWS_STREAM_URI = "https://bedrock-runtime.%s.amazonaws.com/model/%s/invoke-with-response-stream";
    private static final String AWS_NON_STREAM_URI = "https://bedrock-runtime.%s.amazonaws.com/model/%s/invoke";
    private static final String AWS_ANTHROPIC_VERSION = "bedrock-2023-05-31";

    @Qualifier("httpWebClient")
    private final WebClient httpWebClient;

    @Qualifier("sseWebClient")
    private final WebClient sseWebClient;

    private final ObjectMapper objectMapper;

    /**
     * URL编码工具方法
     */
    private static String encodeURL(String url) {
        return URLEncoder.encode(url, StandardCharsets.UTF_8);
    }

    /**
     * 为AWS Bedrock请求添加认证头
     */
    private static void addAwsResponseHeader(String body, ChatContext chatContext, WebClient.RequestBodySpec requestBodyUriSpec, String url) {
        var timestamp = ZonedDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmss'Z'"));
        var keyInfo = chatContext.getKeyInfo();
        String secret = keyInfo.getSecret();
        String accessKey = keyInfo.getToken();
        String region = keyInfo.getRegion();
        
        var request = new BedrockRequest(
                accessKey,
                secret,
                region,
                "bedrock",
                body,
                timestamp,
                UUID.randomUUID().toString()
        );
        var response = BedrockApiInvoker.invokeBedrockApi(request, url);
        response.getHeaders().forEach(requestBodyUriSpec::header);
    }

    /**
     * 准备AWS Bedrock请求体
     */
    public void prepareAwsRequestBody(HashMap<String, Object> body) {
        body.remove("model");
        body.remove("stream");
        body.put("anthropic_version", AWS_ANTHROPIC_VERSION);
    }

    /**
     * 创建AWS Bedrock流式请求
     */
    public Flux<ServerSentEvent<String>> streamAwsCompletion(HashMap<String, Object> body, 
                                                             ChatContext chatContext,
                                                             Function<String, Tuple2<String, String>> processStreamFunction,
                                                             Function<Throwable, Void> errorHandler,
                                                             Runnable successHandler) {
        AtomicBoolean isFirst = new AtomicBoolean(true);
        BestKeyResponse key = chatContext.getKeyInfo();
        String region = key.getRegion();
        var uri = String.format(AWS_STREAM_URI, region, encodeURL(chatContext.apiModelName()));
        var req = JsonUtils.toJSONString(body);
        
        WebClient.RequestBodySpec clientReqSpec;
        try {
            clientReqSpec = sseWebClient.post().uri(new URI(uri));
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        
        addAwsResponseHeader(req, chatContext, clientReqSpec, uri);
        
        return clientReqSpec
                .bodyValue(req)
                .retrieve()
                .bodyToFlux(byte[].class)
                .map(bytes -> new String(bytes, StandardCharsets.UTF_8))
                .scan(new StringBuilder(), (acc, value) -> {
                    acc.append(value);
                    int newlineIndex;
                    while ((newlineIndex = acc.indexOf("\r")) != -1) {
                        String line = acc.substring(0, newlineIndex);
                        acc.delete(0, newlineIndex + 1);
                        if (!line.isBlank()) {
                            if (line.contains(":message-type")) {
                                String content = parseEventContent(line);
                                return new StringBuilder(content);
                            }
                        }
                    }
                    return acc;
                })
                .filter(sb -> sb.length() > 0)
                .map(StringBuilder::toString)
                .map(processStreamFunction)
                .map(response -> (ServerSentEvent.<String>builder().data(response._1).event(response._2).build()))
                .publishOn(Schedulers.boundedElastic())
                .doOnError(error -> errorHandler.apply(error))
                .doOnComplete(successHandler);
    }

    /**
     * 创建AWS Bedrock非流式请求
     */
    public Mono<Object> nonStreamAwsCompletion(HashMap<String, Object> body, 
                                               ChatContext chatContext,
                                               Function<String, Object> processNonStreamFunction,
                                               Function<Throwable, Void> errorHandler,
                                               Runnable successHandler) {
        BestKeyResponse key = chatContext.getKeyInfo();
        String region = key.getRegion();
        var uri = String.format(AWS_NON_STREAM_URI, region, encodeURL(chatContext.apiModelName()));
        var req = JsonUtils.toJSONString(body);
        
        WebClient.RequestBodySpec clientReqSpec;
        try {
            clientReqSpec = httpWebClient.post().uri(new URI(uri));
        } catch (URISyntaxException e) {
            log.error("AWS URI构建错误", e);
            throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
        }
        
        addAwsResponseHeader(req, chatContext, clientReqSpec, uri);
        
        return clientReqSpec
                .bodyValue(req)
                .retrieve()
                .bodyToMono(String.class)
                .doOnCancel(() -> {
                    log.warn("AWS请求被取消或超时");
                    throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
                })
                .map(processNonStreamFunction)
                .publishOn(Schedulers.boundedElastic())
                .doOnError(error -> errorHandler.apply(error))
                .doOnSuccess(result -> successHandler.run());
    }

    /**
     * 解析AWS事件内容
     */
    private static String parseEventContent(String content) {
        try {
            var jsonContent = getString(content);
            var jsonNode = JsonUtils.parseJson(jsonContent);
            String bytesBase64 = jsonNode.get("bytes").asText();
            byte[] decodedBytes = Base64.getDecoder().decode(bytesBase64);
            return new String(decodedBytes);
        } catch (Exception e) {
            log.error("解析AWS事件内容时发生错误: {}", e.getMessage());
            return "";
        }
    }

    /**
     * 提取JSON字符串
     */
    private static String getString(String content) {
        int jsonStart = content.indexOf("{");
        int bracketCount = 0;
        int jsonEnd = jsonStart;
        for (int i = jsonStart; i < content.length(); i++) {
            if (content.charAt(i) == '{') {
                bracketCount++;
            } else if (content.charAt(i) == '}') {
                bracketCount--;
            }
            if (bracketCount == 0) {
                jsonEnd = i + 1;
                break;
            }
        }
        return content.substring(jsonStart, jsonEnd);
    }

    /**
     * 处理AWS流式响应的使用情况统计
     */
    public Function<String, Tuple2<String, String>> createAwsStreamProcessor(ChatContext chatContext, AtomicBoolean isFirst) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            if (isFirst.getAndSet(false)) {
                chatContext.getChatRequestStatistic().setPreparationTime(Instant.now().toEpochMilli());
                log.debug("收到首个AWS数据块 | 用户ID: {} | 模型: {}", userId, modelName);
            }

            // 收集流式响应内容到 streamResponse
            if (message != null && !message.trim().isEmpty()) {
                chatContext.getChatRequestStatistic().getStreamResponse().append(message).append("\n");
            }

            try {
                log.debug("接收AWS数据块 | 用户ID: {} | 模型: {} | 消息内容: {}", userId, modelName, message);

                var jsonNode = JsonUtils.parseJson(message);
                var type = jsonNode.path("type").asText();
                if ("message_start".equalsIgnoreCase(type)) {
                    var usage = objectMapper.convertValue(jsonNode.path("message").path("usage"), ClaudeMessageTokenStats.class);
                    chatContext.setChatUsage(ChatUsage.create(usage));
                    log.info("更新AWS使用情况 | 请求类型: 流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(usage));
                }

                if ("message_delta".equalsIgnoreCase(type)) {
                    var outputTokens = jsonNode.path("usage").path("output_tokens").asInt();
                    chatContext.getChatUsage().updateOutPut(outputTokens);
                }

                log.debug("AWS数据块解析完成 | 用户ID: {} | 模型: {} | 响应内容: {}", userId, modelName, message);
                return Tuple.of(message, type);
            } catch (Exception e) {
                log.error("AWS数据块解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}", userId, modelName, e.getMessage());
                throw new CognitionWebException(JsonUtils.toJSONString(OpenAIError.serviceUnavailable()));
            }
        };
    }

    /**
     * 处理AWS非流式响应的使用情况统计
     */
    public Function<String, Object> createAwsNonStreamProcessor(ChatContext chatContext) {
        Long userId = chatContext.getChatUserInfo().getCharactersId();
        String modelName = chatContext.apiModelName();

        return message -> {
            try {
                log.debug("接收AWS非流式响应 | 用户ID: {} | 模型: {} | 消息内容: {}", userId, modelName, message);
                var usage = objectMapper.convertValue(JsonUtils.parseJson(message).path("usage"), ClaudeMessageTokenStats.class);

                if (usage != null) {
                    chatContext.setChatUsage(ChatUsage.create(usage));
                    log.info("更新AWS使用情况 | 请求类型: 非流式 | 用户ID: {} | 模型: {} | 使用量: {}", userId, modelName, JsonUtils.toJSONString(usage));
                }

                try {
                    return objectMapper.readValue(message, Object.class);
                } catch (Exception e) {
                    log.error("AWS非流式响应解析错误", e);
                    throw new RuntimeException(e);
                }
            } catch (Exception e) {
                log.error("AWS响应解析错误 | 用户ID: {} | 模型: {} | 错误信息: {}", userId, modelName, e.getMessage());
                throw new CognitionWebException("Error occurred with AWS Claude API parse: " + e.getMessage());
            }
        };
    }
}
