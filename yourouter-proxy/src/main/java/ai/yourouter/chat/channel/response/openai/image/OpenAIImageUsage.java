package ai.yourouter.chat.channel.response.openai.image;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OpenAIImageUsage {
    @JsonProperty("total_tokens")
    private int totalTokens;
    @JsonProperty("input_tokens")
    private int inputTokens;
    @JsonProperty("output_tokens")
    private int outputTokens;
    @JsonProperty("input_tokens_details")
    private InputTokensDetails inputTokensDetails;


    @Data
    public static class InputTokensDetails {
        @JsonProperty("text_tokens")
        private int textTokens;
        @JsonProperty("image_tokens")
        private int imageTokens;
    }

}
