package ai.yourouter.chat.channel.aws.req;

import java.util.Map;

public class BedrockResponse {
    private Map<String, String> headers;
    private String responseBody;

    public BedrockResponse(Map<String, String> headers, String responseBody) {
        this.headers = headers;
        this.responseBody = responseBody;
    }

    // Getters
    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public String getResponseBody() {
        return responseBody;
    }

    public void setResponseBody(String responseBody) {
        this.responseBody = responseBody;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("BedrockResponse:\n");
        sb.append("Headers:\n");
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            sb.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }
        sb.append("Response Body: ").append(responseBody);
        return sb.toString();
    }
}
