package ai.yourouter.chat.channel.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

@Configuration
public class WebClientConfig {

    @Bean
    public WebClient httpWebClient() {
        // 创建连接池
        ConnectionProvider provider = ConnectionProvider.builder("hwc-cos")
                .maxConnections(10000) // 设置最大连接数
                .maxIdleTime(Duration.ofSeconds(20)) // 设置连接的最大空闲时间
                .maxLifeTime(Duration.ofSeconds(60)) // 设置连接的最大生命周期
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 设置等待获取连接的超时时间
                .build();

        final int size = 16 * 1024 * 1024;

        final ExchangeStrategies strategies = ExchangeStrategies.builder()
                .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
                .build();

        // 配置HttpClient
        HttpClient httpClient = HttpClient.create(provider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 15000) // 设置连接超时为5秒
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(6000, TimeUnit.SECONDS)) // 设置读取超时为10秒
                        .addHandlerLast(new WriteTimeoutHandler(6000, TimeUnit.SECONDS))); // 设置写入超时为10秒

        // 创建WebClient
        return WebClient.builder()
                .exchangeStrategies(strategies)
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }


    @Bean
    public WebClient gcpStreamClient() {
        // 创建连接池
        ConnectionProvider provider = ConnectionProvider.builder("gs-cos")
                .maxConnections(20000) // 设置最大连接数
                .maxIdleTime(Duration.ofSeconds(20)) // 设置连接的最大空闲时间
                .maxLifeTime(Duration.ofSeconds(60)) // 设置连接的最大生命周期
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 设置等待获取连接的超时时间
                .build();

        // 配置HttpClient
        HttpClient httpClient = HttpClient.create(provider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000) // 设置连接超时为5秒
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(3, TimeUnit.SECONDS)) // 设置读取超时为10秒
                        .addHandlerLast(new WriteTimeoutHandler(900, TimeUnit.SECONDS))); // 设置写入超时为10秒

        // 创建WebClient
        return WebClient.builder()
//                .clientConnector(
//                        new ReactorClientHttpConnector(
//                                HttpClient.create().proxyWithSystemProperties()))
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }

    @Bean
    public WebClient gcpHttpClient() {
        // 创建连接池
        ConnectionProvider provider = ConnectionProvider.builder("gh-cos")
                .maxConnections(10000) // 设置最大连接数
                .maxIdleTime(Duration.ofSeconds(20)) // 设置连接的最大空闲时间
                .maxLifeTime(Duration.ofSeconds(60)) // 设置连接的最大生命周期
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 设置等待获取连接的超时时间
                .build();

        // 配置HttpClient
        HttpClient httpClient = HttpClient.create(provider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 1000) // 设置连接超时为5秒
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(900, TimeUnit.SECONDS)) // 设置读取超时为10秒
                        .addHandlerLast(new WriteTimeoutHandler(900, TimeUnit.SECONDS))); // 设置写入超时为10秒

        // 创建WebClient
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }


    @Bean
    public WebClient sseWebClient() {
        // 创建连接池
        ConnectionProvider provider = ConnectionProvider.builder("sse-cos")
                .maxConnections(10000) // 设置最大连接数
                .maxIdleTime(Duration.ofSeconds(20)) // 设置连接的最大空闲时间
                .maxLifeTime(Duration.ofSeconds(60)) // 设置连接的最大生命周期
                .pendingAcquireTimeout(Duration.ofSeconds(60)) // 设置等待获取连接的超时时间
                .build();

        // 配置HttpClient
        HttpClient httpClient = HttpClient.create(provider)
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 5000) // 设置连接超时为5秒
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(900, TimeUnit.SECONDS)) // 设置读取超时为10秒
                        .addHandlerLast(new WriteTimeoutHandler(900, TimeUnit.SECONDS))); // 设置写入超时为10秒

        // 创建WebClient
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build();
    }


}