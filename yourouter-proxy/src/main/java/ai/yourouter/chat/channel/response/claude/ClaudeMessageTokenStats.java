package ai.yourouter.chat.channel.response.claude;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Token statistics model class for tracking token usage and cache performance.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ClaudeMessageTokenStats {

    /**
     * The total number of input tokens processed.
     * Used to track the size of input data for processing.
     */
    @Builder.Default
    @JsonProperty("input_tokens")
    private Integer inputTokens = 0;

    /**
     * The number of tokens used during cache creation.
     * Tracks token usage specifically for cache generation operations.
     */
    @Builder.Default
    @JsonProperty("cache_creation_input_tokens")
    private Integer cacheCreationInputTokens = 0;

    /**
     * The number of tokens read from cache.
     * Measures cache utilization through token read operations.
     */
    @Builder.Default
    @JsonProperty("cache_read_input_tokens")
    private Integer cacheReadInputTokens = 0;

    /**
     * The total number of output tokens generated.
     * Represents the size of the processed output data.
     */
    @Builder.Default
    @JsonProperty("output_tokens")
    private Integer outputTokens = 0;
}
