package ai.yourouter.chat.channel.response.gemini;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Gemini 流式响应模型
 * 对应 Gemini streamGenerateContent API 的响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeminiResponseChunk {


    /**
     * 使用量元数据
     */
    @JsonProperty("usageMetadata")
    private GeminiUsageMetadata usageMetadata;

}
