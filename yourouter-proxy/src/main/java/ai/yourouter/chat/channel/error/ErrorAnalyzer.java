package ai.yourouter.chat.channel.error;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.Set;

/**
 * 错误分析器，用于判断400错误是否可以重试
 * 采用白名单机制：只有包含特定错误消息的400错误才可以重试，其他所有情况都不可重试
 */
@Slf4j
@Component
public class ErrorAnalyzer {

    /**
     * 可重试的错误消息白名单
     * 只有包含这些特定错误消息的400错误才可以重试，其他所有情况都不可重试
     */
    private static final Set<String> RETRYABLE_ERROR_MESSAGES = Set.of(
            "Your credit balance is too low",
            "This organization has been disabled",
            "Organization has been restricted",
            "is not allowed to use Publisher Model",
            "Your account is not active",
            "You exceeded your current quota",
            "Your access was terminated due to violation of our policies",
            "403 Forbidden from POST"
    );

    /**
     * 分析400错误是否可以重试
     * 基于响应文本内容进行分析，支持多种API提供商
     *
     * @param ex WebClientResponseException
     * @return ErrorAnalysisResult 分析结果
     */
    public ErrorAnalysisResult analyze400Error(WebClientResponseException ex) {
        String responseBody = ex.getResponseBodyAsString();

        log.debug("开始分析400错误 | 响应内容: {}", responseBody);

        // 基于响应文本进行分析
        return analyzeErrorText(responseBody);
    }



    /**
     * 分析错误文本内容
     * 只有包含白名单中特定错误消息的400错误才可以重试，其他所有情况都不可重试
     */
    private ErrorAnalysisResult analyzeErrorText(String errorText) {
        if (errorText == null || errorText.trim().isEmpty()) {
            return ErrorAnalysisResult.nonRetryable("空的错误响应");
        }

        log.info("分析错误文本 | 内容: {}", errorText);

        // 检查是否包含可重试的错误消息
        for (String retryableMessage : RETRYABLE_ERROR_MESSAGES) {
            if (errorText.contains(retryableMessage)) {
                String reason = "错误文本包含可重试的错误消息: " + retryableMessage;
                log.info("匹配到可重试错误消息: {}", retryableMessage);
                return ErrorAnalysisResult.retryable(reason);
            }
        }

        // 默认不重试（保守策略）
        // 对于不在白名单中的400错误，一律不重试
        log.info("错误消息不在可重试白名单中，不重试");
        return ErrorAnalysisResult.nonRetryable("错误消息不在可重试白名单中，不重试");
    }

    /**
     * 错误分析结果
     */
    @Getter
    public static class ErrorAnalysisResult {
        private final boolean retryable;
        private final String reason;

        private ErrorAnalysisResult(boolean retryable, String reason) {
            this.retryable = retryable;
            this.reason = reason;
        }

        public static ErrorAnalysisResult retryable(String reason) {
            return new ErrorAnalysisResult(true, reason);
        }

        public static ErrorAnalysisResult nonRetryable(String reason) {
            return new ErrorAnalysisResult(false, reason);
        }

    }
}
