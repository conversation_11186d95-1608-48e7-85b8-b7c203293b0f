package ai.yourouter.chat.channel.response.gemini;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class GeminiUsageMetadata {
    private Integer promptTokenCount = 0;
    private Integer cachedContentTokenCount = 0;
    private Integer candidatesTokenCount = 0;
    private Integer toolUsePromptTokenCount = 0;
    private Integer thoughtsTokenCount = 0;
    private Integer totalTokenCount = 0;
    private List<ModalityTokenCount> promptTokensDetails = new ArrayList<>();
    private List<ModalityTokenCount> cacheTokensDetails = new ArrayList<>();
    private List<ModalityTokenCount> candidatesTokensDetails = new ArrayList<>();
    private List<ModalityTokenCount> toolUsePromptTokensDetails = new ArrayList<>();


    @Data
    public static class ModalityTokenCount {
        //MODALITY_UNSPECIFIED TEXT  IMAGE AUDIO VIDEO DOCUMENT
        private String modality; // e.g., "TEXT", "IMAGE", etc.
        private Integer tokenCount;
    }

}

