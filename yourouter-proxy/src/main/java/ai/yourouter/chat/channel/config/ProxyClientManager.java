package ai.yourouter.chat.channel.config;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.transport.ProxyProvider;

import java.net.URI;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 代理WebClient管理器
 * 负责缓存和管理带代理的WebClient实例，避免重复创建
 */
@Slf4j
@Component
public class ProxyClientManager implements DisposableBean {

    private final WebClient defaultHttpWebClient;
    private final WebClient defaultSseWebClient;
    
    // 缓存代理WebClient，key格式: "proxyUrl:clientType"
    private final ConcurrentHashMap<String, WebClient> proxyClientCache = new ConcurrentHashMap<>();
    
    // 缓存连接提供者，用于优雅关闭
    private final ConcurrentHashMap<String, ConnectionProvider> connectionProviderCache = new ConcurrentHashMap<>();

    public ProxyClientManager(@Qualifier("httpWebClient") WebClient httpWebClient,
                             @Qualifier("sseWebClient") WebClient sseWebClient) {
        this.defaultHttpWebClient = httpWebClient;
        this.defaultSseWebClient = sseWebClient;
        log.info("ProxyClientManager初始化完成");
    }

    /**
     * 获取WebClient实例（带缓存）
     *
     * @param proxyUrl 代理URL，如果为空则返回默认WebClient
     * @param isSSE    是否为SSE客户端
     * @return WebClient实例
     */
    public WebClient getWebClient(String proxyUrl, boolean isSSE) {
        // 如果没有代理配置，返回默认WebClient
        if (!StringUtils.hasText(proxyUrl)) {
            return isSSE ? defaultSseWebClient : defaultHttpWebClient;
        }

        String clientType = isSSE ? "sse" : "http";
        String cacheKey = proxyUrl + ":" + clientType;

        // 从缓存中获取
        WebClient cachedClient = proxyClientCache.get(cacheKey);
        if (cachedClient != null) {
            log.info("从缓存获取代理WebClient | 缓存键: {}", cacheKey);
            return cachedClient;
        }

        // 创建新的代理WebClient
        return createAndCacheProxyWebClient(proxyUrl, isSSE, cacheKey);
    }

    /**
     * 创建并缓存代理WebClient
     */
    private WebClient createAndCacheProxyWebClient(String proxyUrl, boolean isSSE, String cacheKey) {
        try {
            log.info("创建新的代理WebClient | 代理URL: {} | 类型: {}", proxyUrl, isSSE ? "SSE" : "HTTP");
            
            URI proxyUri = URI.create(proxyUrl);
            String scheme = proxyUri.getScheme();
            String host = proxyUri.getHost();
            int port = proxyUri.getPort();

            if (host == null || port == -1) {
                log.warn("代理URL格式无效，使用默认WebClient | 代理URL: {}", proxyUrl);
                return isSSE ? defaultSseWebClient : defaultHttpWebClient;
            }

            // 创建连接池
            String providerName = "proxy-" + cacheKey.replace(":", "-").replace("/", "-");
            ConnectionProvider provider = ConnectionProvider.builder(providerName)
                    .maxConnections(1000)
                    .maxIdleTime(Duration.ofSeconds(20))
                    .maxLifeTime(Duration.ofSeconds(60))
                    .pendingAcquireTimeout(Duration.ofSeconds(60))
                    .build();

            // 缓存连接提供者用于后续清理
            connectionProviderCache.put(cacheKey, provider);

            // 配置HttpClient with proxy
            HttpClient httpClient = HttpClient.create(provider)
                    .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, isSSE ? 5000 : 15000)
                    .doOnConnected(conn -> conn
                            .addHandlerLast(new ReadTimeoutHandler(isSSE ? 900 : 6000, TimeUnit.SECONDS))
                            .addHandlerLast(new WriteTimeoutHandler(isSSE ? 900 : 6000, TimeUnit.SECONDS)));

            // 根据代理类型配置代理
            if ("socks5".equalsIgnoreCase(scheme)) {
                httpClient = httpClient.proxy(proxy -> proxy.type(ProxyProvider.Proxy.SOCKS5).host(host).port(port));
            } else if ("http".equalsIgnoreCase(scheme) || "https".equalsIgnoreCase(scheme)) {
                httpClient = httpClient.proxy(proxy -> proxy.type(ProxyProvider.Proxy.HTTP).host(host).port(port));
            } else {
                log.warn("不支持的代理类型，使用默认WebClient | 代理类型: {}", scheme);
                return isSSE ? defaultSseWebClient : defaultHttpWebClient;
            }

            // 为HTTP客户端配置更大的内存限制
            WebClient.Builder builder = WebClient.builder()
                    .clientConnector(new ReactorClientHttpConnector(httpClient));
            
            if (!isSSE) {
                final int size = 16 * 1024 * 1024;
                final ExchangeStrategies strategies = ExchangeStrategies.builder()
                        .codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(size))
                        .build();
                builder.exchangeStrategies(strategies);
            }

            WebClient webClient = builder.build();
            
            // 缓存WebClient
            proxyClientCache.put(cacheKey, webClient);
            log.info("代理WebClient创建并缓存成功 | 缓存键: {} | 当前缓存大小: {}", cacheKey, proxyClientCache.size());
            
            return webClient;

        } catch (Exception e) {
            log.error("创建代理WebClient失败，使用默认WebClient | 代理URL: {} | 错误: {}", proxyUrl, e.getMessage());
            return isSSE ? defaultSseWebClient : defaultHttpWebClient;
        }
    }

    /**
     * 清理指定代理的WebClient缓存
     *
     * @param proxyUrl 代理URL
     */
    public void evictProxyClient(String proxyUrl) {
        if (!StringUtils.hasText(proxyUrl)) {
            return;
        }

        String httpKey = proxyUrl + ":http";
        String sseKey = proxyUrl + ":sse";

        evictCacheEntry(httpKey);
        evictCacheEntry(sseKey);
        
        log.info("清理代理WebClient缓存完成 | 代理URL: {}", proxyUrl);
    }

    /**
     * 清理单个缓存条目
     */
    private void evictCacheEntry(String cacheKey) {
        WebClient removedClient = proxyClientCache.remove(cacheKey);
        if (removedClient != null) {
            log.debug("移除代理WebClient缓存 | 缓存键: {}", cacheKey);
        }

        ConnectionProvider provider = connectionProviderCache.remove(cacheKey);
        if (provider != null) {
            try {
                provider.dispose();
                log.debug("关闭连接提供者 | 缓存键: {}", cacheKey);
            } catch (Exception e) {
                log.warn("关闭连接提供者失败 | 缓存键: {} | 错误: {}", cacheKey, e.getMessage());
            }
        }
    }

    /**
     * 清理所有代理WebClient缓存
     */
    public void evictAllProxyClients() {
        log.info("开始清理所有代理WebClient缓存 | 当前缓存大小: {}", proxyClientCache.size());
        
        proxyClientCache.clear();
        
        // 关闭所有连接提供者
        connectionProviderCache.forEach((key, provider) -> {
            try {
                provider.dispose();
                log.debug("关闭连接提供者 | 缓存键: {}", key);
            } catch (Exception e) {
                log.warn("关闭连接提供者失败 | 缓存键: {} | 错误: {}", key, e.getMessage());
            }
        });
        connectionProviderCache.clear();
        
        log.info("清理所有代理WebClient缓存完成");
    }

    /**
     * 获取当前缓存统计信息
     */
    public String getCacheStats() {
        return String.format("代理WebClient缓存统计 - 缓存条目数: %d, 连接提供者数: %d", 
                proxyClientCache.size(), connectionProviderCache.size());
    }

    /**
     * 定期监控缓存状态（每5分钟）
     */
    @Scheduled(fixedRate = 300000) // 5分钟
    public void monitorCacheStatus() {
        int cacheSize = proxyClientCache.size();
        int providerSize = connectionProviderCache.size();

        if (cacheSize > 0 || providerSize > 0) {
            log.info("代理WebClient缓存状态监控 | 缓存条目数: {} | 连接提供者数: {} | 时间: {}",
                    cacheSize, providerSize, LocalDateTime.now());
        }

        // 如果缓存过多，记录警告
        if (cacheSize > 50) {
            log.warn("代理WebClient缓存条目过多，建议检查是否有内存泄漏 | 当前缓存数: {}", cacheSize);
        }
    }

    /**
     * 定期清理空闲的代理客户端（每30分钟）
     * 注意：这里只是示例，实际使用中可能需要更复杂的LRU策略
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void cleanupIdleClients() {
        int initialSize = proxyClientCache.size();
        if (initialSize == 0) {
            return;
        }

        log.info("开始定期清理代理WebClient缓存 | 当前缓存数: {}", initialSize);

        // 这里可以添加更复杂的清理逻辑，比如基于最后使用时间
        // 目前只是记录状态，实际清理可以根据业务需求实现

        log.debug("定期清理完成 | 清理前: {} | 清理后: {}", initialSize, proxyClientCache.size());
    }

    /**
     * Spring Bean销毁时的清理工作
     */
    @Override
    public void destroy() {
        log.info("ProxyClientManager正在销毁，清理所有资源");
        evictAllProxyClients();
    }
}
