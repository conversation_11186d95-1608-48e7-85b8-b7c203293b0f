package ai.yourouter.chat.channel.response.openai;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * Represents the main API Response object.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChatResponse {

    /**
     * Unix timestamp (in seconds) of when this Response was created.
     * Represents the creation time as a numerical value.
     */
    @JsonProperty("created_at")
    private Long createdAt;

    /**
     * A number associated with the response, potentially for identification or sequencing.
     * Its specific purpose might depend on the API context.
     */
    private Integer number; // Assuming Integer based on common usage, adjust if needed

    /**
     * An error object returned when the model fails to generate a Response. Null if no error occurred.
     * Contains details about the failure.
     */
    private ErrorDetails error;

    /**
     * Unique identifier for this Response.
     * Used to uniquely identify this specific response instance.
     */
    private String id;

    /**
     * Details about why the response is incomplete. Null if the response is complete or not applicable.
     * Provides reasons for partial results.
     */
    @JsonProperty("incomplete_details")
    private IncompleteDetails incompleteDetails;

    /**
     * Inserts a system (or developer) message as the first item in the model's context. Null if not provided.
     * Can be used to set context or instructions for the model generation.
     */
    private String instructions;

    /**
     * An upper bound for the number of tokens that can be generated for a response. Null if no limit is set.
     * Includes visible output tokens and reasoning tokens.
     */
    @JsonProperty("max_output_tokens")
    private Integer maxOutputTokens;

    /**
     * Set of 16 key-value pairs that can be attached to an object. Null if no metadata provided.
     * Useful for storing additional structured information. Keys max length 64, Values max length 512.
     */
    private Map<String, String> metadata;

    /**
     * Model ID used to generate the response (e.g., "gpt-4o", "o1").
     * Specifies the AI model that processed the request.
     */
    private String model;

    /**
     * The object type of this resource. Always set to "response".
     * Identifies the type of this data structure.
     */
    private String object;

    /**
     * An array of content items generated by the model.
     * The structure and content depend on the model's output and requested tools.
     */
    private List<OutputItem> output;

    /**
     * SDK Only: Aggregated text output from all output_text items in the output array. Null if no text output.
     * Convenience property provided by some SDKs, may not be present in raw API response.
     */
    @JsonProperty("output_text")
    private String outputText;

    /**
     * Whether to allow the model to run tool calls in parallel.
     * Controls the execution behavior of tools.
     */
    @JsonProperty("parallel_tool_calls")
    private Boolean parallelToolCalls;

    /**
     * The unique ID of the previous response to the model. Null for the first turn.
     * Used to maintain conversation state in multi-turn interactions.
     */
    @JsonProperty("previous_response_id")
    private String previousResponseId;

    /**
     * Configuration options for reasoning models (o-series models only). Null if not applicable or not provided.
     * Allows tuning the reasoning process of specific models.
     */
    private ReasoningConfig reasoning;

    /**
     * The status of the response generation (e.g., "completed", "failed", "in_progress", "incomplete").
     * Indicates the current state of the response generation process.
     */
    private String status;

    /**
     * Sampling temperature to use, between 0 and 2. Null for default.
     * Controls randomness: higher values (e.g., 0.8) are more random, lower (e.g., 0.2) more deterministic.
     */
    private Double temperature;

    /**
     * Configuration options for a text response from the model. Null if not applicable.
     * Defines format requirements for the text output (plain text, JSON).
     */
    private TextConfig text;

    /**
     * How the model should select which tool(s) to use. Can be a String ("none", "auto", "required") or an Object (HostedTool, FunctionToolChoice).
     * Controls the invocation of tools defined in the 'tools' parameter. Represented as Object due to union type.
     */
    @JsonProperty("tool_choice")
    private Object toolChoice; // Union type (String | HostedTool | FunctionToolChoice) - Use Object or custom deserializer

    /**
     * An array of tools the model may call while generating a response. Null if no tools are provided.
     * Defines available built-in tools (like file search) or custom functions.
     */
    private List<Tool> tools;

    /**
     * Nucleus sampling parameter, between 0 and 1. Null for default.
     * Alternative to temperature; considers tokens comprising the top 'top_p' probability mass.
     */
    @JsonProperty("top_p")
    private Double topP;

    /**
     * The truncation strategy to use for the model response ("auto", "disabled"). Null for default ("disabled").
     * Manages how to handle responses exceeding the context window.
     */
    private String truncation;

    /**
     * Represents token usage details for the request and response. Null if usage info is not available.
     * Provides counts for input, output, and total tokens.
     */
    private UsageDetails usage;

    /**
     * A unique identifier representing your end-user. Null if not provided.
     * Helps OpenAI monitor and detect abuse.
     */
    private String user;


    //-------------------------------------------------------------------------
    // Inner Classes for Nested Structures
    //-------------------------------------------------------------------------

    public static void main(String[] args) throws JsonProcessingException {
        var chatResponse = new ObjectMapper().readValue("{\n" +
                "  \"id\": \"resp_67ccd2bed1ec8190b14f964abc0542670bb6a6b452d3795b\",\n" +
                "  \"object\": \"response\",\n" +
                "  \"created_at\": 1741476542,\n" +
                "  \"status\": \"completed\",\n" +
                "  \"error\": null,\n" +
                "  \"incomplete_details\": null,\n" +
                "  \"instructions\": null,\n" +
                "  \"max_output_tokens\": null,\n" +
                "  \"model\": \"gpt-4o-2024-08-06\",\n" +
                "  \"output\": [\n" +
                "    {\n" +
                "      \"type\": \"message\",\n" +
                "      \"id\": \"msg_67ccd2bf17f0819081ff3bb2cf6508e60bb6a6b452d3795b\",\n" +
                "      \"status\": \"completed\",\n" +
                "      \"role\": \"assistant\",\n" +
                "      \"content\": [\n" +
                "        {\n" +
                "          \"type\": \"output_text\",\n" +
                "          \"text\": \"In a peaceful grove beneath a silver moon, a unicorn named Lumina discovered a hidden pool that reflected the stars. As she dipped her horn into the water, the pool began to shimmer, revealing a pathway to a magical realm of endless night skies. Filled with wonder, Lumina whispered a wish for all who dream to find their own hidden magic, and as she glanced back, her hoofprints sparkled like stardust.\",\n" +
                "          \"annotations\": []\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"parallel_tool_calls\": true,\n" +
                "  \"previous_response_id\": null,\n" +
                "  \"reasoning\": {\n" +
                "    \"effort\": null,\n" +
                "    \"generate_summary\": null\n" +
                "  },\n" +
                "  \"store\": true,\n" +
                "  \"temperature\": 1.0,\n" +
                "  \"text\": {\n" +
                "    \"format\": {\n" +
                "      \"type\": \"text\"\n" +
                "    }\n" +
                "  },\n" +
                "  \"tool_choice\": \"auto\",\n" +
                "  \"tools\": [],\n" +
                "  \"top_p\": 1.0,\n" +
                "  \"truncation\": \"disabled\",\n" +
                "  \"usage\": {\n" +
                "    \"input_tokens\": 36,\n" +
                "    \"input_tokens_details\": {\n" +
                "      \"cached_tokens\": 0\n" +
                "    },\n" +
                "    \"output_tokens\": 87,\n" +
                "    \"output_tokens_details\": {\n" +
                "      \"reasoning_tokens\": 0\n" +
                "    },\n" +
                "    \"total_tokens\": 123\n" +
                "  },\n" +
                "  \"user\": null,\n" +
                "  \"metadata\": {}\n" +
                "}", ChatResponse.class);
        System.out.println(new ObjectMapper().writeValueAsString(chatResponse));
    }

    /**
     * Details of an error that occurred during response generation.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ErrorDetails {
        /**
         * The error code for the response.
         * A short identifier for the type of error.
         */
        private String code;

        /**
         * A human-readable description of the error.
         * Provides more context about what went wrong.
         */
        private String message;
    }

    /**
     * Details about why a response is incomplete.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IncompleteDetails {
        /**
         * The reason why the response is incomplete.
         * Explains the cause of the incompleteness.
         */
        private String reason;
    }

    /**
     * Base interface/class for polymorphic items in the 'output' list.
     * Uses the 'type' field to determine the specific subclass during deserialization.
     */
    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.PROPERTY,
            property = "type",
            visible = true // Keep the 'type' field in the Java object
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(value = OutputMessage.class, name = "message"),
            @JsonSubTypes.Type(value = FileSearchToolCallOutput.class, name = "file_search_call"),
            @JsonSubTypes.Type(value = FunctionToolCallOutput.class, name = "function_call"), // Note: type might differ slightly based on context (tool vs output)
            @JsonSubTypes.Type(value = WebSearchToolCallOutput.class, name = "web_search_call"),
            @JsonSubTypes.Type(value = ComputerToolCallOutput.class, name = "computer_call"),
            @JsonSubTypes.Type(value = ReasoningOutput.class, name = "reasoning")
    })
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    // Lombok annotations added to implementing classes
    public static abstract class OutputItem {
        /**
         * The type of the output item (e.g., "message", "file_search_call").
         * Determines the specific structure of the item.
         */
        private String type;
        /**
         * The status of the item (e.g., "in_progress", "completed", "incomplete"). Populated when items are returned via API.
         * Indicates the processing state of this specific output item.
         */
        private String status;
        /**
         * The unique ID of the output item.
         * Used to identify this specific part of the output. Can vary based on item type (message ID, tool call ID etc.)
         */
        private String id;
    }

    /**
     * Represents an output message from the model (typically assistant role).
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputMessage extends OutputItem {
        /**
         * The content of the output message.
         * Contains the actual text and potentially annotations or refusals.
         */
        private List<ContentItem> content;

        // id is inherited from OutputItem

        /**
         * The role of the output message. Always "assistant".
         * Indicates the source of the message.
         */
        private String role;

        // status is inherited from OutputItem
        // type is handled by JsonTypeInfo/JsonSubTypes ("message")
    }

    /**
     * Base interface/class for polymorphic items within the 'content' array of an OutputMessage.
     * Uses the 'type' field to determine the specific subclass.
     */
    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.PROPERTY,
            property = "type",
            visible = true
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(value = OutputText.class, name = "output_text"),
            @JsonSubTypes.Type(value = Refusal.class, name = "refusal")
            // Note: Other potential content types might exist based on API evolution.
    })
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static abstract class ContentItem {
        /**
         * The type of the content item (e.g., "output_text", "refusal").
         * Determines the structure and meaning of the content part.
         */
        private String type;
    }

    /**
     * Represents a text output segment within the message content.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputText extends ContentItem {
        /**
         * Annotations associated with the text output (e.g., citations, file paths).
         * Provides metadata or references linked to spans within the text.
         */
        private List<Annotation> annotations;

        /**
         * The actual text output from the model for this segment.
         * The core textual content generated.
         */
        private String text;

        // type is handled by JsonTypeInfo/JsonSubTypes ("output_text")
    }

    /**
     * Represents a refusal from the model within the message content.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Refusal extends ContentItem {
        /**
         * The refusal explanation from the model.
         * Describes why the model declined to generate a full response or perform an action.
         */
        private String refusal;

        // type is handled by JsonTypeInfo/JsonSubTypes ("refusal")
    }

    /**
     * Base interface/class for polymorphic items within the 'annotations' array of OutputText.
     * Uses the 'type' field to determine the specific subclass.
     */
    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.PROPERTY,
            property = "type",
            visible = true
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(value = FileCitation.class, name = "file_citation"),
            @JsonSubTypes.Type(value = URLCitation.class, name = "url_citation"),
            @JsonSubTypes.Type(value = FilePath.class, name = "file_path")
    })
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static abstract class Annotation {
        /**
         * The type of the annotation (e.g., "file_citation", "url_citation", "file_path").
         * Identifies the kind of annotation provided.
         */
        private String type;

        /**
         * The index indicating the position of the annotation relative to others (e.g., file index).
         * Provides positional context, meaning depends on annotation type.
         */
        private Integer index; // Common field, appears in FileCitation and FilePath
    }

    /**
     * Represents a citation to an uploaded file used by the model.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileCitation extends Annotation {
        /**
         * The ID of the cited file.
         * Links the citation to a specific file resource.
         */
        @JsonProperty("file_id")
        private String fileId;

        // index is inherited from Annotation
        // type is handled by JsonTypeInfo/JsonSubTypes ("file_citation")
    }

    /**
     * Represents a citation to a web resource (URL) used by the model.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class URLCitation extends Annotation {
        /**
         * The index of the last character of the URL citation in the message text.
         * Defines the end boundary of the cited text span.
         */
        @JsonProperty("end_index")
        private Integer endIndex;

        /**
         * The index of the first character of the URL citation in the message text.
         * Defines the start boundary of the cited text span.
         */
        @JsonProperty("start_index")
        private Integer startIndex;

        /**
         * The title of the web resource.
         * Provides a human-readable name for the cited URL.
         */
        private String title;

        // type is handled by JsonTypeInfo/JsonSubTypes ("url_citation")

        /**
         * The URL of the web resource.
         * The actual web address cited.
         */
        private String url;

        // index is not typically part of URLCitation schema based on description
    }

    /**
     * Represents a path to a file, potentially used or generated by the model.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FilePath extends Annotation {
        /**
         * The ID of the file being referenced by the path.
         * Links the path to a specific file resource.
         */
        @JsonProperty("file_id")
        private String fileId;

        // index is inherited from Annotation
        // type is handled by JsonTypeInfo/JsonSubTypes ("file_path")
    }

    /**
     * Represents the results of a file search tool call within the output.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileSearchToolCallOutput extends OutputItem {
        // id is inherited from OutputItem (tool call ID)

        /**
         * The queries used to search for files.
         * Shows the search terms submitted by the model.
         */
        private List<String> queries; // Assuming list of strings, adjust if structure is more complex

        // status is inherited from OutputItem
        // type is handled by JsonTypeInfo/JsonSubTypes ("file_search_call")

        /**
         * The results returned by the file search tool. Null if search failed or yielded no results.
         * Contains the documents found matching the queries.
         */
        private List<FileSearchResult> results;
    }

    /**
     * Represents a single result item from a file search tool call.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileSearchResult {
        /**
         * Set of 16 key-value pairs attached to the file result. Null if none.
         * Allows storing extra structured data about the search result (value can be string, number, boolean).
         */
        private Map<String, Object> attributes; // Value can be string, number, boolean

        /**
         * The unique ID of the file found.
         * Identifies the specific file resource.
         */
        @JsonProperty("file_id")
        private String fileId;

        /**
         * The name of the file found.
         * Human-readable filename.
         */
        private String filename;

        /**
         * The relevance score of the file (between 0 and 1).
         * Indicates how relevant the file is to the search query.
         */
        private Double score;

        /**
         * The text snippet retrieved from the file that matched the search.
         * Relevant content extracted from the file.
         */
        private String text;
    }

    /**
     * Represents a function tool call within the output.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FunctionToolCallOutput extends OutputItem {
        /**
         * A JSON string representing the arguments to pass to the function.
         * Contains the parameters the model decided to use for the function call.
         */
        private String arguments;

        /**
         * The unique ID of the function tool call generated by the model. This might be different from the top-level 'id' of the OutputItem.
         * Specific identifier for this instance of the function call.
         */
        @JsonProperty("call_id")
        private String callId;

        /**
         * The name of the function the model wants to run.
         * Matches the name defined in the 'tools' parameter.
         */
        private String name;

        // type is handled by JsonTypeInfo/JsonSubTypes ("function_call")
        // id is inherited from OutputItem (tool call ID)
        // status is inherited from OutputItem
    }

    /**
     * Represents the results of a web search tool call within the output.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WebSearchToolCallOutput extends OutputItem {
        // id is inherited from OutputItem (tool call ID)
        // status is inherited from OutputItem
        // type is handled by JsonTypeInfo/JsonSubTypes ("web_search_call")
        // Note: Specific results structure for web search is not detailed here, add if needed.
    }

    /**
     * Represents a computer use tool call within the output.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComputerToolCallOutput extends OutputItem {
        /**
         * The specific action requested or performed by the computer tool. Structure depends on the action.
         * Represented as Object due to unspecified polymorphic nature.
         */
        private Object action; // Structure varies, use Object or define specific action classes if known

        /**
         * An identifier used when responding to the tool call with output. This might be different from the top-level 'id'.
         * Specific identifier related to the action or response flow.
         */
        @JsonProperty("call_id")
        private String callId;

        // id is inherited from OutputItem (tool call ID)

        /**
         * Pending safety checks associated with the computer call. Null if none.
         * Lists checks that need confirmation or resolution.
         */
        @JsonProperty("pending_safety_checks")
        private List<PendingSafetyCheck> pendingSafetyChecks;

        // status is inherited from OutputItem
        // type is handled by JsonTypeInfo/JsonSubTypes ("computer_call")
    }

    /**
     * Represents a pending safety check for a computer tool call.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PendingSafetyCheck {
        /**
         * The type code of the pending safety check.
         * Identifies the nature of the check required.
         */
        private String code;

        /**
         * The unique ID of the pending safety check.
         * Identifier for this specific check instance.
         */
        private String id;

        /**
         * Details about the pending safety check.
         * Provides context or instructions related to the check.
         */
        private String message;
    }

    /**
     * Represents reasoning or chain-of-thought information within the output.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReasoningOutput extends OutputItem {
        // id is inherited from OutputItem (reasoning content ID)

        /**
         * An array containing summaries or steps of the reasoning process.
         * Provides insights into how the model arrived at the response.
         */
        private List<ReasoningSummary> summary;

        // type is handled by JsonTypeInfo/JsonSubTypes ("reasoning")
        // status is inherited from OutputItem
    }

    /**
     * Represents a summary text segment within the reasoning output.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReasoningSummary {
        /**
         * A short summary of the reasoning used by the model.
         * Textual description of a reasoning step or conclusion.
         */
        private String text;

        /**
         * The type of the summary object. Always "summary_text".
         * Identifies this object as a text summary within reasoning.
         */
        private String type; // Always "summary_text"
    }

    /**
     * Configuration options specific to reasoning models (o-series).
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ReasoningConfig {
        /**
         * Constrains effort on reasoning ("low", "medium", "high"). Null for default.
         * Affects speed and token usage for reasoning.
         */
        private String effort;

        /**
         * Requests a summary of reasoning ("concise", "detailed"). Null for no summary. (computer_use_preview only).
         * Useful for debugging model's thought process.
         */
        @JsonProperty("generate_summary")
        private String generateSummary;
    }

    /**
     * Configuration options for the text response format.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TextConfig {
        /**
         * Specifies the required format for the model's output (e.g., plain text, JSON schema).
         * Determines the structure the model must adhere to.
         */
        private Format format;
    }

    /**
     * Base interface/class for polymorphic response format options.
     * Uses the 'type' field to determine the specific format configuration.
     */
    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.PROPERTY,
            property = "type",
            visible = true
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(value = TextFormat.class, name = "text"),
            @JsonSubTypes.Type(value = JsonSchemaFormat.class, name = "json_schema"),
            @JsonSubTypes.Type(value = JsonObjectFormat.class, name = "json_object")
    })
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static abstract class Format {
        /**
         * The type of response format being defined (e.g., "text", "json_schema").
         * Identifies the specific format configuration class.
         */
        private String type;
    }

    /**
     * Specifies a plain text response format (default).
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TextFormat extends Format {
        // type is handled by JsonTypeInfo/JsonSubTypes ("text")
    }

    /**
     * Specifies a JSON schema response format for structured outputs.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JsonSchemaFormat extends Format {
        /**
         * The name of the response format. Max length 64.
         * A label for this specific JSON schema format.
         */
        private String name;

        /**
         * The JSON Schema object describing the desired output structure.
         * Represented as Object or Map<String, Object> for flexibility.
         */
        private Object schema; // Represents a JSON Schema object

        // type is handled by JsonTypeInfo/JsonSubTypes ("json_schema")

        /**
         * A description for the model about what the format is for.
         * Helps the model understand how to use the schema.
         */
        private String description;

        /**
         * Whether to enforce strict adherence to the schema. Null for default (usually false).
         * If true, model must strictly follow the schema subset.
         */
        private Boolean strict;
    }

    // --- Tool Choice Specific Classes (used in 'toolChoice' field if it's an object) ---

    /**
     * Specifies the older JSON object response format.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class JsonObjectFormat extends Format {
        // type is handled by JsonTypeInfo/JsonSubTypes ("json_object")
    }

    /**
     * Represents forcing the model to use a specific built-in tool.
     * Part of the 'toolChoice' union type.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HostedToolChoice { // Used if toolChoice is an object specifying a built-in tool
        /**
         * The type of hosted tool to use (e.g., "file_search", "web_search_preview").
         * Identifies the specific built-in capability.
         */
        private String type; // e.g., "file_search", "web_search_preview", "computer_use_preview"
    }


    // --- Tool Definition Classes (used in 'tools' list) ---

    /**
     * Represents forcing the model to call a specific function.
     * Part of the 'toolChoice' union type.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FunctionToolChoice { // Used if toolChoice is an object specifying a function
        /**
         * The name of the function to force call.
         * Must match a function defined in the 'tools' list.
         */
        private String name;

        /**
         * The type identifier for function calling choice. Always "function".
         * Distinguishes this choice type.
         */
        private String type; // Always "function"
    }

    /**
     * Base interface/class for polymorphic tool definitions in the 'tools' list.
     * Uses the 'type' field to determine the specific tool configuration.
     */
    @JsonTypeInfo(
            use = JsonTypeInfo.Id.NAME,
            include = JsonTypeInfo.As.PROPERTY,
            property = "type",
            visible = true
    )
    @JsonSubTypes({
            @JsonSubTypes.Type(value = FileSearchTool.class, name = "file_search"),
            @JsonSubTypes.Type(value = FunctionTool.class, name = "function"),
            @JsonSubTypes.Type(value = ComputerUseTool.class, name = "computer_use_preview"),
            // Web search has multiple possible type strings
            @JsonSubTypes.Type(value = WebSearchTool.class, name = "web_search_preview"),
            @JsonSubTypes.Type(value = WebSearchTool.class, name = "web_search_preview_2025_03_11") // Map both names to the same class
    })
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static abstract class Tool {
        /**
         * The type of the tool being defined (e.g., "file_search", "function").
         * Identifies the specific tool configuration class.
         */
        private String type;
    }

    /**
     * Defines the configuration for the built-in file search tool.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileSearchTool extends Tool {
        // type is handled by JsonTypeInfo/JsonSubTypes ("file_search")

        /**
         * The IDs of the vector stores to search. Null to search all.
         * Specifies the data sources for the search.
         */
        @JsonProperty("vector_store_ids")
        private List<String> vectorStoreIds;

        /**
         * A filter to apply based on file attributes. Null for no filter.
         * Can be a ComparisonFilter or CompoundFilter. Represented as Object due to polymorphism without explicit type field in base filter.
         */
        private Object filters; // Can be ComparisonFilter or CompoundFilter

        /**
         * The maximum number of search results to return (1-50). Null for default.
         * Limits the quantity of results provided.
         */
        @JsonProperty("max_num_results")
        private Integer maxNumResults;

        /**
         * Ranking options for the search. Null for default ranking.
         * Fine-tunes how search results are ordered and thresholded.
         */
        @JsonProperty("ranking_options")
        private RankingOptions rankingOptions;
    }

    /**
     * Filter for comparing a file attribute to a value. Used within FileSearchTool's 'filters'.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComparisonFilter { // Not extending Filter base, as it seems structurally distinct in JSON
        /**
         * The attribute key to compare against.
         * Specifies the file metadata field for comparison.
         */
        private String key;

        /**
         * The comparison operator ("eq", "ne", "gt", "gte", "lt", "lte").
         * Defines the type of comparison to perform.
         */
        private String type; // Note: Here 'type' refers to the comparison operation

        /**
         * The value to compare against the key (String, Number, or Boolean).
         * The target value for the comparison.
         */
        private Object value; // String, Number, or Boolean
    }

    /**
     * Filter for combining multiple filters using AND or OR logic. Used within FileSearchTool's 'filters'.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class CompoundFilter { // Not extending Filter base
        /**
         * Array of filters to combine (can contain ComparisonFilter or other CompoundFilter objects).
         * The list of sub-filters to evaluate.
         */
        private List<Object> filters; // List of ComparisonFilter or CompoundFilter

        /**
         * The logical operator to combine filters ("and", "or").
         * Defines how the sub-filters are combined.
         */
        private String type; // Note: Here 'type' refers to the logical operation
    }

    /**
     * Ranking options for file search results.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RankingOptions {
        /**
         * The ranker to use for the file search. Null for default.
         * Specifies the ranking algorithm.
         */
        private String ranker;

        /**
         * The score threshold (0-1) for results. Null for no threshold.
         * Filters results based on minimum relevance score.
         */
        @JsonProperty("score_threshold")
        private Double scoreThreshold;
    }

    /**
     * Defines a custom function tool that the model can call.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FunctionTool extends Tool {
        /**
         * The name of the function to call.
         * Must uniquely identify the function within the 'tools' list.
         */
        private String name;

        /**
         * A JSON Schema object describing the parameters the function accepts.
         * Defines the expected arguments structure. Represented as Object for flexibility.
         */
        private Object parameters; // JSON Schema Object

        /**
         * Whether to enforce strict parameter validation based on the schema. Default true.
         * Controls how strictly the model must adhere to parameter definitions.
         */
        private Boolean strict; // Default: true

        // type is handled by JsonTypeInfo/JsonSubTypes ("function")

        /**
         * A description of what the function does. Null if not provided.
         * Used by the model to decide when to call the function.
         */
        private String description;
    }

    /**
     * Defines the configuration for the built-in computer use tool.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ComputerUseTool extends Tool {
        /**
         * The height of the virtual computer display in pixels.
         * Defines the screen resolution for the tool's environment.
         */
        @JsonProperty("display_height")
        private Integer displayHeight; // Assuming Integer, could be Double

        /**
         * The width of the virtual computer display in pixels.
         * Defines the screen resolution for the tool's environment.
         */
        @JsonProperty("display_width")
        private Integer displayWidth; // Assuming Integer, could be Double

        /**
         * The type of computer environment to control (specifics depend on API).
         * Defines the operating system or context for the tool.
         */
        private String environment;

        // type is handled by JsonTypeInfo/JsonSubTypes ("computer_use_preview")
    }

    /**
     * Defines the configuration for the built-in web search tool.
     */
    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WebSearchTool extends Tool {
        // type is handled by JsonTypeInfo/JsonSubTypes ("web_search_preview", "web_search_preview_2025_03_11")

        /**
         * Guidance on context window usage for search ("low", "medium", "high"). Null for default ("medium").
         * Influences how much context is allocated for search results.
         */
        @JsonProperty("search_context_size")
        private String searchContextSize;

        /**
         * Approximate location parameters for the search. Null if not provided.
         * Helps localize search results.
         */
        @JsonProperty("user_location")
        private UserLocation userLocation;
    }


    // --- Usage Details Classes ---

    /**
     * Approximate user location details for web search.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserLocation {
        /**
         * The type of location approximation. Always "approximate".
         * Identifies the method used for location data.
         */
        private String type; // Always "approximate"

        /**
         * Free text input for the user's city (e.g., "San Francisco").
         * City name for localization.
         */
        private String city;

        /**
         * The two-letter ISO country code (e.g., "US").
         * Country code for localization.
         */
        private String country;

        /**
         * Free text input for the user's region (e.g., "California").
         * State or region name for localization.
         */
        private String region;

        /**
         * The IANA timezone of the user (e.g., "America/Los_Angeles").
         * Timezone for localization and time-sensitive queries.
         */
        private String timezone;
    }

    /**
     * Contains details about token usage for the API call.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UsageDetails {
        /**
         * The number of tokens in the input prompt.
         * Measures the length of the input provided to the model.
         */
        @JsonProperty("input_tokens")
        private Integer inputTokens;

        /**
         * A detailed breakdown of input tokens, including cached tokens. Null if not applicable.
         * Provides more granularity on input token sources.
         */
        @JsonProperty("input_tokens_details")
        private InputTokensDetails inputTokensDetails;

        /**
         * The number of tokens generated in the response.
         * Measures the length of the model's output.
         */
        @JsonProperty("output_tokens")
        private Integer outputTokens;

        /**
         * A detailed breakdown of output tokens, including reasoning tokens. Null if not applicable.
         * Provides more granularity on different types of output tokens.
         */
        @JsonProperty("output_tokens_details")
        private OutputTokensDetails outputTokensDetails;

        /**
         * The total number of tokens used (input + output).
         * Represents the overall token consumption for the request.
         */
        @JsonProperty("total_tokens")
        private Integer totalTokens;
    }

    /**
     * Detailed breakdown of input tokens.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InputTokensDetails {
        /**
         * The number of input tokens retrieved from the cache.
         * Indicates usage of prompt caching features.
         */
        @JsonProperty("cached_tokens")
        private Integer cachedTokens;
    }

    /**
     * Detailed breakdown of output tokens.
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputTokensDetails {
        /**
         * The number of tokens used specifically for reasoning steps.
         * Applicable to models that perform explicit reasoning.
         */
        @JsonProperty("reasoning_tokens")
        private Integer reasoningTokens;
    }
}