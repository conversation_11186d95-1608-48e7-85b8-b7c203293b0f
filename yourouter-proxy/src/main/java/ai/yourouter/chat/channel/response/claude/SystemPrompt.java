package ai.yourouter.chat.channel.response.claude;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemPrompt {

    private String type;
    private String text;

    /**
     * 创建默认的Claude系统提示
     */
    public static SystemPrompt createDefault() {
        return new SystemPrompt("text", "You are Claude Code, Anthropic's official CLI for Claude.");
    }

    /**
     * 创建自定义文本系统提示
     */
    public static SystemPrompt createTextPrompt(String text) {
        return new SystemPrompt("text", text);
    }

    /**
     * 创建自定义类型和文本的系统提示
     */
    public static SystemPrompt createCustomPrompt(String type, String text) {
        return new SystemPrompt(type, text);
    }
}
