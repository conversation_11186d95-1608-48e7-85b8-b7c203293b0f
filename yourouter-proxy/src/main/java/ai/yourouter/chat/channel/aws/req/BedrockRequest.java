package ai.yourouter.chat.channel.aws.req;

public class BedrockRequest {
    private String accessKey;
    private String secretKey;
    private String region;
    private String service;
    private String requestBody;
    private String timestamp;
    private String invocationId;

    public BedrockRequest(String accessKey, String secretKey, String region, String service, String requestBody, String timestamp, String invocationId) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.region = region;
        this.service = service;
        this.requestBody = requestBody;
        this.timestamp = timestamp;
        this.invocationId = invocationId;
    }

    // Getters
    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public String getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(String requestBody) {
        this.requestBody = requestBody;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getInvocationId() {
        return invocationId;
    }

    public void setInvocationId(String invocationId) {
        this.invocationId = invocationId;
    }
}

