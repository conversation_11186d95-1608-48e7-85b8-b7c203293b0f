package ai.yourouter.chat.channel.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * ProxyClientManager管理控制器
 * 提供代理WebClient缓存的管理接口
 */
@Slf4j
@RestController
@RequestMapping("/api/proxy-client")
@RequiredArgsConstructor
public class ProxyClientManagerController {

    private final ProxyClientManager proxyClientManager;

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/stats")
    public String getCacheStats() {
        return proxyClientManager.getCacheStats();
    }

    /**
     * 清理指定代理的WebClient缓存
     */
    @DeleteMapping("/evict")
    public String evictProxyClient(@RequestParam String proxyUrl) {
        try {
            proxyClientManager.evictProxyClient(proxyUrl);
            return "成功清理代理WebClient缓存: " + proxyUrl;
        } catch (Exception e) {
            log.error("清理代理WebClient缓存失败 | 代理URL: {} | 错误: {}", proxyUrl, e.getMessage());
            return "清理失败: " + e.getMessage();
        }
    }

    /**
     * 清理所有代理WebClient缓存
     */
    @DeleteMapping("/evict-all")
    public String evictAllProxyClients() {
        try {
            proxyClientManager.evictAllProxyClients();
            return "成功清理所有代理WebClient缓存";
        } catch (Exception e) {
            log.error("清理所有代理WebClient缓存失败 | 错误: {}", e.getMessage());
            return "清理失败: " + e.getMessage();
        }
    }
}
