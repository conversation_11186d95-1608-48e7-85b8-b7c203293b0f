package ai.yourouter.chat.channel.response.gemini;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Gemini 原生 API 响应模型
 * 对应 Gemini generateContent API 的响应格式
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class GeminiResponse {

    /**
     * 候选响应列表
     */
    @JsonProperty("candidates")
    private List<Candidate> candidates;

    /**
     * 提示反馈信息
     */
    @JsonProperty("promptFeedback")
    private PromptFeedback promptFeedback;

    /**
     * 使用量元数据
     */
    @JsonProperty("usageMetadata")
    private GeminiUsageMetadata usageMetadata;

    /**
     * 候选响应
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Candidate {
        
        /**
         * 内容
         */
        @JsonProperty("content")
        private Content content;

        /**
         * 完成原因
         */
        @JsonProperty("finishReason")
        private String finishReason;

        /**
         * 索引
         */
        @JsonProperty("index")
        private Integer index;

        /**
         * 安全评级
         */
        @JsonProperty("safetyRatings")
        private List<SafetyRating> safetyRatings;
    }

    /**
     * 内容
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Content {
        
        /**
         * 部分列表
         */
        @JsonProperty("parts")
        private List<Part> parts;

        /**
         * 角色
         */
        @JsonProperty("role")
        private String role;
    }

    /**
     * 部分
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Part {
        
        /**
         * 文本内容
         */
        @JsonProperty("text")
        private String text;

        /**
         * 函数调用
         */
        @JsonProperty("functionCall")
        private FunctionCall functionCall;

        /**
         * 函数响应
         */
        @JsonProperty("functionResponse")
        private FunctionResponse functionResponse;
    }

    /**
     * 函数调用
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FunctionCall {
        
        /**
         * 函数名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 参数
         */
        @JsonProperty("args")
        private Object args;
    }

    /**
     * 函数响应
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class FunctionResponse {
        
        /**
         * 函数名称
         */
        @JsonProperty("name")
        private String name;

        /**
         * 响应内容
         */
        @JsonProperty("response")
        private Object response;
    }

    /**
     * 安全评级
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class SafetyRating {
        
        /**
         * 类别
         */
        @JsonProperty("category")
        private String category;

        /**
         * 概率
         */
        @JsonProperty("probability")
        private String probability;
    }

    /**
     * 提示反馈
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PromptFeedback {
        
        /**
         * 阻止原因
         */
        @JsonProperty("blockReason")
        private String blockReason;

        /**
         * 安全评级
         */
        @JsonProperty("safetyRatings")
        private List<SafetyRating> safetyRatings;
    }
}
