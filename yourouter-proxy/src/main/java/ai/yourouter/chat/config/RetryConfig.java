package ai.yourouter.chat.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * 重试配置类
 */
@Data
@Component
@ConfigurationProperties(prefix = "kmg.retry")
public class RetryConfig {

    /**
     * 重试次数，默认3次
     */
    private int maxAttempts;

    /**
     * 初始延迟时间，默认1秒
     */
    private Duration initialDelay;

    /**
     * 最大延迟时间，默认10秒
     */
    private Duration maxDelay;

    /**
     * 延迟倍数，默认2倍
     */
    private double multiplier;

    /**
     * 是否启用重试，默认启用
     */
    private boolean enabled;
}
