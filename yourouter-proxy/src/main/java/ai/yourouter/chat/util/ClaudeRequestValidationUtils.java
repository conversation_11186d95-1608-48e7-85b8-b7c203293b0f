package ai.yourouter.chat.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * Claude 请求验证工具类
 * 专门处理 Claude Messages API 的请求参数验证
 */
@Slf4j
public class ClaudeRequestValidationUtils {

    private ClaudeRequestValidationUtils() {
        // 工具类禁止实例化
    }

    /**
     * 验证 Claude Messages 请求参数
     */
    public static boolean isValidClaudeMessagesRequest(LinkedHashMap<String, Object> req) {
        if (req == null || req.isEmpty()) {
            log.warn("Claude 请求参数为空");
            return false;
        }
        
        // 验证 model 参数
        Object model = req.get("model");
        if (model == null || !StringUtils.hasText(model.toString())) {
            log.warn("Claude model 参数缺失或为空");
            return false;
        }
        
        // 验证 messages 参数
        Object messages = req.get("messages");
        if (messages == null) {
            log.warn("Claude messages 参数缺失");
            return false;
        }
        
        if (!(messages instanceof List)) {
            log.warn("<PERSON> messages 参数格式错误，应为数组");
            return false;
        }
        
        @SuppressWarnings("unchecked")
        List<Object> messagesList = (List<Object>) messages;
        if (messagesList.isEmpty()) {
            log.warn("Claude messages 参数为空数组");
            return false;
        }
        
        // 验证每个消息的格式
        for (Object messageObj : messagesList) {
            if (!isValidClaudeMessage(messageObj)) {
                return false;
            }
        }
        
        // 验证 max_tokens 参数（Claude API 必需）
        Object maxTokens = req.get("max_tokens");
        if (maxTokens == null) {
            log.warn("Claude max_tokens 参数缺失");
            return false;
        }
        
        if (!(maxTokens instanceof Number)) {
            log.warn("Claude max_tokens 参数格式错误，应为数字");
            return false;
        }
        
        return true;
    }

    /**
     * 验证单个 Claude 消息格式
     */
    private static boolean isValidClaudeMessage(Object messageObj) {
        if (!(messageObj instanceof Map)) {
            log.warn("Claude 消息格式错误，应为对象");
            return false;
        }
        
        @SuppressWarnings("unchecked")
        Map<String, Object> message = (Map<String, Object>) messageObj;
        
        // 验证 role 参数
        Object role = message.get("role");
        if (role == null || !StringUtils.hasText(role.toString())) {
            log.warn("Claude 消息 role 参数缺失或为空");
            return false;
        }
        
        String roleStr = role.toString();
        if (!("user".equals(roleStr) || "assistant".equals(roleStr) || "system".equals(roleStr))) {
            log.warn("Claude 消息 role 参数值无效: {}", roleStr);
            return false;
        }
        
        // 验证 content 参数
        Object content = message.get("content");
        if (content == null) {
            log.warn("Claude 消息 content 参数缺失");
            return false;
        }
        
        // content 可以是字符串或数组
        if (!(content instanceof String) && !(content instanceof List)) {
            log.warn("Claude 消息 content 参数格式错误");
            return false;
        }
        
        return true;
    }

    /**
     * 解析 Claude stream 参数
     */
    public static boolean parseClaudeStreamParameter(LinkedHashMap<String, Object> req) {
        Object streamObj = req.getOrDefault("stream", false);
        if (streamObj instanceof Boolean) {
            return (Boolean) streamObj;
        }
        if (streamObj instanceof String) {
            return Boolean.parseBoolean((String) streamObj);
        }
        return false;
    }

    /**
     * 创建 Claude 请求副本，避免修改原始请求
     */
    public static HashMap<String, Object> createClaudeRequestCopy(LinkedHashMap<String, Object> original) {
        return new HashMap<>(original);
    }

    /**
     * 确保 Claude 请求包含必要的默认值
     */
    public static void ensureClaudeDefaultValues(HashMap<String, Object> req, boolean isStream) {
        req.put("stream", isStream);
        
    }

    /**
     * 转换 OpenAI 格式请求为 Claude 格式
     * 用于兼容性处理
     */
    public static HashMap<String, Object> convertOpenAIToClaudeFormat(HashMap<String, Object> openaiRequest) {
        HashMap<String, Object> claudeRequest = new HashMap<>(openaiRequest);
        
        // 移除 OpenAI 特有的参数
        claudeRequest.remove("frequency_penalty");
        claudeRequest.remove("presence_penalty");
        claudeRequest.remove("logit_bias");
        claudeRequest.remove("logprobs");
        claudeRequest.remove("top_logprobs");
        claudeRequest.remove("n");
        claudeRequest.remove("response_format");
        claudeRequest.remove("seed");
        claudeRequest.remove("service_tier");
        claudeRequest.remove("stop");
        claudeRequest.remove("stream_options");
        claudeRequest.remove("tool_choice");
        claudeRequest.remove("tools");
        claudeRequest.remove("parallel_tool_calls");
        claudeRequest.remove("user");
        
        // 确保包含 Claude 必需的参数
        if (!claudeRequest.containsKey("max_tokens")) {
            claudeRequest.put("max_tokens", 4096);
        }
        
        return claudeRequest;
    }
}
