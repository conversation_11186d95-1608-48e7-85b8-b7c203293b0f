package ai.yourouter.chat.util;

import ai.yourouter.common.context.ChatContext;
import ai.yourouter.common.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

/**
 * 聊天日志工具类 - 统一日志记录格式
 */
@Slf4j
public class ChatLogUtils {

    private ChatLogUtils() {
        // 工具类禁止实例化
    }

    /**
     * 记录请求开始日志
     */
    public static void logRequestStart(ChatContext chatContext, Object bestKey, boolean isStream) {
        String requestType = isStream ? "流式请求" : "非流式请求";
        log.info("开始{} | 用户ID: {} | 模型: {} | 密钥: {} | 请求ID: {}",
                requestType, 
                chatContext.getChatUserInfo().getCharactersId(), 
                chatContext.apiModelName(), 
                JsonUtils.toJSONString(bestKey),
                chatContext.getRequestId());
    }

    /**
     * 记录请求完成日志
     */
    public static void logRequestComplete(ChatContext chatContext, boolean isStream) {
        String requestType = isStream ? "流式请求" : "非流式请求";
        long duration = chatContext.getChatRequestStatistic().consumerTime();
        log.info("{}完成 | 用户ID: {} | 模型: {} | 耗时: {}ms | 请求ID: {}",
                requestType,
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                duration,
                chatContext.getRequestId());
    }

    /**
     * 记录请求错误日志
     */
    public static void logRequestError(Throwable error, ChatContext chatContext, boolean isStream) {
        String requestType = isStream ? "流式请求" : "非流式请求";
        long duration = chatContext.getChatRequestStatistic().consumerTime();
        log.error("{}发生错误 | 用户ID: {} | 模型: {} | 耗时: {}ms | 请求ID: {} | 错误: {}", 
                requestType,
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                duration,
                chatContext.getRequestId(),
                error.getMessage());
    }

    /**
     * 记录重试日志
     */
    public static void logRetryAttempt(String requestType, ChatContext chatContext, long attempt, Throwable failure) {
        log.warn("{}重试 | 用户ID: {} | 模型: {} | 供应商: {} | 第{}次重试 | 请求ID: {} | 错误: {}",
                requestType, 
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                chatContext.getChatRequestStatistic().vendorHeader(),
                attempt,
                chatContext.getRequestId(),
                failure.getMessage());
    }

    /**
     * 记录重试完成日志
     */
    public static void logRetryComplete(String requestType, ChatContext chatContext, long attempt) {
        log.info("{}重试完成 | 用户ID: {} | 模型: {} | 供应商: {} | 第{}次重试 | 请求ID: {}",
                requestType,
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                chatContext.getChatRequestStatistic().vendorHeader(),
                attempt,
                chatContext.getRequestId());
    }

    /**
     * 记录重试耗尽日志
     */
    public static void logRetryExhausted(String requestType, ChatContext chatContext, long totalRetries, Throwable finalError) {
        log.error("{}重试耗尽 | 用户ID: {} | 模型: {} | 供应商: {} | 总重试次数: {} | 请求ID: {} | 最终错误: {}",
                requestType,
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                chatContext.getChatRequestStatistic().vendorHeader(),
                totalRetries,
                chatContext.getRequestId(),
                finalError.getMessage());
    }

    /**
     * 记录使用量统计发布日志
     */
    public static void logUsageStatistics(ChatContext chatContext, boolean success) {
        if (success) {
            log.debug("使用量统计发布成功 | 用户ID: {} | 模型: {} | 请求ID: {}",
                    chatContext.getChatUserInfo().getCharactersId(),
                    chatContext.apiModelName(),
                    chatContext.getRequestId());
        } else {
            log.error("使用量统计发布失败 | 用户ID: {} | 模型: {} | 请求ID: {}",
                    chatContext.getChatUserInfo().getCharactersId(),
                    chatContext.apiModelName(),
                    chatContext.getRequestId());
        }
    }

    /**
     * 记录客户端断开连接日志
     */
    public static void logClientDisconnected(ChatContext chatContext) {
        log.debug("客户端断开连接 | 用户ID: {} | 模型: {} | 请求ID: {}",
                chatContext.getChatUserInfo().getCharactersId(),
                chatContext.apiModelName(),
                chatContext.getRequestId());
    }
}
