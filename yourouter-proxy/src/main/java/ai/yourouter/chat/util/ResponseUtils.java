package ai.yourouter.chat.util;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import reactor.core.CorePublisher;

/**
 * 响应工具类 - 统一响应构建逻辑
 */
public class ResponseUtils {

    private ResponseUtils() {
        // 工具类禁止实例化
    }

    /**
     * 创建流式响应
     */
    public static ResponseEntity<? extends CorePublisher<?>> createStreamResponse(CorePublisher<?> body) {
        HttpHeaders headers = createHeaders(MediaType.TEXT_EVENT_STREAM);
        return ResponseEntity.status(HttpStatus.OK)
                .headers(headers)
                .body(body);
    }

    /**
     * 创建非流式响应
     */
    public static ResponseEntity<? extends CorePublisher<?>> createNonStreamResponse(CorePublisher<?> body) {
        HttpHeaders headers = createHeaders(MediaType.APPLICATION_JSON);
        return ResponseEntity.status(HttpStatus.OK)
                .headers(headers)
                .body(body);
    }

    /**
     * 创建错误响应
     */
    public static ResponseEntity<? extends CorePublisher<?>> createErrorResponse(HttpStatus status) {
        return ResponseEntity.status(status).build();
    }

    /**
     * 创建响应头
     */
    private static HttpHeaders createHeaders(MediaType contentType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(contentType);
        return headers;
    }
}
