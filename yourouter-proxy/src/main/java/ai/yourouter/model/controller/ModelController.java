package ai.yourouter.model.controller;

import ai.yourouter.model.service.ModelService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping(value = "/v1")
@RequiredArgsConstructor
public class ModelController {

    private final ModelService modelService;


    @RequestMapping(value = "/models")
    public Map<String,Object> models() {
        return modelService.models();
    }

}
