package ai.yourouter.model.service.impl;

import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.jpa.system.model.info.bean.SystemModelInfo;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import ai.yourouter.model.service.ModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class ModelServiceImpl implements ModelService {

    private final SystemModelInfoRepository systemModelInfoRepository;


    @Override
    public Map<String, Object> models() {
        HashMap<String, Object> models = new HashMap<>();
        List<Map<String,Object>> modelList = new ArrayList<>();
        models.put("object", "list");

        List<SystemModelInfo> systemModelInfoList = systemModelInfoRepository.findSystemModelInfosByStatuses(PermissionStatusEnum.AVAILABLE.getCode());
        ListIterator<SystemModelInfo> systemModelInfoListIterator = systemModelInfoList.listIterator();
        while (systemModelInfoListIterator.hasNext()) {
            SystemModelInfo systemModelInfo = systemModelInfoListIterator.next();
            Map<String, Object> model = new HashMap<>();
            model.put("id",      systemModelInfo.getModelName());
            model.put("object",  "model");
            model.put("created", systemModelInfo.getCreateTime());
            model.put("owned_by",systemModelInfo.getManufacturerName());
            modelList.add(model);
        }
        models.put("data", modelList);
        return models;
    }
}
