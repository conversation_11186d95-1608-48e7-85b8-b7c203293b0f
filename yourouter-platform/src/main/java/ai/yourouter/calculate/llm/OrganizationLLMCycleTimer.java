package ai.yourouter.calculate.llm;


import ai.yourouter.common.utils.OrganizationBalanceUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.SystemClock;
import ai.yourouter.jpa.organization.cycle.repository.OrganizationLLMCycleRepository;
import ai.yourouter.jpa.organization.dailybill.repository.OrganizationLLMDailyBillRepository;
import ai.yourouter.jpa.organization.statistics.repository.OrganizationLLMStatisticsRepository;
import ai.yourouter.jpa.system.model.info.bean.SystemModelInfo;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class OrganizationLLMCycleTimer {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final SystemModelInfoRepository systemModelInfoRepository;

    private final OrganizationLLMStatisticsRepository organizationLLMStatisticsRepository;

    private final OrganizationLLMCycleRepository organizationLLMCycleRepository;

    private final OrganizationLLMDailyBillRepository organizationLLMDailyBillRepository;

    private static final Duration WINDOW = Duration.ofMinutes(5);

    private final OrganizationBalanceUtils organizationBalanceUtils;

    @SneakyThrows
    @Scheduled(cron = "0 0/5 * * * *")
    @Transactional(rollbackFor = Exception.class)
    public void doAggregate() {
        log.info("Organization LLM Cycle Timer started");
        long now    = SystemClock.now();
        long cutOff = (now / WINDOW.toMillis()) * WINDOW.toMillis() - 1;

        /* === A. 抢行并聚合 === */
        List<Object[]> rows = organizationLLMStatisticsRepository.lockAndAggregate(cutOff);
        if (rows.isEmpty()) return;

        /* === B. 预加载模型名称 === */
        Map<Long,String> modelNames = systemModelInfoRepository.findAllById(
                rows.stream().map(r -> (Long) r[1]).collect(Collectors.toSet())
        ).stream().collect(Collectors.toMap(SystemModelInfo::getId,
                SystemModelInfo::getModelName));

        Map<Long, BigDecimal> quotaTotals = new HashMap<>();

        /* === C. 循环写 Cycle / Daily 并扣费 === */
        for (Object[] r : rows) {
            int p = 0;
            Long orgId       = (Long)    r[p++];
            Long modelId     = (Long)    r[p++];
            long bucketStart = ((Number) r[p++]).longValue();
            long req         = ((Number) r[p++]).longValue();

            long tp   = ((Number) r[p++]).longValue();
            long tcp  = ((Number) r[p++]).longValue();
            long tcp5 = ((Number) r[p++]).longValue();
            long tcp1h= ((Number) r[p++]).longValue();
            long tc   = ((Number) r[p++]).longValue();
            long ap   = ((Number) r[p++]).longValue();
            long acp  = ((Number) r[p++]).longValue();
            long ac   = ((Number) r[p++]).longValue();
            long rc   = ((Number) r[p++]).longValue();
            long ip   = ((Number) r[p++]).longValue();
            long icp  = ((Number) r[p++]).longValue();
            long ic   = ((Number) r[p++]).longValue();
            long st   = ((Number) r[p++]).longValue();
            BigDecimal quota  = (BigDecimal) r[p++];
            Long[] ids        = (Long[])    r[p++];

            /* --- UPSERT Cycle --- */
            organizationLLMCycleRepository.upsertCycle(
                    snowflakeIdGenerator.nextId(), orgId, modelId, bucketStart,
                    tp, tcp, tcp5, tcp1h, tc,
                    ap, acp, ac, rc,
                    ip, icp, ic,
                    req ,st, quota);

            /* --- UPSERT Daily --- */
            long billDay = Instant.ofEpochMilli(bucketStart)
                    .atOffset(ZoneOffset.UTC)
                    .toLocalDate()
                    .toEpochDay() * 86_400_000L;
            organizationLLMDailyBillRepository.upsertDaily(
                    snowflakeIdGenerator.nextId(), orgId, modelNames.get(modelId),
                    billDay,now,
                    tp, tcp, tcp5, tcp1h, tc,
                    ap, acp, ac, rc,
                    ip, icp, ic,
                    req,st, quota);

            /* --- 钱包扣费（事务外也可） --- */
            quotaTotals.merge(orgId, quota, BigDecimal::add);

            /* --- 标记已完成 2→1 --- */
            organizationLLMStatisticsRepository.markDone(ids);
        }


        for (Map.Entry<Long, BigDecimal> e : quotaTotals.entrySet()) {
            organizationBalanceUtils.consumeBalance(e.getKey(), e.getValue());
        }

    }

}
