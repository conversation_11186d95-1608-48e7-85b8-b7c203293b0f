package ai.yourouter.calculate.transaction;

import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.jpa.organization.transaction.balance.bean.DailyBalanceSnapshot;
import ai.yourouter.jpa.organization.transaction.balance.bean.ReconciliationDiff;
import ai.yourouter.jpa.organization.transaction.balance.repository.DailyBalanceSnapshotRepository;
import ai.yourouter.jpa.organization.transaction.balance.repository.ReconciliationDiffRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class ReconcileJob {


    private final JdbcTemplate jdbcTemplate;
    private final RedisTemplate<String, String> redisTemplate;
    private final DailyBalanceSnapshotRepository dailyBalanceSnapshotRepository;
    private final ReconciliationDiffRepository reconciliationDiffRepository;
    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private static final long TOLERANCE = 10_000;   // 1 美分

    private static final ZoneId ZONE_UTC = ZoneOffset.UTC;

    /** 每天 01:30 跑 */
    @Scheduled(cron = "0 30 1 * * *", zone = "UTC")
    @Transactional
    public void reconcile() {
        LocalDate yesterday = LocalDate.now(ZoneOffset.UTC).minusDays(1);

        /* ---------- 1. 累计充值（美元） ---------- */
        Map<Long, BigDecimal> rechargeUSD = jdbcTemplate.query("""
        SELECT organization_id, COALESCE(SUM(amount),0) AS usd
        FROM sp_platform_transaction_balance
        WHERE created < ?::date + interval '1 day'
        GROUP BY organization_id
    """, ps -> ps.setDate(1, Date.valueOf(yesterday)), rs -> {
            Map<Long, BigDecimal> m = new HashMap<>();
            while (rs.next()) m.put(rs.getLong("organization_id"), rs.getBigDecimal("usd"));
            return m;
        });

        /* ---------- 2. 累计消费（美元） ---------- */
        Map<Long, BigDecimal> consumeUSD = jdbcTemplate.query("""
    WITH t1 AS (
        SELECT organization_id, COALESCE(SUM(cost_usd),0) AS usd   -- 假设列以美元存储
        FROM sp_platform_organization_llm_statistics
        WHERE create_time < ?::date + interval '1 day'
        GROUP BY organization_id
    ),
    t2 AS (
        SELECT organization_id, COALESCE(SUM(quota_usd),0) AS usd  -- 假设列以美元存储
        FROM sp_platform_organization_search_statistics
        WHERE create_time < ?::date + interval '1 day'
        GROUP BY organization_id
    )
    SELECT COALESCE(t1.organization_id, t2.organization_id) AS organization_id,
           COALESCE(t1.usd,0) + COALESCE(t2.usd,0)          AS usd
    FROM t1 FULL JOIN t2 ON t1.organization_id = t2.organization_id
    """,
                ps -> {
                    ps.setDate(1, Date.valueOf(yesterday));
                    ps.setDate(2, Date.valueOf(yesterday));
                },
                rs -> {
                    Map<Long, BigDecimal> m = new HashMap<>();
                    while (rs.next()) m.put(rs.getLong("organization_id"), rs.getBigDecimal("usd"));
                    return m;
                });

        /* ---------- 3. 计算 & 校准 (全部转 nano-dollar) ---------- */
        Set<Long> orgIds = new HashSet<>();
        orgIds.addAll(rechargeUSD.keySet());
        orgIds.addAll(consumeUSD.keySet());

        for (Long orgId : orgIds) {

            long rechargeNanos = BillingUtils.usdToNanoCent(
                    rechargeUSD.getOrDefault(orgId, BigDecimal.ZERO));

            long consumeNanos  = BillingUtils.usdToNanoCent(
                    consumeUSD.getOrDefault(orgId, BigDecimal.ZERO));

            long expected = rechargeNanos - consumeNanos;          // 账面余额 (nano)

            String key = "BAL:" + orgId;
            long live = Optional.ofNullable(redisTemplate.opsForValue().get(key))
                    .map(Long::parseLong).orElse(0L);

            long delta = live - expected;

            // 记录差异 (nano)
            reconciliationDiffRepository.save(new ReconciliationDiff(
                    snowflakeIdGenerator.nextId(),
                    yesterday,
                    orgId,
                    expected,
                    live,
                    delta,
                    Instant.now()
            ));

            // 修正 Redis
            if (Math.abs(delta) > TOLERANCE) {
                long fixed = expected + delta;      // 保留当日增量
                redisTemplate.opsForValue().set(key, String.valueOf(fixed));
            }

            // 写日结余快照
            dailyBalanceSnapshotRepository.save(
                    new DailyBalanceSnapshot(snowflakeIdGenerator.nextId(), yesterday, orgId, expected));
        }
    }
}


