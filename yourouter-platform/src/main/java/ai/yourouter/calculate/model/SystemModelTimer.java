package ai.yourouter.calculate.model;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.HashMap;

@Slf4j
@Component
@RequiredArgsConstructor
public class SystemModelTimer {

    private final RedisTemplate<String,String> redisTemplate;

    private final SystemModelInfoRepository systemModelInfoRepository;


    @Scheduled(cron = "0 0/2 * * * *")
    public void RefreshModel(){
        systemModelInfoRepository.findAll().forEach(
                systemModelInfo -> {
                    redisTemplate.opsForSet().add(SystemConstant.PROXY_MODEL_LIST_KEY,systemModelInfo.getModelName());
                    redisTemplate.opsForValue().set(SystemConstant.PROXY_MODEL_KEY+systemModelInfo.getModelName(), String.valueOf(systemModelInfo.getId()));
                }
        );
    }
}
