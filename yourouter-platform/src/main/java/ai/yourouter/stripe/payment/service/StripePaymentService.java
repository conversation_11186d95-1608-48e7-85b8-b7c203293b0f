package ai.yourouter.stripe.payment.service;

import ai.yourouter.common.result.Result;
import org.springframework.web.bind.annotation.RequestParam;

import java.math.BigDecimal;
import java.util.Map;

public interface StripePaymentService {

    public Result<Map<String,String>> createCheckoutUrl(Long organizationId, Long amount);

    public Result refundCharge(Long organizationId, BigDecimal amountUsdLong);

    public Result<Map<String,String>> checkRefund(Long organizationId);
}
