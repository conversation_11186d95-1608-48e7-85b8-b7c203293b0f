package ai.yourouter.stripe.payment.controller;

import ai.yourouter.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import ai.yourouter.stripe.payment.service.StripePaymentService;

import java.math.BigDecimal;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/stripe/payment")
@RequiredArgsConstructor
public class StripePaymentController {

    private final StripePaymentService stripePaymentService;

    @RequestMapping(value = "/checkout", method = RequestMethod.POST)
    public Result<Map<String,String>> createCheckoutUrl(@RequestParam(value = "organizationId") Long organizationId,@RequestParam Long amount) {
        return stripePaymentService.createCheckoutUrl(organizationId,amount);
    }

    @RequestMapping(value = "/refund", method = RequestMethod.POST)
    public Result refundCharge(@RequestParam(value = "organizationId") Long organizationId,@RequestParam BigDecimal amountUsdLong){
        return stripePaymentService.refundCharge(organizationId,amountUsdLong);
    }

    @RequestMapping(value = "/checkRefund",method = RequestMethod.GET)
    public Result<Map<String,String>> checkRefund(@RequestParam(value = "organizationId") Long organizationId){
        return stripePaymentService.checkRefund(organizationId);
    }



}
