package ai.yourouter.stripe.payment.service.impl;

import ai.yourouter.common.constant.ErrorCodeEnum;
import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.common.utils.OrganizationBalanceUtils;
import ai.yourouter.common.utils.StripePaymentUtils;
import ai.yourouter.jpa.organization.info.bean.OrganizationInfo;
import ai.yourouter.jpa.organization.info.repository.OrganizationInfoRepository;
import ai.yourouter.jpa.organization.team.bean.OrganizationTeam;
import ai.yourouter.jpa.organization.team.repository.OrganizationTeamRepository;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationBillingStatement;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import ai.yourouter.jpa.organization.transaction.balance.repository.OrganizationBillingStatementRepository;
import ai.yourouter.jpa.user.info.bean.UserInfo;
import ai.yourouter.jpa.user.info.repository.UserInfoRepository;
import com.stripe.exception.StripeException;
import com.stripe.model.Customer;
import com.stripe.model.checkout.Session;
import com.stripe.net.RequestOptions;
import com.stripe.param.CustomerCreateParams;
import com.stripe.param.checkout.SessionCreateParams;
import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.jpa.stripe.record.bean.StripeChargeRecord;
import ai.yourouter.jpa.stripe.record.repository.StripeChargeRecordRepository;
import io.micrometer.common.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ai.yourouter.stripe.payment.service.StripePaymentService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class StripePaymentServiceImpl implements StripePaymentService {

    @Value("${stripe.callback.domain}")
    private String callbackDomain;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final StripeChargeRecordRepository stripeChargeRecordRepository;

    private final BalanceTransactionRepository balanceTransactionRepository;

    private final OrganizationInfoRepository organizationInfoRepository;

    private final OrganizationTeamRepository organizationTeamRepository;

    private final UserInfoRepository userInfoRepository;

    private final OrganizationBalanceUtils organizationBalanceUtils;

    private final OrganizationBillingStatementRepository organizationBillingStatementRepository;

    private final StripePaymentUtils stripePaymentUtils;


    @SneakyThrows
    @Override
    @Transactional
    public Result<Map<String, String>> createCheckoutUrl(Long organizationId, Long amount) {

        //参数校验
        if (amount == null || amount <= 0 || amount % 100 != 0) {
            return Result.fail(ErrorCodeEnum.INVALID_AMOUNT);
        }



        //找customer Id
        OrganizationInfo org = organizationInfoRepository.findOrganizationInfoById(organizationId);
        if (org == null) {
            return Result.fail(ErrorCodeEnum.ORGANIZATION_NOT_FOUND);
        }
        if (StringUtils.isBlank(org.getCustomerId())) {
            OrganizationTeam organizationTeam = organizationTeamRepository.findOrganizationTeamsByOrganizationIdAndPermissionAndStatuses(organizationId, PermissionStatusEnum.AVAILABLE.getCode(),PermissionStatusEnum.AVAILABLE.getCode()).listIterator().next();
            UserInfo userInfo = userInfoRepository.findUserInfoById(organizationTeam.getUserId());
            CustomerCreateParams params = CustomerCreateParams.builder()
                    .setEmail(userInfo.getEmail())
                    .setName(userInfo.getNickname())
                    .build();
            Customer customer = null;
            try {
                customer = Customer.create(params);
            } catch (StripeException e) {
                throw new RuntimeException(e);
            }

            // 3. 从 Customer 对象中获取 ID
            String customerId = customer.getId();
            org.setCustomerId(customerId);
            organizationInfoRepository.save(org);
        }



        //本地记录立刻Save
        long localId = snowflakeIdGenerator.nextId();
        StripeChargeRecord record = new StripeChargeRecord();
        record.setId(localId);
        record.setOrganizationId(organizationId);
        record.setAmount(BigDecimal.valueOf(amount).divide(BigDecimal.valueOf(100)));
        record.setAmountRefunded(BigDecimal.ZERO);
        record.setAmountCaptured(BigDecimal.ZERO);
        record.setRefunded(Boolean.FALSE);
        record.setCurrency("usd");
        record.setStatus("PENDING");
        record.setCreated(Instant.now());
        record.setCustomerId(org.getCustomerId());
        // 先保存一次，确保有持久化记录（便于失败时补充错误信息）
        stripeChargeRecordRepository.save(record);


        //到底是多少钱 1 USD 为一个单位
        long quantity = amount / 100L;

        //构建 LineItem
        SessionCreateParams.LineItem lineItem = SessionCreateParams.LineItem.builder()
                .setPriceData(
                        SessionCreateParams.LineItem.PriceData.builder()
                                .setCurrency("usd")
                                .setUnitAmount(100L)
                                .setProductData(
                                        SessionCreateParams.LineItem.PriceData.ProductData.builder()
                                                .setName("API Prepaid Credit – $" + quantity)
                                                .setDescription(
                                                        "Preload $" + quantity +
                                                                " to your account as API usage credit. " +
                                                                "Standard API usage will deduct from this balance until it’s used."
                                                )
                                                .build()
                                )
                                .build()
                )
                .setQuantity(quantity) // ★ 必须设置
                .build();


        //构建 Session 参数
        SessionCreateParams params = SessionCreateParams.builder()
                .setCustomer(org.getCustomerId())
                .setMode(SessionCreateParams.Mode.PAYMENT)
                .setSuccessUrl(callbackDomain + "/pay/success?session_id={CHECKOUT_SESSION_ID}")
                .setCancelUrl(callbackDomain + "/pay/cancel")
                .putMetadata("localChargeId", String.valueOf(record.getId()))
                .setClientReferenceId(String.valueOf(record.getId()))
                .putMetadata("organizationId", String.valueOf(organizationId))
                .putMetadata("amountCents", String.valueOf(amount))
                .addLineItem(lineItem)
                .setAllowPromotionCodes(true)
                .build();

        //幂等键，避免重复创建
        RequestOptions requestOptions =
                com.stripe.net.RequestOptions.builder()
                        .setIdempotencyKey("checkout_" + localId + "_" + UUID.randomUUID())
                        .build();

        //调用 Stripe 创建 Session
        Session session;
        try {
            session = Session.create(params, requestOptions);
        } catch (StripeException e) {

            // 创建失败：标记 FAILED 并记录错误
            String msg = e.getMessage();
            String reqId = e.getRequestId();
            record.setStatus("FAILED");
            record.setMetadata("{\"stripe_error\":\"" + safe(msg) + "\",\"request_id\":\"" + safe(reqId) + "\"}");
            stripeChargeRecordRepository.save(record);

            log.error("Failed to create Stripe Session for org {}, amount {}: {} (reqId={})",
                    organizationId, amount, msg, reqId, e);
            return Result.fail(ErrorCodeEnum.MISSING_CUSTOMER_ID);
        }

        //填充 Stripe 字段并持久化
        record.setStripeId(session.getId());
        record.setPaymentIntentId(session.getPaymentIntent());
        stripeChargeRecordRepository.save(record);

        //返回前端跳转 URL
        return Result.success(Map.of("checkoutUrl", session.getUrl()));

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result refundCharge(Long organizationId, BigDecimal amountUsdLong) {
        //参数校验
        if (organizationId == null) {
            return Result.fail(ErrorCodeEnum.INVALID_ORGANIZATION);
        }
        if (amountUsdLong == null || amountUsdLong.compareTo(BigDecimal.ZERO) <= 0L) {
            return Result.fail(ErrorCodeEnum.INVALID_REFUND_AMOUNT);
        }

        // 目标退款额（USD，保留两位用于换算美分）
        BigDecimal requestedUsd = amountUsdLong.setScale(2,RoundingMode.DOWN);
        BigDecimal remaining = requestedUsd;
        BigDecimal refundedUsd = BigDecimal.ZERO;


        //取 RECHARGE 列表（最新优先）
        List<BalanceTransaction> recharges = balanceTransactionRepository.findRechargeTransactionsDesc(organizationId);
        if (recharges == null || recharges.isEmpty()) {
            return Result.fail(ErrorCodeEnum.NO_REFUNDABLE_BALANCE);
        }


        //预取对应的 Stripe 记录，减少 N+1
        Map<String, StripeChargeRecord> stripeMap = Collections.emptyMap();
        List<String> stripeIds = recharges.stream()
                .map(BalanceTransaction::getStripeChargeId)
                .filter(s -> s != null && !s.isBlank())
                .distinct()
                .toList();
        if (!stripeIds.isEmpty()) {
            List<StripeChargeRecord> srs = stripeChargeRecordRepository
                    .findByOrganizationIdAndStripeIdIn(organizationId, stripeIds);
            stripeMap = srs.stream().collect(Collectors.toMap(StripeChargeRecord::getStripeId, Function.identity(), (a, b)->a));
        }


        //逐笔退款
        for (BalanceTransaction tx : recharges) {
            if (remaining.compareTo(BigDecimal.ZERO) <= 0) break;
            //本地可退
            BigDecimal amount   = nz(tx.getAmount());
            BigDecimal refunded = nz(tx.getAmountRefunded());
            BigDecimal captured = nz(tx.getAmountCaptured());
            BigDecimal localAvail = amount.subtract(refunded).subtract(captured);
            if (localAvail.compareTo(BigDecimal.ZERO) <= 0) continue;
            String stripeId = tx.getStripeChargeId();
            if (stripeId == null || stripeId.isBlank()) {
                continue;
            }
            StripeChargeRecord sr = stripeMap.get(stripeId);
            if (sr == null) continue;
            String status = sr.getStatus() == null ? "" : sr.getStatus();
            if (!"succeeded".equalsIgnoreCase(status) || Boolean.TRUE.equals(sr.getRefunded())) continue;
            BigDecimal srAvail = nz(sr.getAmount())
                    .subtract(nz(sr.getAmountRefunded()))
                    .subtract(nz(sr.getAmountCaptured()));
            if (srAvail.compareTo(BigDecimal.ZERO) <= 0) continue;

            BigDecimal toRefundUsd = localAvail.min(srAvail).min(remaining);
            if (toRefundUsd.compareTo(BigDecimal.ZERO) <= 0) continue;

            long refundCents = dollarsToCents(toRefundUsd);
            try {
                stripePaymentUtils.refundByPaymentIntent(
                        sr.getPaymentIntentId(),
                        refundCents,
                        sr.getId()
                );
            } catch (Exception e) {
                log.error("Stripe refund failed: org={}, stripeId={}, txId={}, refund={} USD",
                        organizationId, stripeId, tx.getId(), toRefundUsd.toPlainString(), e);
                break;
            }

            tx.setAmountRefunded(refunded.add(toRefundUsd));
            if (amount.subtract(tx.getAmountRefunded()).subtract(captured).compareTo(BigDecimal.ZERO) == 0) {
                tx.setRefunded(Boolean.TRUE);
            }
            balanceTransactionRepository.save(tx);

            BigDecimal srRefundedNew = nz(sr.getAmountRefunded()).add(toRefundUsd);
            sr.setAmountRefunded(srRefundedNew);
            if (sr.getAmount() != null && srRefundedNew.compareTo(nz(sr.getAmount())) >= 0) {
                sr.setRefunded(Boolean.TRUE);
            }
            stripeChargeRecordRepository.save(sr);

            long nowMs = System.currentTimeMillis();
            OrganizationBillingStatement stmt = organizationBillingStatementRepository
                    .findOrganizationBillingStatementByOrganizationId(organizationId)
                    .orElseGet(() -> {
                        OrganizationBillingStatement s = new OrganizationBillingStatement();
                        s.setOrganizationId(organizationId);
                        s.setStartTime(nowMs);
                        s.setCreatedAt(Instant.ofEpochMilli(nowMs));
                        s.setTotalRecharge(BigDecimal.ZERO);
                        s.setTotalGift(BigDecimal.ZERO);
                        s.setTotalAdjustment(BigDecimal.ZERO);
                        s.setTotalCaptured(BigDecimal.ZERO);
                        s.setTotalRefunded(BigDecimal.ZERO);
                        s.setNetBalanceChange(BigDecimal.ZERO);
                        return s;
                    });
            stmt.setTotalRefunded(nz(stmt.getTotalRefunded()).add(toRefundUsd));
            BigDecimal net = nz(stmt.getTotalRecharge())
                    .add(nz(stmt.getTotalGift()))
                    .add(nz(stmt.getTotalAdjustment()))
                    .subtract(nz(stmt.getTotalCaptured()))
                    .subtract(nz(stmt.getTotalRefunded()));
            stmt.setNetBalanceChange(net);
            stmt.setEndTime(nowMs);
            organizationBillingStatementRepository.save(stmt);

            // 扣组织余额
            organizationBalanceUtils.deductBalance(
                    organizationId,
                    BillingUtils.usdToNanoCent(toRefundUsd)
            );

            //累计 & 递减
            refundedUsd = refundedUsd.add(toRefundUsd);
            remaining   = remaining.subtract(toRefundUsd);

            log.info("↩️ refund ok: org={} txId={} stripeId={} refunded={} USD, remaining={}",
                    organizationId, tx.getId(), stripeId,
                    toRefundUsd.toPlainString(), remaining.toPlainString());

        }

        Map<String, String> data = new LinkedHashMap<>();
        data.put("requestedUsd", requestedUsd.toPlainString());
        data.put("refundedUsd", refundedUsd.toPlainString());
        data.put("unrefundedUsd", remaining.toPlainString());

        if (refundedUsd.compareTo(BigDecimal.ZERO) == 0) {
            // 1 分都没退成功
            return Result.fail(ErrorCodeEnum.NO_REFUNDABLE_BALANCE);
        }
        // 若未完全满足，前端可提示“部分退款已完成”
        return Result.success(data);
    }


    @Override
    public Result<Map<String, String>> checkRefund(Long organizationId) {

        OrganizationBillingStatement stmt = organizationBillingStatementRepository
                .findOrganizationBillingStatementByOrganizationId(organizationId)
                .orElseThrow(() -> new IllegalStateException("未找到该组织的累计账单记录"));

        // 取值并做 null 安全
        BigDecimal totalRecharge   = nz(stmt.getTotalRecharge());
        BigDecimal totalGift       = nz(stmt.getTotalGift());
        BigDecimal totalAdjustment = nz(stmt.getTotalAdjustment());
        BigDecimal totalCaptured   = nz(stmt.getTotalCaptured()); // 全口径消费（含赠送/调账/充值）
        BigDecimal totalRefunded   = nz(stmt.getTotalRefunded()); // 累计退款（当前模型下等价于充值退款）

        // 充值被消费 = max(0, 总消费 - 赠送 - 调账)
        BigDecimal rechargeConsumed = maxZero(totalCaptured.subtract(totalGift).subtract(totalAdjustment));

        // 充值可退本金 = max(0, 累计充值 - 累计退款)
        BigDecimal remainingRechargePrincipal = maxZero(totalRecharge.subtract(totalRefunded));

        // 可退款 = max(0, 可退本金 - 充值被消费)
        BigDecimal refundable = maxZero(remainingRechargePrincipal.subtract(rechargeConsumed));

        Map<String, String> data = new LinkedHashMap<>();
        data.put("totalRecharge", totalRecharge.toPlainString());
        data.put("totalGift", totalGift.toPlainString());
        data.put("totalAdjustment", totalAdjustment.toPlainString());
        data.put("totalCaptured(allSources)", totalCaptured.toPlainString());
        data.put("totalRefunded", totalRefunded.toPlainString());
        data.put("rechargeConsumed(derived)", rechargeConsumed.toPlainString());
        data.put("remainingRechargePrincipal", remainingRechargePrincipal.toPlainString());
        data.put("refundable", refundable.toPlainString());

        return Result.success(data);
    }

    private static BigDecimal nz(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v;
    }
    private static BigDecimal maxZero(BigDecimal v) {
        return v.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : v;
    }

    /** USD(BigDecimal) -> cents(long)。严格保留两位再换算。 */
    private static long dollarsToCents(BigDecimal usd) {
        return usd.setScale(2, RoundingMode.HALF_UP)
                .movePointRight(2)
                .setScale(0, RoundingMode.HALF_UP)
                .longValueExact();
    }

    private static String safe(String s) {
        return s == null ? "" : s.replace("\"", "\\\"");
    }
}
