package ai.yourouter.stripe.config;

import com.stripe.Stripe;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.PostConstruct;

/**
 * Stripe 配置类
 * 负责初始化 Stripe API 密钥
 */
@Slf4j
@Configuration
public class StripeConfig {

    @Value("${stripe.api-key}")
    private String stripeApiKey;

    /**
     * 在 Spring 容器初始化完成后设置 Stripe API 密钥
     */
    @PostConstruct
    public void initStripe() {
        Stripe.apiKey = stripeApiKey;
        log.info("Stripe API key initialized successfully");
    }
}
