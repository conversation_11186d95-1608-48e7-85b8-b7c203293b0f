package ai.yourouter.stripe.webhook.controller;

import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import ai.yourouter.stripe.webhook.service.StripeWebhookService;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/stripewebhook")
@RequiredArgsConstructor
public class StripeWebhookController {

    private final StripeWebhookService stripeWebhookService;


    @SneakyThrows
    @RequestMapping(value = "/webhook",method = RequestMethod.POST,produces = MediaType.APPLICATION_JSON_VALUE,consumes = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> handleWebhook(@RequestBody String payload,
                                                @RequestHeader("Stripe-Signature") String sigHeader) {
        return stripeWebhookService.handleWebhook(payload, sigHeader);
    }
}
