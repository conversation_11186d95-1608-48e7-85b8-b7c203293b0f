package ai.yourouter.system.startup.controller;

import ai.yourouter.common.result.Result;
import ai.yourouter.request.system.SubmitApplicationRequest;
import ai.yourouter.system.startup.service.StartupApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/api/public/system/startup")
@RequiredArgsConstructor
public class StartupApplicationController {

    private final StartupApplicationService startupApplicationService;


    @RequestMapping(value = "/apply",method = RequestMethod.POST)
    public Result<String> apply(@RequestBody SubmitApplicationRequest submitApplicationRequest){
        return startupApplicationService.apply(submitApplicationRequest);
    }


}
