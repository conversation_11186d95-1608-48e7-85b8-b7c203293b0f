package ai.yourouter.system.startup.service.impl;

import ai.yourouter.common.constant.ErrorCodeEnum;
import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.DnsLookupUtils;
import ai.yourouter.common.utils.DomainRegistrationLookup;
import ai.yourouter.common.utils.EmailRules;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.jpa.organization.info.bean.OrganizationInfo;
import ai.yourouter.jpa.organization.info.repository.OrganizationInfoRepository;
import ai.yourouter.jpa.organization.team.repository.OrganizationTeamRepository;
import ai.yourouter.jpa.system.startup.bean.StartupApplication;
import ai.yourouter.jpa.system.startup.repository.StartupApplicationRepository;
import ai.yourouter.jpa.user.info.repository.UserInfoRepository;
import ai.yourouter.request.system.SubmitApplicationRequest;
import ai.yourouter.system.startup.service.StartupApplicationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;


@Slf4j
@Service
@RequiredArgsConstructor
public class StartupApplicationServiceImpl implements StartupApplicationService {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationTeamRepository  organizationTeamRepository;

    private final StartupApplicationRepository startupApplicationRepository;


    private static final double W_EMAIL       = 3.5;
    private static final double W_DOMAIN_AGE  = 2.0;
    private static final double W_AI          = 2.0;
    private static final double W_FEAS        = 2.5;
    private static final double PASS_THRESHOLD = 9;

    private static final Set<String> FEAS_KWS = Set.of(
            "用户","客户","市场","竞争","营收","收入","成本","毛利","单位经济","里程碑","合规","风控","定价","渠道","转化","留存","原型","上线","路线图",
            "customer","user","market","competition","revenue","cost","margin","unit economics",
            "milestone","compliance","risk","pricing","channel","conversion","retention",
            "prototype","launch","roadmap","timeline","budget"
    );

    @Override
    public Result apply(SubmitApplicationRequest req) {
        if (req == null || req.getOrgId() == null
                || isBlank(req.getEmail()) || isBlank(req.getProjectIdea())) {
            return Result.fail(ErrorCodeEnum.PARAM_INVALID);
        }

        Long orgId = req.getOrgId();
        String email = req.getEmail().trim().toLowerCase(Locale.ROOT);
        String idea  = req.getProjectIdea().trim();

        // —— Step 1：邮箱/域名分 ——
        boolean isTeam = organizationTeamRepository.existsMemberWithSameEmailDomain(orgId, email);
        double emailScore = scoreEmail(email, isTeam);
        log.info("StartupApply score[EMAIL] orgId={}, email={}, score={}, isTeam={}",  // <<< 新增日志
                orgId, email, emailScore, isTeam);

        // —— Step 2：域龄分 ——
        String domain = extractDomain(email);
        Optional<Instant> createdAtOpt = DomainRegistrationLookup.lookupCreationInstant(domain);
        double domainAgeScore = scoreDomainAge(createdAtOpt);
        Long daysAgo = createdAtOpt.map(t -> Duration.between(t, Instant.now()).toDays()).orElse(null);
        log.info("StartupApply score[DOMAIN_AGE] orgId={}, domain={}, score={}, createdAt={}, daysAgo={}", // <<< 新增日志
                orgId, domain, domainAgeScore, createdAtOpt.map(Instant::toString).orElse("UNKNOWN"), daysAgo);

        // —— Step 3：AI 代写分 ——
        double aiLikelihood = callLLMForAiLikelihood(idea).orElseGet(() -> heuristicAiLikelihood(idea));
        double aiScore = scoreAi(aiLikelihood);
        log.info("StartupApply score[AI_LIKELIHOOD] orgId={}, score={}, likelihood={}",  // <<< 新增日志
                orgId, aiScore, aiLikelihood);

        // —— Step 4：可行度分 ——
        double feasScore = scoreFeasibility(idea);
        log.info("StartupApply score[FEASIBILITY] orgId={}, score={}", orgId, feasScore); // <<< 新增日志

        // —— 汇总 ——
        double total = round2(emailScore + domainAgeScore + aiScore + feasScore);
        boolean pass = total >= PASS_THRESHOLD && emailScore > 0;
        log.info("StartupApply score[TOTAL] orgId={}, total={}/10, threshold={}, decision={}", // <<< 新增日志
                orgId, total, PASS_THRESHOLD, (pass ? "AUTO_APPROVED" : "NEEDS_REVIEW"));

        // —— 组装要落库的申请记录（第一次 save） ——
        StartupApplication app = new StartupApplication();
        app.setId(snowflakeIdGenerator.nextId());
        app.setOrganizationId(orgId);
        app.setEmail(email);
        app.setProjectIdea(idea);
        app.setScore((int)Math.round(total));
        app.setDecisionReason(trim512(buildDecisionReason(
                emailScore, domainAgeScore, aiScore, feasScore, aiLikelihood, createdAtOpt)));
        app.setStatus(pass ? StartupApplication.Status.APPROVED : StartupApplication.Status.SUBMITTED);
        if (pass) {
            app.setReviewerId("SYSTEM");
            app.setReviewedAt(Instant.now());
        }

        try {
            startupApplicationRepository.save(app);
        } catch (DataIntegrityViolationException ex) {
            // 命中唯一约束 (orgId, email, status) 的兜底：更新最近一条同 org+email 的记录
            log.warn("Save StartupApplication hit unique constraint, fallback to update. orgId={}, email={}, status={}",
                    orgId, email, app.getStatus(), ex);
            startupApplicationRepository.findTopByOrganizationIdAndEmailOrderByCreatedAtDesc(orgId, email)
                    .ifPresentOrElse(existing -> {
                        existing.setProjectIdea(idea);
                        existing.setScore(app.getScore());
                        existing.setDecisionReason(app.getDecisionReason());
                        if (pass && existing.getStatus() == StartupApplication.Status.SUBMITTED) {
                            existing.setStatus(StartupApplication.Status.APPROVED);
                            existing.setReviewerId("SYSTEM");
                            existing.setReviewedAt(Instant.now());
                        }
                        startupApplicationRepository.save(existing);
                    }, () -> {
                        startupApplicationRepository.save(app);
                    });
        }

        log.info("StartupApply saved orgId={}, email={}, status={}, score={}, domainCreated={}",
                orgId, email, app.getStatus(), app.getScore(),
                createdAtOpt.map(Instant::toString).orElse("UNKNOWN"));

        return pass ? Result.success(ErrorCodeEnum.AUTO_APPROVED)
                : Result.fail(ErrorCodeEnum.NEEDS_REVIEW);
    }

    private double scoreEmail(String email, boolean isTeam) {
        if (isTeam) return W_EMAIL;
        int at = email.lastIndexOf('@');
        if (at <= 0 || at == email.length() - 1) return 0.0;
        String local = email.substring(0, at);
        String domain = email.substring(at + 1);
        boolean free = EmailRules.isFreeDomain(domain);
        boolean disposable = EmailRules.isDisposableDomain(domain);
        boolean role = EmailRules.isRoleLocalPart(local);
        boolean hasMxOrA = DnsLookupUtils.hasMx(domain) || DnsLookupUtils.hasA(domain);
        if (free || disposable || role || !hasMxOrA) return 0.0;
        return W_EMAIL * 0.7; // 非组织同域但通过基本校验 → 给 70% 分
    }
    private double scoreDomainAge(Optional<Instant> createdAtOpt) {
        if (createdAtOpt.isEmpty()) return 0.8;
        long days = Duration.between(createdAtOpt.get(), Instant.now()).toDays();
        if (days >= 365) return W_DOMAIN_AGE;
        if (days >= 180) return 1.5;
        if (days >= 90)  return 1.0;
        return 0.0;
    }
    private double scoreAi(double likelihood) {
        if (likelihood <= 0.3) return W_AI;
        if (likelihood <= 0.6) return 1.0;
        return 0.0;
    }
    private double scoreFeasibility(String idea) {
        if (idea == null || idea.isBlank()) return 0.0;
        String t = idea.toLowerCase(Locale.ROOT);
        double base = 0.0;
        int len = t.length();
        if (len >= 200 && len <= 1200) base += 1.0;
        else if (len > 1200) base += 0.6;
        else if (len >= 120) base += 0.5;
        long hit = FEAS_KWS.stream().filter(kw -> t.contains(kw.toLowerCase(Locale.ROOT))).count();
        if (hit >= 8)       base += 1.5;
        else if (hit >= 5)  base += 1.2;
        else if (hit >= 3)  base += 0.8;
        else if (hit >= 1)  base += 0.4;
        long digits = t.chars().filter(Character::isDigit).count();
        if (digits >= 3) base += 0.3;
        return Math.min(W_FEAS, base);
    }

    // ==== LLM 伪代码：返回 [0,1]，失败则 empty，外部走启发式回退 ====
    private Optional<Double> callLLMForAiLikelihood(String text) {
        // HttpRequest -> 大模型服务 -> JSON: {"likelihood": 0.x}
        return Optional.empty();
    }
    private double heuristicAiLikelihood(String text) {
        if (text == null || text.isBlank()) return 1.0;
        String t = text.toLowerCase(Locale.ROOT).trim();
        List<Pattern> aiPatterns = List.of(
                Pattern.compile("as an ai language model"),
                Pattern.compile("i am an ai"),
                Pattern.compile("can't access the internet"),
                Pattern.compile("provide an outline"),
                Pattern.compile("here are some suggestions"),
                Pattern.compile("in conclusion[,\\.]?")
        );
        double score = 0.0;
        for (Pattern p : aiPatterns) if (p.matcher(t).find()) score += 0.25;
        int len = t.length();
        long commas = t.chars().filter(c -> c == ',').count();
        long digits = t.chars().filter(Character::isDigit).count();
        if (commas > 20 && digits == 0) score += 0.2;
        if (len > 1200 && digits == 0) score += 0.2;
        if (len < 120) score += 0.3;
        return Math.max(0.0, Math.min(1.0, score));
    }

    // ==== 小工具 ====
    private static boolean isBlank(String s) { return s == null || s.trim().isEmpty(); }
    private static String extractDomain(String email) {
        int at = email.lastIndexOf('@');
        return (at > 0 && at < email.length() - 1) ? email.substring(at + 1) : email;
    }
    private static double round2(double v) { return Math.round(v * 100.0) / 100.0; }

    private static String trim512(String s) {
        if (s == null) return null;
        return s.length() <= 512 ? s : s.substring(0, 512);
    }

    private static String buildDecisionReason(double emailScore, double domainAgeScore,
                                              double aiScore, double feasScore,
                                              double aiLikelihood, Optional<Instant> createdAt) {
        return String.format(Locale.ROOT,
                "auto-screening: email=%.2f, domainAge=%.2f, ai=%.2f(l=%.2f), feas=%.2f, created=%s",
                emailScore, domainAgeScore, aiScore, aiLikelihood, feasScore,
                createdAt.map(Instant::toString).orElse("UNKNOWN"));
    }
}
