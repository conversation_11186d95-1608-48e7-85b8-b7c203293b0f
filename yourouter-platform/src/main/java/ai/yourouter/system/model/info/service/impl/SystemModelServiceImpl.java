package ai.yourouter.system.model.info.service.impl;

import ai.yourouter.common.result.Result;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import ai.yourouter.response.system.model.info.ModelPriceInfoResponse;
import ai.yourouter.system.model.info.service.SystemModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class SystemModelServiceImpl implements SystemModelService {

    private final SystemModelInfoRepository systemModelInfoRepository;

    @Override
    public Result<List<ModelPriceInfoResponse>> getModelInfoAll() {

        List<Object[]> raw = systemModelInfoRepository.fetchModelPricingInfoRaw();
        List<ModelPriceInfoResponse> list = new ArrayList<>();

        for (Object[] row : raw) {
            ModelPriceInfoResponse dto = new ModelPriceInfoResponse();

            dto.setModelName((String) row[1]);
            dto.setManufacturerName((String) row[2]);

            // —— 兼容解析 vendor_names （String[] 或 "{a,b}"） ——
            Object vr = row[3];
            List<String> vendors = new ArrayList<>();
            if (vr != null) {
                if (vr instanceof String[] arr) {
                    vendors = Arrays.stream(arr)
                            .map(String::trim)
                            .filter(s -> !s.isBlank())
                            .collect(Collectors.toList());
                } else {
                    String rawStr = vr.toString().replace("{", "").replace("}", "");
                    vendors = Arrays.stream(rawStr.split(","))
                            .map(String::trim)
                            .filter(s -> !s.isBlank())
                            .collect(Collectors.toList());
                }
            }
            dto.setVendorNames(vendors);

            dto.setModelType((Integer) row[4]);

            // —— 拼装价格 map ——
            Map<String, String> prices = new LinkedHashMap<>();
            // LLM 价格字段（全部 /MTokens）
            prices.put("textPrompt",      ModelPriceInfoResponse.formatLLM((BigDecimal) row[5]));
            prices.put("textCachePrompt", ModelPriceInfoResponse.formatLLM((BigDecimal) row[6]));
            prices.put("textCompletion",  ModelPriceInfoResponse.formatLLM((BigDecimal) row[7]));
            prices.put("audioPrompt",     ModelPriceInfoResponse.formatLLM((BigDecimal) row[8]));
            prices.put("audioCachePrompt",ModelPriceInfoResponse.formatLLM((BigDecimal) row[9]));
            prices.put("audioCompletion", ModelPriceInfoResponse.formatLLM((BigDecimal) row[10]));
            prices.put("reasoningCompletion", ModelPriceInfoResponse.formatLLM((BigDecimal) row[11]));
            prices.put("imagePrompt",     ModelPriceInfoResponse.formatLLM((BigDecimal) row[12]));
            prices.put("imageCachePrompt",ModelPriceInfoResponse.formatLLM((BigDecimal) row[13]));
            prices.put("imageCompletion", ModelPriceInfoResponse.formatLLM((BigDecimal) row[14]));
            prices.put("textCachePromptWrite5m", ModelPriceInfoResponse.formatLLM((BigDecimal) row[15]));
            prices.put("textCachePromptWrite1h", ModelPriceInfoResponse.formatLLM((BigDecimal) row[16]));
            // Search 价格字段（/1KCall）
            prices.put("searchCall",      ModelPriceInfoResponse.formatSearch((BigDecimal) row[17]));
            prices.put("searchTool",ModelPriceInfoResponse.formatSearch((BigDecimal) row[18]));

            dto.setPrices(prices);
            list.add(dto);
        }

        return Result.success(list);
    }
}
