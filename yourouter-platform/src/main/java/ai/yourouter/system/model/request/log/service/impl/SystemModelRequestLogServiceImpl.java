package ai.yourouter.system.model.request.log.service.impl;

import ai.yourouter.common.constant.SystemConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import ai.yourouter.response.system.model.log.MinuteAvgTpsDto;
import ai.yourouter.system.model.request.log.service.SystemModelRequestLogService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class SystemModelRequestLogServiceImpl implements SystemModelRequestLogService {

    private final RedisTemplate<String, String> redis;




    public List<MinuteAvgTpsDto> last24Hours() {
        long now = System.currentTimeMillis();
        long firstBucket = (now / 60_000) * 60_000 - 86_400_000L;

        List<MinuteAvgTpsDto> result = new ArrayList<>(2048);

        for (long ts = firstBucket; ts < now; ts += 60_000) {
            Map<Object, Object> map = redis.opsForHash().entries(SystemConstant.KEY_PREFIX + ts);
            if (map == null || map.isEmpty()) continue;

            long finalTs = ts;
            map.forEach((field, val) -> {
                String[] parts = field.toString().split(":", 2);
                if (parts.length != 2) return;
                result.add(new MinuteAvgTpsDto(
                        finalTs,
                        parts[0],
                        parts[1],
                        Double.valueOf(val.toString())));
            });
        }
        return result;
    }
}
