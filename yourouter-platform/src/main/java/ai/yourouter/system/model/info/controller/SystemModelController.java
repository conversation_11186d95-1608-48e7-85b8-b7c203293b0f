package ai.yourouter.system.model.info.controller;


import ai.yourouter.common.result.Result;
import ai.yourouter.response.system.model.info.ModelPriceInfoResponse;
import ai.yourouter.system.model.info.service.SystemModelService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/system/model/info")
@RequiredArgsConstructor
public class SystemModelController {

    private final SystemModelService systemModelService;


    @RequestMapping(value = "/getModelInfoAll",method = RequestMethod.GET)
    public Result<List<ModelPriceInfoResponse>> getModelInfoAll(){
       return systemModelService.getModelInfoAll();
    }

}
