package ai.yourouter.system.model.request.log.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import ai.yourouter.response.system.model.log.MinuteAvgTpsDto;
import ai.yourouter.system.model.request.log.service.SystemModelRequestLogService;

import java.util.List;

@RestController
@RequestMapping("/metrics")
@RequiredArgsConstructor
public class SystemModelRequestLogController {

    private final SystemModelRequestLogService systemModelRequestLogService;

    @GetMapping("/tps/24h")
    public List<MinuteAvgTpsDto> tps24h() {
        return systemModelRequestLogService.last24Hours();     // 全部走 Redis
    }
}
