package ai.yourouter.system.vendor.info.controller;

import ai.yourouter.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import ai.yourouter.response.vendor.VendorResponse;
import ai.yourouter.system.vendor.info.service.SystemVendorInfoService;

import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/vendor/info")
@RequiredArgsConstructor
public class SystemVendorInfoController {

    private final SystemVendorInfoService systemVendorInfoService;

    //获取厂商和下面的所有模型
    @RequestMapping(value = "/selectVendorAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<VendorResponse>> selectVendorAll() {
        return systemVendorInfoService.selectVendorAll();
    }







}
