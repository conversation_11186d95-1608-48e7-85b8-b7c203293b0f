package ai.yourouter.system.vendor.info.service.impl;

import ai.yourouter.common.result.Result;
import ai.yourouter.jpa.system.model.info.repository.SystemModelInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ai.yourouter.response.vendor.VendorResponse;
import ai.yourouter.system.vendor.info.service.SystemVendorInfoService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SystemVendorInfoServiceImpl implements SystemVendorInfoService {

    private final SystemModelInfoRepository systemModelInfoRepository;


    @Override
    public Result<List<VendorResponse>> selectVendorAll() {
        List<VendorResponse> vendorResponseList = new ArrayList<>();

        return null;
    }
}
