package ai.yourouter.system.balance.controller;

import ai.yourouter.common.result.Result;
import ai.yourouter.system.balance.service.BalanceTransactionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/system/balance")
@RequiredArgsConstructor
public class BalanceTransactionController {

    private final BalanceTransactionService balanceTransactionService;

    @RequestMapping(value = "/recordManualTransaction",method = RequestMethod.GET)
    public Result recordManualTransaction(@RequestParam("organizationId") Long organizationId, @RequestParam(value = "manual") BigDecimal manual){
        return balanceTransactionService.recordManualTransaction(organizationId,manual);
    }

}
