package ai.yourouter.system.balance.service.impl;

import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.common.utils.OrganizationBalanceUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.SystemClock;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.bean.OrganizationBillingStatement;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import ai.yourouter.jpa.organization.transaction.balance.repository.OrganizationBillingStatementRepository;
import ai.yourouter.system.balance.service.BalanceTransactionService;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;

@Slf4j
@Service
@RequiredArgsConstructor
public class BalanceTransactionServiceImpl implements BalanceTransactionService {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationBalanceUtils organizationBalanceUtils;

    private final BalanceTransactionRepository balanceTransactionRepository;

    private final OrganizationBillingStatementRepository  organizationBillingStatementRepository;


    @Override
    public Result recordManualTransaction(Long organizationId, BigDecimal manual) {

        Long now = SystemClock.now();
        long transactionId = snowflakeIdGenerator.nextId();


        // 1. 保存余额变动记录
        balanceTransactionRepository.save(
                BalanceTransaction.builder()
                        .id(transactionId)
                        .organizationId(organizationId)
                        .type(BalanceTransaction.TransactionType.ADJUSTMENT)
                        .amount(manual)
                        .amountCaptured(BigDecimal.ZERO)
                        .amountRefunded(BigDecimal.ZERO)
                        .refunded(false)
                        .stripeChargeId(null)
                        .created(now)
                        .referenceNo(null)
                        .memo("Manual adjustment of $" + manual.toPlainString())
                        .build()
        );

        // 2. 更新账单汇总
        OrganizationBillingStatement statement = organizationBillingStatementRepository
                .findOrganizationBillingStatementByOrganizationId(organizationId)
                .orElseThrow(() -> new EntityNotFoundException(
                        "Billing statement not found for organization: " + organizationId));
        statement.setEndTime(SystemClock.now());
        statement.setTotalAdjustment(manual.add(statement.getTotalAdjustment()));
        statement.setNetBalanceChange(manual.add(statement.getNetBalanceChange()));
        organizationBillingStatementRepository.save(statement);

        // 3. 更新实际账户余额（纳分）
        long deltaNano = BillingUtils.usdToNanoCent(manual);
        organizationBalanceUtils.addBalance(organizationId, deltaNano);

        return Result.success();
    }
}
