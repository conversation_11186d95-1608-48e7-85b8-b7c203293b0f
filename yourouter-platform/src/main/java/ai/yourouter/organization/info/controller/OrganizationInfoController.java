package ai.yourouter.organization.info.controller;

import ai.yourouter.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import ai.yourouter.organization.info.service.OrganizationInfoService;
import ai.yourouter.response.organization.OrganizationInfoResponse;

import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/organization/info")
@RequiredArgsConstructor
public class OrganizationInfoController {

    private final OrganizationInfoService organizationInfoService;

    @RequestMapping(value = "/selectOrganizationInfoAll", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll() {
        return organizationInfoService.selectOrganizationInfoAll();
    }


    @RequestMapping(value = "/updateOrganizationInfo", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> updateOrganizationInfo(@RequestParam("organizationId") Long organizationId, @RequestParam("organizationName") String organizationName) {
        return organizationInfoService.updateOrganizationInfo(organizationId, organizationName);
    }

    @GetMapping("/current/organization")
    public Result<OrganizationInfoResponse> currentOrganization(@RequestHeader(value = "x-server-timezone",defaultValue = "Asia/Shanghai")String zoneId) {
        return organizationInfoService.currentOrganization();
    }

    @PostMapping("/current/organization/change")
    public Result<OrganizationInfoResponse> currentOrganizationChange(@RequestParam("organizationId") Long organizationId, @RequestHeader(value = "x-server-timezone",defaultValue = "Asia/Shanghai")String zoneId) {
        return organizationInfoService.changeCurrentOrganization(organizationId);
    }

}
