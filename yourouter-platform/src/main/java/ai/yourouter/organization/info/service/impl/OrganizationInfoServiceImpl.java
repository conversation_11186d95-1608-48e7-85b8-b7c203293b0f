package ai.yourouter.organization.info.service.impl;


import ai.yourouter.common.auth.CurrentUser;
import ai.yourouter.common.auth.ThreadLocalUserPermissionContext;
import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.BillingUtils;
import ai.yourouter.jpa.organization.info.bean.OrganizationInfo;
import ai.yourouter.jpa.organization.info.repository.OrganizationInfoRepository;
import ai.yourouter.jpa.organization.team.bean.OrganizationTeam;
import ai.yourouter.jpa.organization.team.repository.OrganizationTeamRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import ai.yourouter.organization.info.service.OrganizationInfoService;
import ai.yourouter.response.organization.OrganizationInfoResponse;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationInfoServiceImpl implements OrganizationInfoService {

    private final CurrentUser currentUser;

    private final ThreadLocalUserPermissionContext threadLocalUserPermissionContext;

    private final RedisTemplate<String, String> redisTemplate;

    private final OrganizationInfoRepository organizationInfoRepository;

    private final OrganizationTeamRepository organizationTeamRepository;




    @Override
    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll() {
        // 获取当前用户ID
        Long userId = threadLocalUserPermissionContext.getUserId();
        if (userId == null) {
            // 回退到 CurrentUser
            userId = currentUser.get().getId();
        }

        log.info("?????hello");
        List<OrganizationTeam> binds = organizationTeamRepository
                .findOrganizationTeamsByUserIdAndStatuses(userId, PermissionStatusEnum.AVAILABLE.getCode());

        if (binds.isEmpty()) {
            return Result.success(Collections.emptyList());
        }
        List<OrganizationInfoResponse> organizationInfoResponseList = new ArrayList<>();
        Iterator<OrganizationTeam> organizationTeamIterator = binds.iterator();
        while (organizationTeamIterator.hasNext()) {
            OrganizationTeam organizationTeam = organizationTeamIterator.next();
            OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoByIdAndStatuses(organizationTeam.getOrganizationId(),PermissionStatusEnum.AVAILABLE.getCode());

            String balanceStr = redisTemplate.opsForValue().get(SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationInfo.getId());
            log.info(balanceStr);
            long balanceNanos = balanceStr != null ? Long.parseLong(balanceStr) : 0L;
            OrganizationInfoResponse organizationInfoResponse = new OrganizationInfoResponse(organizationInfo.getId(), organizationInfo.getOrganizationName(), BillingUtils.nanosToUSD(balanceNanos));
            organizationInfoResponseList.add(organizationInfoResponse);
        }
        return Result.success(organizationInfoResponseList);
    }

    @Override
    public Result<String> updateOrganizationInfo(Long organizationId, String organizationName) {
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        organizationInfo.setOrganizationName(organizationName);
        organizationInfoRepository.save(organizationInfo);
        return Result.success();
    }

    @Override
    public Result<OrganizationInfoResponse> currentOrganization() {
        // 获取当前用户ID
        Long userId = threadLocalUserPermissionContext.getUserId();
        if (userId == null) {
            // 回退到 CurrentUser
            userId = currentUser.get().getId();
        }
        var organizationId = getOrganizationId(userId);
        if (null == organizationId) {
            return Result.success();
        }
        var organizationInfo = organizationInfoRepository.findOrganizationInfoById(organizationId);
        String balanceStr = redisTemplate.opsForValue().get(SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationInfo.getId());
        long balanceNanos = balanceStr != null ? Long.parseLong(balanceStr) : 0L;
        return Result.success(OrganizationInfoResponse.builder().organizationId(organizationInfo.getId()).organizationName(organizationInfo.getOrganizationName()).balanceAmount(BillingUtils.nanosToUSD(balanceNanos)).build());
    }

    @Override
    public Result<OrganizationInfoResponse> changeCurrentOrganization(Long newOrganizationId) {
        Long userId = threadLocalUserPermissionContext.getUserId();
        if (userId == null) {
            // 回退到 CurrentUser
            userId = currentUser.get().getId();
        }
        var binds = organizationTeamRepository
                .findOrganizationTeamsByUserIdAndStatuses(userId, 1);
        if (binds.stream().map(OrganizationTeam::getOrganizationId).noneMatch(newOrganizationId::equals)) {
            throw new RuntimeException("No permission");
        }
        redisTemplate.opsForValue().set(SystemConstant.USER_SELECTED_ORGANIZATION + userId, String.valueOf(newOrganizationId));
        OrganizationInfo organizationInfo = organizationInfoRepository.findOrganizationInfoById(newOrganizationId);
        String balanceStr = redisTemplate.opsForValue().get(SystemConstant.ORGANIZATION_REDIS_BALANCE + organizationInfo.getId());
        long balanceNanos = balanceStr != null ? Long.parseLong(balanceStr) : 0L;
        return Result.success(OrganizationInfoResponse.builder().organizationId(organizationInfo.getId()).organizationName(organizationInfo.getOrganizationName()).balanceAmount(BillingUtils.nanosToUSD(balanceNanos)).build());
    }

    private Long getOrganizationId(Long userId) {
        String cachedOrgId = redisTemplate.opsForValue().get(SystemConstant.USER_SELECTED_ORGANIZATION + userId);

        if (cachedOrgId != null) {
            return Long.valueOf(cachedOrgId);
        }

        List<OrganizationTeam> binds = organizationTeamRepository
                .findOrganizationTeamsByUserIdAndStatuses(userId, PermissionStatusEnum.AVAILABLE.getCode());

        if (binds.isEmpty()) {
            return null;
        }

        OrganizationTeam organizationTeam = binds.getFirst();
        Long organizationId = organizationTeam.getOrganizationId();

        redisTemplate.opsForValue().set(
                SystemConstant.USER_SELECTED_ORGANIZATION + userId,
                String.valueOf(organizationId)
        );

        return organizationId;
    }
}
