package ai.yourouter.organization.info.service;

import ai.yourouter.common.result.Result;
import ai.yourouter.response.organization.OrganizationInfoResponse;


import java.util.List;

public interface OrganizationInfoService {

    public Result<List<OrganizationInfoResponse>> selectOrganizationInfoAll();

    public Result<String> updateOrganizationInfo(Long organizationId, String organizationName);

    public Result<OrganizationInfoResponse> currentOrganization();

    public Result<OrganizationInfoResponse> changeCurrentOrganization(Long organizationId);
}
