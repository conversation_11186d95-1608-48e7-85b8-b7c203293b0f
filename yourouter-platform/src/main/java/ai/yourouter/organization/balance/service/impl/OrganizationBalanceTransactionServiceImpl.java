package ai.yourouter.organization.balance.service.impl;

import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.StripePaymentUtils;
import ai.yourouter.jpa.organization.transaction.balance.bean.BalanceTransaction;
import ai.yourouter.jpa.organization.transaction.balance.repository.BalanceTransactionRepository;
import ai.yourouter.jpa.stripe.record.bean.StripeChargeRecord;
import ai.yourouter.jpa.stripe.record.repository.StripeChargeRecordRepository;
import ai.yourouter.organization.balance.service.OrganizationBalanceTransactionService;
import ai.yourouter.response.organization.BalanceTransactionResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationBalanceTransactionServiceImpl implements OrganizationBalanceTransactionService {

    private final StripePaymentUtils stripePaymentUtils;

    private final BalanceTransactionRepository balanceTransactionRepository;

    private final StripeChargeRecordRepository stripeChargeRecordRepository;

    @Transactional
    public Result<List<BalanceTransactionResponse>> listCreditAndAdjustmentTransactions(Long organizationId) {
        List<BalanceTransaction.TransactionType> types = Arrays.asList(
                BalanceTransaction.TransactionType.RECHARGE,
                BalanceTransaction.TransactionType.GIFT,
                BalanceTransaction.TransactionType.ADJUSTMENT
        );
        List<BalanceTransaction> txs = balanceTransactionRepository
                .findByOrganizationIdAndTypeInOrderByCreatedDesc(organizationId, types);

        List<BalanceTransactionResponse> responses = txs.stream()
                .map(tx -> {
                    String invoiceUrl = null;

                    // 如果存在 StripeChargeId，则尝试获取发票 URL 并更新记录
                    if (tx.getStripeChargeId() != null) {
                        stripeChargeRecordRepository.findStripeChargeRecordByStripeId(tx.getStripeChargeId())
                                .ifPresent(record -> {
                                    // 从外部工具获取发票链接
                                    String url = stripePaymentUtils.getCharge(record.getPaymentIntentId());
                                    record.setReceiptUrl(url);
                                    stripeChargeRecordRepository.save(record);
                                });

                        // 获取更新后的发票链接
                        invoiceUrl = stripeChargeRecordRepository
                                .findStripeChargeRecordByStripeId(tx.getStripeChargeId())
                                .map(StripeChargeRecord::getReceiptUrl)
                                .orElse(null);
                    }

                    return new BalanceTransactionResponse(
                            tx.getAmount(),
                            tx.getCreated(),
                            tx.getType(),
                            invoiceUrl
                    );
                })
                .collect(Collectors.toList());

        return Result.success(responses);
    }
}

