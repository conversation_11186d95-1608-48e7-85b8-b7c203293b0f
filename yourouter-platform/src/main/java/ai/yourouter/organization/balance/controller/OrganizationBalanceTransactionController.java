package ai.yourouter.organization.balance.controller;

import ai.yourouter.common.result.Result;
import ai.yourouter.organization.balance.service.OrganizationBalanceTransactionService;
import ai.yourouter.response.organization.BalanceTransactionResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping(value = "/api/private/organizationBalanceTransaction")
@RequiredArgsConstructor
public class OrganizationBalanceTransactionController {

    private final OrganizationBalanceTransactionService organizationBalanceTransactionService;

    @RequestMapping(value = "/listCreditAndAdjustmentTransactions",method = RequestMethod.GET)
    public Result<List<BalanceTransactionResponse>> listCreditAndAdjustmentTransactions(@RequestParam(value = "organizationId") Long organizationId){
        return organizationBalanceTransactionService.listCreditAndAdjustmentTransactions(organizationId);
    }
}
