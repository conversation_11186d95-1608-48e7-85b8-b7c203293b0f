package ai.yourouter.organization.secret.service;


import ai.yourouter.common.result.Result;
import ai.yourouter.jpa.organization.secret.bean.OrganizationSecret;
import org.springframework.data.domain.PageRequest;
import ai.yourouter.response.PageResponse;

import java.util.List;

public interface OrganizationSecretService {


    public Result<List<OrganizationSecret>> selectOrganizationSecretByOrganizationId(Long organizationId);

    public Result<OrganizationSecret> persistenceOrganizationSecretByOrganizationId(Long organizationId);

    public Result<OrganizationSecret> deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId(Long charactersId, Long charactersSecretId);


    Result<PageResponse<OrganizationSecret>> pageOrganizationSecretByOrganizationId(Long organizationId, PageRequest of);
}
