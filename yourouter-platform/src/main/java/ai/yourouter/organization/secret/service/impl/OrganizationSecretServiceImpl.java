package ai.yourouter.organization.secret.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.constant.SystemConstant;
import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.SecurityUtils;
import ai.yourouter.common.utils.SnowflakeIdGenerator;
import ai.yourouter.common.utils.StringUtils;
import ai.yourouter.common.utils.SystemClock;

import ai.yourouter.jpa.organization.secret.bean.OrganizationSecret;
import ai.yourouter.jpa.organization.secret.repository.OrganizationSecretRepository;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import ai.yourouter.organization.secret.service.OrganizationSecretService;
import ai.yourouter.response.PageResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationSecretServiceImpl implements OrganizationSecretService {

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationSecretRepository organizationSecretRepository;

    private final RedisTemplate<String, String> redisTemplate;

    private final ObjectMapper objectMapper;


    @Override
    public Result<List<OrganizationSecret>> selectOrganizationSecretByOrganizationId(Long organizationId) {
        List<OrganizationSecret> organizationSecretListResponse = new ArrayList<>();
        List<OrganizationSecret> organizationSecretList = organizationSecretRepository.findOrganizationSecretsByOrganizationIdAndStatusesIn(organizationId, List.of(PermissionStatusEnum.AVAILABLE.getCode()));
        ListIterator<OrganizationSecret> organizationSecretListIterator = organizationSecretList.listIterator();
        while (organizationSecretListIterator.hasNext()){
            OrganizationSecret organizationSecret = organizationSecretListIterator.next();
            organizationSecret.setSecretKey(StringUtils.mask(organizationSecret.getSecretKey()));
            organizationSecretListResponse.add(organizationSecret);
        }
        return Result.success(organizationSecretListResponse);
    }

    @SneakyThrows
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Result<OrganizationSecret> persistenceOrganizationSecretByOrganizationId(Long organizationId) {
        String secretKey = SecurityUtils.getRandomString(48);
        OrganizationSecret organizationSecret = new OrganizationSecret(snowflakeIdGenerator.nextId(), organizationId, secretKey, SystemClock.now(), PermissionStatusEnum.AVAILABLE.getCode());
        organizationSecretRepository.save(organizationSecret);
        redisTemplate.opsForValue()
                .set(SystemConstant.PROXY_ORGANIZATION_KEY + organizationSecret.getSecretKey(), objectMapper.writeValueAsString(organizationSecret));
        return Result.success(organizationSecret);
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<OrganizationSecret> deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId(Long organizationId, Long organizationSecretId) {
        OrganizationSecret organizationSecret = organizationSecretRepository.findOrganizationSecretByOrganizationIdAndId(organizationId,organizationSecretId);
        if (organizationSecret.getStatuses() == PermissionStatusEnum.AVAILABLE.getCode()) {
            organizationSecret.setStatuses(PermissionStatusEnum.UNAVAILABLE.getCode());
        }
        organizationSecretRepository.save(organizationSecret);

        redisTemplate.opsForValue()
                .set(SystemConstant.PROXY_ORGANIZATION_KEY + organizationSecret.getSecretKey(), objectMapper.writeValueAsString(organizationSecret),1,TimeUnit.MILLISECONDS);
        return Result.success();
    }

    @Override
    public Result<PageResponse<OrganizationSecret>> pageOrganizationSecretByOrganizationId(Long organizationId, PageRequest pageRequest) {
        // 获取组织的密钥列表
        var page = organizationSecretRepository.findOrganizationSecretsByOrganizationIdAndStatusesIn(
                organizationId,
                List.of(PermissionStatusEnum.AVAILABLE.getCode()),
                pageRequest
        );

        PageResponse<OrganizationSecret> pageResponse = new PageResponse<>(
                page.getContent().stream().peek(x -> x.setSecretKey(StringUtils.mask(x.getSecretKey()))).toList(),
                page.getTotalElements(),
                page.getTotalPages()
        );
        return Result.success(pageResponse);
    }
}
