package ai.yourouter.organization.secret.controller;


import ai.yourouter.common.result.Result;
import ai.yourouter.jpa.organization.secret.bean.OrganizationSecret;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import ai.yourouter.organization.secret.service.OrganizationSecretService;
import ai.yourouter.response.PageResponse;

import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/organizationSecret")
@RequiredArgsConstructor
public class OrganizationSecretController {

    private final OrganizationSecretService organizationSecretService;

    @RequestMapping(value = "/selectOrganizationSecretByOrganizationId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<OrganizationSecret>> selectOrganizationSecretByOrganizationId(@RequestParam("organizationId") Long organizationId) {
        return organizationSecretService.selectOrganizationSecretByOrganizationId(organizationId);
    }

    @RequestMapping(value = "/pageOrganizationSecretByOrganizationId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<PageResponse<OrganizationSecret>> pageOrganizationSecretByOrganizationId(
            @RequestParam("organizationId") Long organizationId,
            @RequestParam("pageNumber") Integer pageNumber,
            @RequestParam("pageSize") Integer pageSize
    ) {

        return organizationSecretService.pageOrganizationSecretByOrganizationId(organizationId, org.springframework.data.domain.PageRequest.of(pageNumber, pageSize));
    }

    @RequestMapping(value = "/persistenceOrganizationSecretByOrganizationId", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<OrganizationSecret> persistenceOrganizationSecretByOrganizationId(
            @RequestParam("organizationId") Long organizationId) {
        return organizationSecretService.persistenceOrganizationSecretByOrganizationId(organizationId);
    }

    @RequestMapping(value = "/deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<OrganizationSecret> deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId(
            @RequestParam("organizationId") Long organizationId,
            @RequestParam("organizationSecretId") Long organizationSecretId) {
        return organizationSecretService.deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId(organizationId, organizationSecretId);
    }
}
