package ai.yourouter.organization.dailybill.service;

import ai.yourouter.common.result.Result;
import ai.yourouter.response.organization.OrganizationDailyBillResponse;

import java.util.List;
import java.util.Map;

public interface OrganizationDailyBillService {

    public Result<Map<Long, List<OrganizationDailyBillResponse>>> showCharts(Integer selectedYear, Integer selectedMonth, String zoneId, Long organizationId);

}
