package ai.yourouter.organization.dailybill.controller;

import ai.yourouter.common.result.Result;
import ai.yourouter.organization.dailybill.service.OrganizationDailyBillService;
import ai.yourouter.response.organization.OrganizationDailyBillResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping(value = "/api/private/organizationDailyBill")
@RequiredArgsConstructor
public class OrganizationDailyBillController {

    private final OrganizationDailyBillService organizationDailyBillService;


    @GetMapping("/charts")
    public Result<Map<Long, List<OrganizationDailyBillResponse>>> showCharts(@RequestParam("selectedYear") Integer selectedYear, @RequestParam("selectedMonth") Integer selectedMonth, @RequestHeader(value = "x-server-timezone", defaultValue = "Asia/Shanghai") String zoneId, @RequestParam("organizationId") Long organizationId) {
        return organizationDailyBillService.showCharts(selectedYear, selectedMonth, "Asia/Shanghai", organizationId);
    }


}
