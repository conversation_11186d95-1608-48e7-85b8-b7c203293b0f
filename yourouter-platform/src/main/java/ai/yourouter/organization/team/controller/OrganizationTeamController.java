package ai.yourouter.organization.team.controller;

import ai.yourouter.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import ai.yourouter.organization.team.service.OrganizationTeamService;

import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/organization/team")
@RequiredArgsConstructor
public class OrganizationTeamController {

    private final OrganizationTeamService organizationTeamService;

    @RequestMapping(value = "/persistenceOrganizationTeam", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String> persistenceOrganizationTeam(@RequestParam("organizationId") Long organizationId,
                                                          @RequestParam("permission") Integer permission,
                                                          @RequestParam("userEmail") String userEmail) {

        return organizationTeamService.persistenceOrganizationTeam(organizationId, permission, userEmail);
    }


    @RequestMapping(value = "/selectOrganizationTeamByOrganizationId", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<List<HashMap<String, String>>> selectOrganizationTeamByOrganizationId(
            @RequestParam("organizationId") Long organizationId) {

        return organizationTeamService.selectOrganizationTeamByOrganizationId(organizationId);
    }

    @RequestMapping(value = "/updateOrganizationTeamByOrganizationId", method = RequestMethod.PUT, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String>  updateOrganizationTeamByOrganizationId(@RequestParam("organizationId") Long organizationId,
                                                                      @RequestParam("permission") Integer permission,
                                                                      @RequestParam("userEmail") String userEmail) {

        return organizationTeamService.updateOrganizationTeamByOrganizationId(organizationId, permission, userEmail);
    }

    @RequestMapping(value = "/deleteOrganizationTeamByOrganizationId", method = RequestMethod.DELETE, produces = MediaType.APPLICATION_JSON_VALUE)
    public Result<String>  deleteOrganizationTeamByOrganizationId(@RequestParam("organizationId") Long organizationId,
                                                                      @RequestParam("userEmail") String userEmail) {
        return organizationTeamService.deleteOrganizationTeamByOrganizationId(organizationId, userEmail);
    }

}
