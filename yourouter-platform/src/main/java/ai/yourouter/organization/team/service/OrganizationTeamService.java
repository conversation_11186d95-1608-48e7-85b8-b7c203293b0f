package ai.yourouter.organization.team.service;

import ai.yourouter.common.result.Result;

import java.util.HashMap;
import java.util.List;

public interface OrganizationTeamService {

    public Result<String> persistenceOrganizationTeam(Long organizationId, Integer permission, String userEmail) ;

    public Result<List<HashMap<String, String>>> selectOrganizationTeamByOrganizationId(Long organizationId);

    public Result<String>  updateOrganizationTeamByOrganizationId(Long organizationId,Integer permission, String userEmail);

    public Result<String>  deleteOrganizationTeamByOrganizationId(Long organizationId, String userEmail);
}
