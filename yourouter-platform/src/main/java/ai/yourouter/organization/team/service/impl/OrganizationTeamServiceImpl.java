package ai.yourouter.organization.team.service.impl;


import ai.yourouter.common.auth.ThreadLocalUserPermissionContext;
import ai.yourouter.common.constant.ErrorCodeEnum;
import ai.yourouter.common.constant.PermissionStatusEnum;
import ai.yourouter.common.result.Result;
import ai.yourouter.common.utils.SnowflakeIdGenerator;

import ai.yourouter.common.utils.SystemClock;
import ai.yourouter.jpa.organization.team.bean.OrganizationTeam;
import ai.yourouter.jpa.organization.team.repository.OrganizationTeamRepository;
import ai.yourouter.jpa.user.info.bean.UserInfo;
import ai.yourouter.jpa.user.info.repository.UserInfoRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import ai.yourouter.organization.team.service.OrganizationTeamService;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class OrganizationTeamServiceImpl implements OrganizationTeamService {

    private final ThreadLocalUserPermissionContext threadLocalUserPermissionContext;

    private final UserInfoRepository userInfoRepository;

    private final SnowflakeIdGenerator snowflakeIdGenerator;

    private final OrganizationTeamRepository organizationTeamRepository;

    /**
     * 检查当前用户是否有管理员权限
     */
    private boolean hasAdminPermission() {
        return threadLocalUserPermissionContext.hasAdminPermission();
    }

    @Override
    public Result<String> persistenceOrganizationTeam(Long organizationId, Integer permission, String userEmail) {
        //权限通过
        if (hasAdminPermission()) {
            var userInfo = userInfoRepository.findUserInfoByEmail(userEmail).orElse(null);
            if (userInfo == null) {
                return Result.fail(ErrorCodeEnum.USER_NOT_FOUND);
            }
            OrganizationTeam organizationTeam = organizationTeamRepository.findOrganizationTeamByOrganizationIdAndUserId(organizationId, userInfo.getId());
            if (organizationTeam == null) {
                organizationTeam = new OrganizationTeam(snowflakeIdGenerator.nextId(), userInfo.getId(), organizationId, SystemClock.now(), permission, PermissionStatusEnum.AVAILABLE.getCode());
            }
            organizationTeam.setStatuses( PermissionStatusEnum.AVAILABLE.getCode());
            organizationTeam.setUserId(userInfo.getId());
            organizationTeam.setPermission(permission);
            organizationTeam.setCreateTime(SystemClock.now());
            organizationTeamRepository.save(organizationTeam);
            return Result.success();
        }

        return Result.fail(ErrorCodeEnum.PERMISSION_DENIED);
    }

    @Override
    public Result<List<HashMap<String, String>>> selectOrganizationTeamByOrganizationId(Long organizationId) {
        List<HashMap<String, String>> userInfos = new ArrayList<HashMap<String, String>>();
        List<OrganizationTeam> organizationTeamList = organizationTeamRepository.findOrganizationTeamsByOrganizationIdAndStatuses(organizationId, PermissionStatusEnum.AVAILABLE.getCode());
        ListIterator<OrganizationTeam> organizationTeamListIterator = organizationTeamList.listIterator();
        while (organizationTeamListIterator.hasNext()) {
            OrganizationTeam organizationTeam = organizationTeamListIterator.next();
            Optional<UserInfo> userInfo = userInfoRepository.findById(organizationTeam.getUserId());
            HashMap<String, String> hashMap = new HashMap<String, String>();
            hashMap.put("email", userInfo.get().getEmail());
            hashMap.put("name", userInfo.get().getNickname());
            hashMap.put("permission", organizationTeam.getPermission().toString());
            userInfos.add(hashMap);
        }
        return Result.success(userInfos);
    }

    @Override
    public Result<String> updateOrganizationTeamByOrganizationId(Long organizationId, Integer permission, String userEmail) {
        //权限通过
        if (hasAdminPermission()) {
            var userInfo = userInfoRepository.findUserInfoByEmail(userEmail)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            OrganizationTeam organizationTeam = organizationTeamRepository.findOrganizationTeamByOrganizationIdAndUserId(organizationId, userInfo.getId());
            organizationTeam.setUserId(userInfo.getId());
            organizationTeam.setPermission(permission);
            organizationTeam.setCreateTime(SystemClock.now());
            organizationTeam.setStatuses(PermissionStatusEnum.AVAILABLE.getCode());
            organizationTeamRepository.save(organizationTeam);
            return Result.success();
        }
        return Result.fail(ErrorCodeEnum.PERMISSION_DENIED);
    }

    @Override
    public Result<String> deleteOrganizationTeamByOrganizationId(Long organizationId, String userEmail) {
        //权限通过
        if (hasAdminPermission()) {
            var userInfo = userInfoRepository.findUserInfoByEmail(userEmail)
                    .orElseThrow(() -> new RuntimeException("User not found"));
            OrganizationTeam organizationTeam= organizationTeamRepository.findOrganizationTeamByOrganizationIdAndUserId(organizationId, userInfo.getId());
            organizationTeam.setStatuses(PermissionStatusEnum.UNAVAILABLE.getCode());
            organizationTeamRepository.save(organizationTeam);
            return Result.success();
        }
        return Result.fail(ErrorCodeEnum.PERMISSION_DENIED);
    }
}
