package ai.yourouter.channel.key.controller;

import ai.yourouter.channel.key.service.ChannelKeyService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/api/private/channel/key")
@RequiredArgsConstructor
public class ChannelKeyController {

    private final ChannelKeyService channelKeyService;




}
