package ai.yourouter.common.auth;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * 用户权限上下文清理过滤器
 * 确保在每个请求结束后清理 ThreadLocal，防止内存泄漏
 */
@Slf4j
@Component
@Order(Ordered.LOWEST_PRECEDENCE) // 最低优先级，确保在其他过滤器之后执行，在 finally 中清理
@RequiredArgsConstructor
public class UserPermissionContextCleanupFilter implements Filter {

    private final ThreadLocalUserPermissionContext threadLocalUserPermissionContext;

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        try {
            log.debug("Request started - URI: {}, Thread: {}", 
                    httpRequest.getRequestURI(), Thread.currentThread().getName());
            
            // 继续执行请求链
            chain.doFilter(request, response);
            
        } finally {
            // 无论请求成功还是失败，都要清理 ThreadLocal
            try {
                threadLocalUserPermissionContext.clear();
                log.debug("Request completed - URI: {}, Thread: {}", 
                        httpRequest.getRequestURI(), Thread.currentThread().getName());
            } catch (Exception e) {
                log.error("Error clearing ThreadLocal context for request: {}", 
                        httpRequest.getRequestURI(), e);
            }
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        log.info("UserPermissionContextCleanupFilter initialized");
    }

    @Override
    public void destroy() {
        log.info("UserPermissionContextCleanupFilter destroyed");
    }
}
