package ai.yourouter.common.auth;

import ai.yourouter.jpa.user.info.bean.UserInfo;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.RequestScope;

@Component
@RequestScope
public class CurrentUser {

    private UserInfo userInfo;

    public UserInfo get() { return userInfo; }

    public void set(UserInfo u) { this.userInfo = u; }

    public boolean available() { return userInfo != null; }
}