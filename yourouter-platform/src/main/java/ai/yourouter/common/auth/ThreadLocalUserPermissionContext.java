package ai.yourouter.common.auth;

import ai.yourouter.jpa.user.info.bean.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 基于 ThreadLocal 的用户权限上下文管理器
 * 解决 RequestScope 代理可能导致的状态不一致问题
 */
@Slf4j
@Component
public class ThreadLocalUserPermissionContext {
    
    private static final ThreadLocal<UserPermissionData> CONTEXT = new ThreadLocal<>();
    
    /**
     * 用户权限数据
     */
    public static class UserPermissionData {
        private UserInfo userInfo;
        private Long userId;
        private Long organizationId;
        private Integer permission;
        private boolean permissionSet = false;
        
        public UserPermissionData(UserInfo userInfo, Long userId, Long organizationId, Integer permission) {
            this.userInfo = userInfo;
            this.userId = userId;
            this.organizationId = organizationId;
            this.permission = permission;
            this.permissionSet = true;
        }
        
        // Getters
        public UserInfo getUserInfo() { return userInfo; }
        public Long getUserId() { return userId; }
        public Long getOrganizationId() { return organizationId; }
        public Integer getPermission() { return permission; }
        public boolean isPermissionSet() { return permissionSet; }
    }
    
    /**
     * 设置用户信息
     */
    public void setUserInfo(UserInfo userInfo) {
        UserPermissionData current = CONTEXT.get();
        if (current == null) {
            current = new UserPermissionData(userInfo, userInfo.getId(), null, null);
            current.permissionSet = false;
        } else {
            current.userInfo = userInfo;
            current.userId = userInfo.getId();
        }
        CONTEXT.set(current);
        log.debug("ThreadLocal userInfo set - userId: {}", userInfo.getId());
    }
    
    /**
     * 设置用户权限信息
     */
    public void setUserPermission(Long userId, Long organizationId, Integer permission) {
        UserPermissionData current = CONTEXT.get();
        if (current == null) {
            current = new UserPermissionData(null, userId, organizationId, permission);
        } else {
            current.userId = userId;
            current.organizationId = organizationId;
            current.permission = permission;
            current.permissionSet = true;
        }
        CONTEXT.set(current);
        
        log.info("ThreadLocal permission set - userId: {}, orgId: {}, permission: {}, thread: {}", 
                userId, organizationId, permission, Thread.currentThread().getName());
    }
    
    /**
     * 获取当前用户权限
     */
    public Integer getPermission() {
        UserPermissionData data = CONTEXT.get();
        Integer permission = data != null ? data.getPermission() : null;
        log.debug("ThreadLocal permission get - permission: {}, thread: {}", 
                permission, Thread.currentThread().getName());
        return permission;
    }
    
    /**
     * 检查是否有管理员权限
     */
    public boolean hasAdminPermission() {
        Integer currentPermission = getPermission();
        boolean isAdmin = currentPermission != null && currentPermission == 1;
        log.debug("ThreadLocal admin permission check - permission: {}, isAdmin: {}", currentPermission, isAdmin);
        return isAdmin;
    }
    
    /**
     * 检查权限是否已设置
     */
    public boolean isPermissionSet() {
        UserPermissionData data = CONTEXT.get();
        return data != null && data.isPermissionSet();
    }
    
    /**
     * 获取用户信息
     */
    public UserInfo getUserInfo() {
        UserPermissionData data = CONTEXT.get();
        return data != null ? data.getUserInfo() : null;
    }
    
    /**
     * 获取用户ID
     */
    public Long getUserId() {
        UserPermissionData data = CONTEXT.get();
        return data != null ? data.getUserId() : null;
    }
    
    /**
     * 获取组织ID
     */
    public Long getOrganizationId() {
        UserPermissionData data = CONTEXT.get();
        return data != null ? data.getOrganizationId() : null;
    }
    
    /**
     * 清除当前线程的上下文
     * 必须在请求结束时调用，防止内存泄漏
     */
    public void clear() {
        UserPermissionData data = CONTEXT.get();
        if (data != null) {
            log.debug("ThreadLocal context clearing - userId: {}, thread: {}",
                    data.getUserId(), Thread.currentThread().getName());
        }
        CONTEXT.remove();
        log.debug("ThreadLocal context cleared - thread: {}", Thread.currentThread().getName());
    }
    
    /**
     * 获取完整的上下文数据（用于调试）
     */
    public UserPermissionData getCurrentData() {
        return CONTEXT.get();
    }
}
