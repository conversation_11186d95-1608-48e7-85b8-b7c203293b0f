package ai.yourouter.common.auth;

import ai.yourouter.jpa.user.info.bean.UserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * ThreadLocal 用户权限上下文测试
 * 用于验证权限管理重构是否正确工作
 */
@Slf4j
@Component
public class ThreadLocalUserPermissionContextTest implements CommandLineRunner {

    private final ThreadLocalUserPermissionContext contextManager;

    public ThreadLocalUserPermissionContextTest(ThreadLocalUserPermissionContext contextManager) {
        this.contextManager = contextManager;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("=== ThreadLocal User Permission Context Test ===");
        
        try {
            // 测试用户信息设置
            UserInfo testUser = new UserInfo();
            testUser.setId(12345L);
            testUser.setEmail("<EMAIL>");
            testUser.setName("Test User");
            
            contextManager.setUserInfo(testUser);
            log.info("✓ User info set successfully");
            
            // 测试权限设置
            contextManager.setUserPermission(12345L, 67890L, 1);
            log.info("✓ Permission set successfully");
            
            // 测试权限检查
            boolean hasAdmin = contextManager.hasAdminPermission();
            log.info("✓ Admin permission check: {}", hasAdmin);
            
            // 测试数据获取
            UserInfo retrievedUser = contextManager.getUserInfo();
            Long userId = contextManager.getUserId();
            Long orgId = contextManager.getOrganizationId();
            Integer permission = contextManager.getPermission();
            
            log.info("✓ Retrieved data - User: {}, UserId: {}, OrgId: {}, Permission: {}", 
                    retrievedUser != null ? retrievedUser.getName() : "null", 
                    userId, orgId, permission);
            
            // 验证数据一致性
            if (hasAdmin && permission != null && permission == 1 && userId != null && userId.equals(12345L)) {
                log.info("✅ ThreadLocal User Permission Context test PASSED");
            } else {
                log.error("❌ ThreadLocal User Permission Context test FAILED");
            }
            
        } catch (Exception e) {
            log.error("❌ ThreadLocal User Permission Context test ERROR", e);
        } finally {
            // 清理测试数据
            contextManager.clear();
            log.info("✓ Test context cleared");
        }
        
        log.info("=== Test completed ===");
    }
}
