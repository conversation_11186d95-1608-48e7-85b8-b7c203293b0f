package ai.yourouter.common.auth;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.http.*;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
@EnableCaching
@RequiredArgsConstructor
public class AuthCacheService {

    /**
     * Auth0 /userinfo 端点
     */
    @Value("${auth0.userinfo-endpoint}")
    private final String userInfoUrl;

    private final RestTemplate rest = new RestTemplate();

    private final ObjectMapper mapper = new ObjectMapper();

    /**
     * 调用 Auth0 /userinfo<br/>
     * 结果按 token 缓存 15 分钟（默认 TTL 取决于 CacheManager 配置）
     */
    @Cacheable(value = "userinfo", key = "#token", sync = true)
    public Map<String, Object> fetchUserInfo(String token) {

        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));

        ResponseEntity<String> rsp;
        try {
            rsp = rest.exchange(userInfoUrl, HttpMethod.GET, new HttpEntity<>(headers), String.class);
        } catch (Exception ex) {
            log.error("Fetching user info failed", ex);
            throw buildAuthException("UserInfo request failed", ex);
        }

        log.info("UserInfo request response: {} | {}", rsp.getBody(),rsp.getStatusCode());

        if (!rsp.getStatusCode().is2xxSuccessful()) {
            log.error("Token not active: HTTP: {}", rsp.getStatusCode());
            throw buildAuthException("Token not active: HTTP " + rsp.getStatusCode(), null);
        }

        try {
            return mapper.readValue(rsp.getBody(), new TypeReference<>() {
            });
        } catch (Exception e) {
            throw buildAuthException("Cannot parse /userinfo response", e);
        }
    }



    private OAuth2AuthenticationException buildAuthException(String msg, Exception cause) {
        OAuth2Error err = new OAuth2Error("invalid_token", msg, null);
        return new OAuth2AuthenticationException(err, msg, cause);
    }



}
