package ai.yourouter.common.auth;

import lombok.extern.slf4j.Slf4j;

/**
 * ThreadLocal 用户权限上下文感知工具类
 * 用于在异步任务或跨线程场景中传递和管理用户权限上下文
 */
@Slf4j
public class ThreadLocalUserPermissionContextAware {

    /**
     * 捕获当前线程的用户权限上下文
     * 用于在异步任务开始前保存上下文
     */
    public static ThreadLocalUserPermissionContext.UserPermissionData captureContext(
            ThreadLocalUserPermissionContext contextManager) {
        ThreadLocalUserPermissionContext.UserPermissionData data = contextManager.getCurrentData();
        if (data != null) {
            log.debug("Context captured for async task - userId: {}, thread: {}", 
                    data.getUserId(), Thread.currentThread().getName());
        }
        return data;
    }

    /**
     * 在新线程中恢复用户权限上下文
     * 用于在异步任务执行时设置上下文
     */
    public static void restoreContext(ThreadLocalUserPermissionContext contextManager, 
                                    ThreadLocalUserPermissionContext.UserPermissionData data) {
        if (data != null) {
            if (data.getUserInfo() != null) {
                contextManager.setUserInfo(data.getUserInfo());
            }
            if (data.isPermissionSet()) {
                contextManager.setUserPermission(data.getUserId(), data.getOrganizationId(), data.getPermission());
            }
            log.debug("Context restored in async task - userId: {}, thread: {}", 
                    data.getUserId(), Thread.currentThread().getName());
        }
    }

    /**
     * 清理当前线程的用户权限上下文
     * 用于在异步任务结束时清理上下文
     */
    public static void clearContext(ThreadLocalUserPermissionContext contextManager) {
        contextManager.clear();
        log.debug("Context cleared in async task - thread: {}", Thread.currentThread().getName());
    }

    /**
     * 包装 Runnable，自动处理上下文传递和清理
     */
    public static Runnable wrapRunnable(ThreadLocalUserPermissionContext contextManager, Runnable task) {
        ThreadLocalUserPermissionContext.UserPermissionData capturedData = captureContext(contextManager);
        
        return () -> {
            try {
                restoreContext(contextManager, capturedData);
                task.run();
            } finally {
                clearContext(contextManager);
            }
        };
    }

    /**
     * 包装 Callable，自动处理上下文传递和清理
     */
    public static <T> java.util.concurrent.Callable<T> wrapCallable(
            ThreadLocalUserPermissionContext contextManager, 
            java.util.concurrent.Callable<T> task) {
        ThreadLocalUserPermissionContext.UserPermissionData capturedData = captureContext(contextManager);
        
        return () -> {
            try {
                restoreContext(contextManager, capturedData);
                return task.call();
            } finally {
                clearContext(contextManager);
            }
        };
    }
}
