package ai.yourouter.common.utils;

import javax.naming.directory.*;
import java.util.Hashtable;

public class DnsLookupUtils {

    private DnsLookupUtils(){}

    public static boolean hasMx(String domain) {
        try {
            DirContext ictx = new InitialDirContext(env());
            Attributes attrs = ictx.getAttributes("dns:/" + domain, new String[]{"MX"});
            Attribute mx = attrs.get("MX");
            return mx != null && mx.size() > 0;
        } catch (Exception e) {
            return false;
        }
    }
    public static boolean hasA(String domain) {
        try {
            DirContext ictx = new InitialDirContext(env());
            Attributes attrs = ictx.getAttributes("dns:/" + domain, new String[]{"A"});
            Attribute a = attrs.get("A");
            return a != null && a.size() > 0;
        } catch (Exception e) {
            return false;
        }
    }
    private static Hashtable<String, String> env() {
        Hashtable<String, String> env = new Hashtable<>();
        env.put("java.naming.factory.initial", "com.sun.jndi.dns.DnsContextFactory");
        env.put("java.naming.provider.url", "dns:");
        env.put("com.sun.jndi.dns.timeout.initial", "2000"); // 2s
        env.put("com.sun.jndi.dns.timeout.retries", "1");
        return env;
    }
}
