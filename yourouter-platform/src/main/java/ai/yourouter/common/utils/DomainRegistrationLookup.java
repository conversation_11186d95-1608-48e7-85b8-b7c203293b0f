package ai.yourouter.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;

import java.time.Instant;
import java.util.Optional;

public class DomainRegistrationLookup {

    private DomainRegistrationLookup(){}

    public static Optional<Instant> lookupCreationInstant(String domain) {
        Optional<Instant> rdap = RdapUtils.queryRegistrationInstant(domain);
        if (rdap.isPresent()) return rdap;
        return WhoisClientUtils.queryCreationInstant(domain);
    }
    @SneakyThrows
    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();

        System.out.println(DomainRegistrationLookup.lookupCreationInstant("hispread.com").get());
    }
}
