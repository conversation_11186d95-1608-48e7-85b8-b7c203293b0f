package ai.yourouter.common.utils;

import java.util.Locale;
import java.util.Set;

public final class EmailRules {
    private EmailRules(){}

    private static final Set<String> FREE_DOMAINS = Set.of(
            "gmail.com","googlemail.com","yahoo.com","hotmail.com","outlook.com","live.com","icloud.com",
            "qq.com","163.com","126.com","yeah.net","foxmail.com",
            "proton.me","protonmail.com","gmx.com","yandex.com","mail.com","zoho.com"
    );
    private static final Set<String> DISPOSABLE_HINTS = Set.of(
            "tempmail","mailinator","10minutemail","guerrillamail","yopmail","trashmail","sharklasers"
    );
    private static final Set<String> ROLE_LOCALPARTS = Set.of(
            "admin","administrator","info","support","contact","hr","jobs","hello","sales","marketing",
            "billing","accounts","postmaster","webmaster","security","abuse","noreply","no-reply"
    );

    public static boolean isFreeDomain(String domain) {
        return FREE_DOMAINS.contains(domain.toLowerCase(Locale.ROOT));
    }
    public static boolean isDisposableDomain(String domain) {
        String d = domain.toLowerCase(Locale.ROOT);
        return DISPOSABLE_HINTS.stream().anyMatch(d::contains);
    }
    public static boolean isRoleLocalPart(String local) {
        return ROLE_LOCALPARTS.contains(local.toLowerCase(Locale.ROOT));
    }
}