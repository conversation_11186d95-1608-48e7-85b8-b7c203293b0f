package ai.yourouter.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;

public class RdapUtils {

    private RdapUtils(){}

    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final HttpClient HTTP = HttpClient.newBuilder()
            .version(HttpClient.Version.HTTP_2)
            .build();

    // 常见 TLD RDAP 端点；优先使用 tld 专属端点，其次通用聚合 rdap.org
    private static final Map<String, List<String>> RDAP_ENDPOINTS = Map.of(
            "com", List.of("https://rdap.verisign.com/com/v1/domain/%s", "https://rdap.org/domain/%s"),
            "net", List.of("https://rdap.verisign.com/net/v1/domain/%s", "https://rdap.org/domain/%s"),
            "org", List.of("https://rdap.publicinterestregistry.net/rdap/org/domain/%s", "https://rdap.org/domain/%s"),
            "io",  List.of("https://rdap.nic.io/domain/%s", "https://rdap.org/domain/%s")
    );

    public static Optional<Instant> queryRegistrationInstant(String domain) {
        String tld = tldOf(domain);
        List<String> templates = RDAP_ENDPOINTS.getOrDefault(tld, List.of("https://rdap.org/domain/%s"));
        for (String tpl : templates) {
            try {
                String url = String.format(tpl, domain);
                HttpRequest req = HttpRequest.newBuilder(URI.create(url))
                        .GET().header("Accept", "application/rdap+json, application/json").build();
                HttpResponse<String> resp = HTTP.send(req, HttpResponse.BodyHandlers.ofString());
                if (resp.statusCode() >= 200 && resp.statusCode() < 300) {
                    Optional<Instant> t = parseRegistration(resp.body());
                    if (t.isPresent()) return t;
                }
            } catch (Exception ignore) {}
        }
        return Optional.empty();
    }

    private static Optional<Instant> parseRegistration(String json) {
        try {
            JsonNode root = MAPPER.readTree(json);
            if (root.has("events")) {
                for (JsonNode ev : root.get("events")) {
                    String action = ev.path("eventAction").asText("").toLowerCase(Locale.ROOT);
                    if ("registration".equals(action) || "registered".equals(action)) {
                        String dt = ev.path("eventDate").asText(null);
                        if (dt != null && !dt.isBlank()) return Optional.of(Instant.parse(dt));
                    }
                }
            }
        } catch (Exception ignore) {}
        return Optional.empty();
    }

    private static String tldOf(String domain) {
        String d = domain.toLowerCase(Locale.ROOT).trim();
        int i = d.lastIndexOf('.');
        return (i > 0 && i < d.length() - 1) ? d.substring(i + 1) : d;
    }
}
