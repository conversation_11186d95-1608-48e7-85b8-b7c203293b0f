package ai.yourouter.common.utils;


import org.apache.commons.net.whois.WhoisClient;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class WhoisClientUtils {

    private WhoisClientUtils(){}

    private static final int TIMEOUT_MS = 5000;

    // 更多样式的创建时间字段
    private static final List<Pattern> CREATION_PATTERNS = List.of(
            Pattern.compile("(?im)^Creation Date:\\s*(.+)$"),
            Pattern.compile("(?im)^created:\\s*(.+)$"),
            Pattern.compile("(?im)^Created:\\s*(.+)$"),
            Pattern.compile("(?im)^Created On:\\s*(.+)$"),
            Pattern.compile("(?im)^Domain Create Date:\\s*(.+)$"),
            Pattern.compile("(?im)^Domain Registration Date:\\s*(.+)$"),
            Pattern.compile("(?im)^Registered On:\\s*(.+)$"),
            Pattern.compile("(?im)^(注册时间|创建时间)[:：]\\s*(.+)$")
    );

    // 常见日期格式
    private static final List<DateTimeFormatter> DATE_FORMATS = List.of(
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssX", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH).withZone(ZoneId.systemDefault()),
            DateTimeFormatter.ofPattern("yyyy-MM-dd", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("dd-MMM-yyyy", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("dd-MMM-yyyy HH:mm:ss", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("yyyy.MM.dd HH:mm:ss", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("yyyy.MM.dd", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("dd.MM.yyyy", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("yyyy/MM/dd", Locale.ENGLISH).withZone(ZoneOffset.UTC),
            DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH).withZone(ZoneOffset.UTC)
    );

    /** 主流程：IANA → Registry → Registrar，且每步尝试多种查询语法 */
    public static Optional<Instant> queryCreationInstant(String domain) {
        try {
            String tld = tldOf(domain);
            Optional<String> iana = query("whois.iana.org", tld);
            String registry = parseFirstMatch(iana.orElse(""), "(?im)^refer:\\s*(\\S+)$")
                    .orElse(guessRegistryByTld(tld));

            // 1) Registry（例如 verisign）
            Optional<String> regText = queryWithVariants(registry, domain);
            Optional<Instant> t = parseCreation(regText.orElse(""));
            if (t.isPresent()) return t;

            // 2) Registrar WHOIS Server
            Optional<String> registrarServer = parseFirstMatch(regText.orElse(""),
                    "(?im)^Registrar WHOIS Server:\\s*(\\S+)$");
            if (registrarServer.isPresent()) {
                Optional<String> regarText = queryWithVariants(registrarServer.get(), domain);
                Optional<Instant> t2 = parseCreation(regarText.orElse(""));
                if (t2.isPresent()) return t2;
            }

            // 3) .com/.net 再强制走 verisign 精确查询
            if (("com".equalsIgnoreCase(tld) || "net".equalsIgnoreCase(tld))
                    && (registry == null || !registry.contains("verisign"))) {
                Optional<String> v = queryWithVariants("whois.verisign-grs.com", domain);
                Optional<Instant> t3 = parseCreation(v.orElse(""));
                if (t3.isPresent()) return t3;
            }

            return Optional.empty();
        } catch (Exception e) {
            return Optional.empty();
        }
    }

    /* ================= 内部工具 ================= */

    private static Optional<String> queryWithVariants(String server, String domain) {
        // 依次尝试：domain <name>、=<name>、<name>
        for (String q : List.of("domain " + domain, "=" + domain, domain)) {
            Optional<String> r = query(server, q);
            if (r.isPresent() && !looksLikeNoMatch(r.get())) return r;
        }
        return Optional.empty();
    }

    private static Optional<String> query(String server, String query) {
        WhoisClient client = new WhoisClient();
        try {
            client.setDefaultTimeout(TIMEOUT_MS);
            client.setSoTimeout(TIMEOUT_MS);
            client.connect(server, WhoisClient.DEFAULT_PORT);
            String raw = client.query(query);
            // 统一转为 UTF-8
            return Optional.of(new String(raw.getBytes(StandardCharsets.ISO_8859_1), StandardCharsets.UTF_8));
        } catch (IOException ignore) {
            return Optional.empty();
        } finally {
            if (client.isConnected()) try { client.disconnect(); } catch (IOException ignored) {}
        }
    }

    private static boolean looksLikeNoMatch(String text) {
        if (text == null) return true;
        String t = text.toLowerCase(Locale.ROOT);
        return t.contains("no match for") || t.contains("no entries found")
                || t.contains("not found") || t.contains("limit exceeded");
    }

    private static Optional<Instant> parseCreation(String whoisText) {
        if (whoisText == null || whoisText.isBlank()) return Optional.empty();
        for (Pattern p : CREATION_PATTERNS) {
            Matcher m = p.matcher(whoisText);
            if (m.find()) {
                String raw = m.groupCount() >= 2 ? m.group(2) : m.group(1);
                Optional<Instant> parsed = tryParseDate(raw);
                if (parsed.isPresent()) return parsed;
            }
        }
        return Optional.empty();
    }

    private static Optional<String> parseFirstMatch(String text, String regex) {
        Matcher m = Pattern.compile(regex).matcher(text);
        return m.find() ? Optional.ofNullable(m.group(1).trim()) : Optional.empty();
    }

    private static Optional<Instant> tryParseDate(String raw) {
        if (raw == null) return Optional.empty();
        String s = raw.trim().replace("UTC", "").replaceAll("\\s+Z$", "Z").trim();
        for (DateTimeFormatter f : DATE_FORMATS) {
            try {
                try { return Optional.of(Instant.from(f.parse(s))); }
                catch (DateTimeParseException ignored) {
                    return Optional.of(LocalDate.parse(s, f).atStartOfDay(ZoneOffset.UTC).toInstant());
                }
            } catch (DateTimeParseException ignored) {}
        }
        return Optional.empty();
    }

    private static String tldOf(String domain) {
        String d = domain.toLowerCase(Locale.ROOT).trim();
        int i = d.lastIndexOf('.');
        return (i > 0 && i < d.length() - 1) ? d.substring(i + 1) : d;
    }

    private static String guessRegistryByTld(String tld) {
        String tl = tld.toLowerCase(Locale.ROOT);
        if ("com".equals(tl) || "net".equals(tl)) return "whois.verisign-grs.com";
        if ("org".equals(tl)) return "whois.pir.org";
        if ("io".equals(tl))  return "whois.nic.io";
        return "whois.nic." + tl;
    }
}
