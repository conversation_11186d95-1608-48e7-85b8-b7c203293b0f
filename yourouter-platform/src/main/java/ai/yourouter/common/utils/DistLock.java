//package ai.yourouter.common.utils;
//
//import lombok.RequiredArgsConstructor;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.stereotype.Component;
//
//import java.time.Duration;
//import java.util.UUID;
//
//@Component
//@RequiredArgsConstructor
//public class DistLock {
//    private final StringRedisTemplate redis;
//
//    private static final String RELEASE_LUA = """
//        if redis.call('GET', KEYS[1]) == ARGV[1] then
//          return redis.call('DEL', KEYS[1])
//        else
//          return 0
//        end
//        """;
//
//    public String tryLock(String key, Duration ttl) {
//        String token = UUID.randomUUID().toString();
//        Boolean ok = redis.opsForValue().setIfAbsent(key, token, ttl);
//        return Boolean.TRUE.equals(ok) ? token : null;
//    }
//
//    public void unlock(String key, String token) {
//        if (token == null) return;
//        redis.execute((RedisCallback<Object>) conn ->
//                conn.scriptingCommands().eval(
//                        RELEASE_LUA.getBytes(StandardCharsets.UTF_8),
//                        ReturnType.INTEGER, 1, key.getBytes(StandardCharsets.UTF_8),
//                        token.getBytes(StandardCharsets.UTF_8)
//                )
//        );
//    }
//}