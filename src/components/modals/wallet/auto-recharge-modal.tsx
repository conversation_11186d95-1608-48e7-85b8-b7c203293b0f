import { useState, useEffect } from "react";
import { useAtomValue } from "jotai";
import { useRequest } from "ahooks";
import { toast } from "sonner";

import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import CommonButton from "@/components/ui/button/new-button";

import { organizationState } from "@/state/user-state";
import { WalletApi } from "@/api/wallet/wallet-api";

interface AutoRechargeModalProps {
    onClose: () => void;
    onSuccess?: () => void;
    currentSettings?: {
        minimumAmount: number;
        rechargeAmount: number;
    };
}

export const AutoRechargeModal = ({ onClose, onSuccess, currentSettings }: AutoRechargeModalProps) => {
    const [isOpen, setIsOpen] = useState(true);
    const [minimumAmount, setMinimumAmount] = useState<string>("");
    const [rechargeAmount, setRechargeAmount] = useState<string>("");

    const organization = useAtomValue(organizationState);

    // 更新自动充值设置
    const { runAsync: updateAutoWallet, loading: updateLoading } = useRequest(WalletApi.updateAutoWallet, {
        manual: true,
        onSuccess: () => {
            // Update auto recharge settings success
            toast.success("Auto recharge settings updated successfully");
            onSuccess?.();
            handleClose();
        },
        onError: (error) => {
            toast.error("Update failed: " + error.message);
        }
    });

    // 初始化设置值
    useEffect(() => {
        // Initialize setting values
        if (currentSettings) {
            setMinimumAmount(currentSettings.minimumAmount.toString());
            setRechargeAmount(currentSettings.rechargeAmount.toString());
        }
    }, [currentSettings]);

    const handleClose = () => {
        setIsOpen(false);
        onClose();
    };

    const handleSave = async () => {
        if (!organization || !minimumAmount || !rechargeAmount) return;

        const minAmount = parseFloat(minimumAmount);
        const rechAmount = parseFloat(rechargeAmount);

        if (isNaN(minAmount) || isNaN(rechAmount) || minAmount <= 0 || rechAmount <= 0) {
            toast.error("Please enter valid amounts");
            return;
        }

        await updateAutoWallet({
            organizationId: organization.organizationId,
            minimumAmount: minAmount,
            rechargeAmount: rechAmount
        });
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
                <DialogHeader>
                    <DialogTitle>Auto Recharge Settings</DialogTitle>
                    <DialogDescription>
                        Set auto recharge rules to automatically recharge when account balance is insufficient
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    <div className="space-y-2">
                        <Label htmlFor="minimumAmount">Minimum Balance Threshold ($)</Label>
                        <Input
                            id="minimumAmount"
                            type="number"
                            value={minimumAmount}
                            onChange={(e) => setMinimumAmount(e.target.value)}
                            placeholder="Auto recharge will be triggered when balance falls below this value"
                        />
                    </div>
                    <div className="space-y-2">
                        <Label htmlFor="rechargeAmount">Auto Recharge Amount ($)</Label>
                        <Input
                            id="rechargeAmount"
                            type="number"
                            value={rechargeAmount}
                            onChange={(e) => setRechargeAmount(e.target.value)}
                            placeholder="Amount to recharge each time"
                        />
                    </div>
                </div>

                <DialogFooter>
                    <CommonButton variant="outline" onClick={handleClose}>
                        Cancel
                    </CommonButton>
                    <CommonButton onClick={handleSave} disabled={updateLoading}>
                        {updateLoading ? "Saving..." : "Save Settings"}
                    </CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}; 