# 钱包模态框组件

本目录包含钱包相关的模态框组件，提供充值、自动充值和退款功能。

## 组件列表

### 1. RechargeModal (充值模态框)
- **文件**: `recharge-modal.tsx`
- **功能**: 手动充值钱包余额
- **特性**: 
  - 预设金额选择（$10, $25, $50, $100, $200, $500）
  - 自定义金额输入
  - 集成Stripe支付
  - 实时金额预览

### 2. AutoRechargeModal (自动充值模态框)
- **文件**: `auto-recharge-modal.tsx`
- **功能**: 设置自动充值规则
- **特性**:
  - 最低余额阈值设置
  - 自动充值金额设置
  - 开启/关闭自动充值

### 3. RefundModal (退款模态框) ✨ 新增
- **文件**: `refund-modal.tsx`
- **功能**: 申请余额退款
- **特性**:
  - 自动检查可退款金额
  - 支持部分退款或全额退款
  - 显示最近充值记录ID
  - 退款状态跟踪
  - 安全提示和确认流程

## 使用方式

### 调用退款模态框
```typescript
import { pushModal } from "@/components/modals";

const handleOpenRefundModal = () => {
    pushModal("RefundModal", {
        onClose: () => {
            // 模态框关闭回调
        },
        onSuccess: () => {
            // 退款成功回调
            setRefreshTable(prev => prev + 1);
            toast.success("Refund request submitted successfully!");
        }
    });
};
```

### API接口调用
```typescript
import { PaymentApi } from "@/api/payment/payment-api";

// 检查可退款金额
const refundInfo = await PaymentApi.checkRefund(organizationId);

// 申请退款
await PaymentApi.refundCharge(organizationId, amountUsdLong);
```

## 设计规范

所有钱包模态框都遵循统一的设计规范：

### 色彩方案
- **充值**: 蓝色主题 `bg-blue-100`, `text-blue-800`
- **退款**: 橙色主题 `bg-orange-600`, `hover:bg-orange-700`
- **自动充值**: 绿色主题 `bg-emerald-600`

### 布局规范
- 模态框最大宽度: `sm:max-w-md`
- 标准圆角: `rounded-lg`
- 标准内边距: `p-4`, `px-6 py-4`
- 间距规范: `gap-3`, `gap-6`

### 交互设计
- 150ms过渡动画
- Loading状态指示
- 表单验证提示
- 成功/错误Toast消息

## 后端接口集成

### 退款相关接口

#### 1. 检查可退款金额
- **端点**: `GET /api/private/stripe/payment/checkRefund`
- **参数**: `organizationId`
- **返回**: 
  ```json
  {
    "refundableAmount": "5000",  // 可退款金额（美分）
    "lastChargeId": "ch_xxx"     // 最近充值记录ID
  }
  ```

#### 2. 申请退款
- **端点**: `POST /api/private/stripe/payment/refund`
- **参数**: 
  - `organizationId`: 组织ID
  - `amountUsdLong`: 退款金额（美分）
- **返回**: 成功/失败状态

## 安全考虑

1. **权限验证**: 所有API调用都需要组织权限验证
2. **金额验证**: 
   - 退款金额不能超过可退款余额
   - 最小退款金额限制
3. **防重复提交**: 退款请求防重复机制
4. **数据加密**: 敏感数据传输加密

## 用户体验优化

1. **实时反馈**: 加载状态、成功/失败提示
2. **错误处理**: 友好的错误消息显示
3. **数据格式化**: 货币金额本地化显示
4. **响应式设计**: 移动端适配
5. **无障碍支持**: 键盘导航、屏幕阅读器支持

## 更新日志

### v1.0.0 - 2025-01-25
- ✨ 新增退款模态框 (RefundModal)
- 🔧 完善PaymentAPI退款接口
- 📝 添加退款相关类型定义
- 🎨 统一钱包模态框设计风格 