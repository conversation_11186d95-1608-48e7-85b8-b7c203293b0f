import { useState } from "react";
import { useAtomValue } from "jotai";
import { useRequest } from "ahooks";
import { toast } from "sonner";
import { CreditCard, DollarSign, ExternalLink } from "lucide-react";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import CommonButton from "@/components/ui/button/new-button";

import { organizationState } from "@/state/user-state";
import { WalletApi } from "@/api/wallet/wallet-api";
import { cn } from "@/lib/utils";

interface RechargeModalProps {
    onClose: () => void;
    onSuccess?: () => void;
}

const PRESET_AMOUNTS = [10, 25, 50, 100, 200, 500];

export const RechargeModal = ({ onClose }: RechargeModalProps) => {
    const [isOpen, setIsOpen] = useState(true);
    const [amount, setAmount] = useState<string>("");
    const [selectedPreset, setSelectedPreset] = useState<number | null>(null);

    const organization = useAtomValue(organizationState);

    // Create Stripe checkout session
    const { runAsync: createCheckout, loading: checkoutLoading } = useRequest(
        WalletApi.createStripeCheckout,
        {
            manual: true,
            onSuccess: (result) => {
                // Open Stripe checkout in new tab
                window.open(result.checkoutUrl, '_blank');
                toast.success("Redirecting to Stripe checkout...");
                handleClose(); // Close modal immediately after opening checkout
            },
            onError: (error) => {
                toast.error("Failed to create checkout session: " + error.message);
            }
        }
    );

    const handleClose = () => {
        setIsOpen(false);
        onClose();
    };

    const handlePresetClick = (presetAmount: number) => {
        setSelectedPreset(presetAmount);
        setAmount(presetAmount.toString());
    };

    const handleAmountChange = (value: string) => {
        setAmount(value);
        setSelectedPreset(null); // Clear preset selection when typing custom amount
    };

    const handleRecharge = async () => {
        if (!organization || !amount) return;

        const rechargeAmount = parseFloat(amount);
        if (isNaN(rechargeAmount) || rechargeAmount <= 0) {
            toast.error("Please enter a valid amount");
            return;
        }

        if (rechargeAmount < 1) {
            toast.error("Minimum recharge amount is $1.00");
            return;
        }

        if (rechargeAmount > 10000) {
            toast.error("Maximum recharge amount is $10,000.00");
            return;
        }

        await createCheckout(organization.organizationId, rechargeAmount);
    };

    const formatAmount = (amount: number) => {
        return amount.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    };

    const currentAmount = parseFloat(amount) || 0;

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
                <DialogHeader className="space-y-4 pb-6 border-b border-zinc-100">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-zinc-100 rounded-lg flex items-center justify-center">
                            <CreditCard className="w-5 h-5 text-zinc-700" />
                        </div>
                        <div className="text-left">
                            <DialogTitle className="text-lg font-medium text-zinc-900">Add Funds</DialogTitle>
                            <DialogDescription className="text-sm text-zinc-500 mt-1">
                                Add funds to your wallet balance
                            </DialogDescription>
                        </div>
                    </div>
                </DialogHeader>

                <div className="space-y-6 py-6">
                    {/* Preset amounts */}
                    <div className="space-y-4">
                        <Label className="text-sm font-medium text-zinc-900">Select Amount</Label>
                        <div className="grid grid-cols-3 gap-3">
                            {PRESET_AMOUNTS.map((presetAmount) => (
                                <Button
                                    key={presetAmount}
                                    variant="outline"
                                    className={cn(
                                        "h-11 text-sm font-medium transition-all duration-150 border-zinc-200",
                                        selectedPreset === presetAmount
                                            ? "border-zinc-900 bg-zinc-900 text-white shadow-sm"
                                            : "hover:border-zinc-300 hover:bg-zinc-50"
                                    )}
                                    onClick={() => handlePresetClick(presetAmount)}
                                >
                                    <DollarSign className="w-4 h-4 mr-1" />
                                    {presetAmount}
                                </Button>
                            ))}
                        </div>
                    </div>

                    {/* Custom amount */}
                    <div className="space-y-4">
                        <Label htmlFor="customAmount" className="text-sm font-medium text-zinc-900">
                            Custom Amount
                        </Label>
                        <div className="relative">
                            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-zinc-400" />
                            <Input
                                id="customAmount"
                                type="number"
                                value={amount}
                                onChange={(e) => handleAmountChange(e.target.value)}
                                placeholder="0.00"
                                className="pl-10 h-11 border-zinc-200 focus:border-zinc-400 focus:ring-0"
                                min="1"
                                max="10000"
                                step="0.01"
                            />
                        </div>
                        <p className="text-xs text-zinc-400">
                            Minimum $1.00 • Maximum $10,000.00
                        </p>
                    </div>

                    {/* Amount preview */}
                    {currentAmount > 0 && (
                        <div className="bg-zinc-50 border border-zinc-200 rounded-lg p-4">
                            <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-zinc-600">
                                    Amount to add
                                </span>
                                <span className="text-lg font-semibold text-zinc-900">
                                    {formatAmount(currentAmount)}
                                </span>
                            </div>
                        </div>
                    )}
                </div>

                <div className="flex gap-3 pt-6 border-t border-zinc-100">
                    <CommonButton
                        variant="outline"
                        onClick={handleClose}
                        className="flex-1 border-zinc-200 text-zinc-700 hover:bg-zinc-50"
                    >
                        Cancel
                    </CommonButton>
                    <CommonButton
                        onClick={handleRecharge}
                        disabled={checkoutLoading || !amount || currentAmount <= 0}
                        className="flex-1 bg-zinc-900 hover:bg-zinc-800 text-white border-0"
                    >
                        {checkoutLoading ? (
                            "Processing..."
                        ) : (
                            <>
                                Continue to Payment
                                <ExternalLink className="w-4 h-4 ml-2" />
                            </>
                        )}
                    </CommonButton>
                </div>

                {/* Security notice */}
                <div className="text-center pt-3">
                    <p className="text-xs text-zinc-400">
                        Secured by Stripe
                    </p>
                </div>
            </DialogContent>
        </Dialog>
    );
};
