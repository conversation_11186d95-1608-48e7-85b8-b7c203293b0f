import { useState, useEffect } from "react";
import { useAtomValue } from "jotai";
import { useRequest } from "ahooks";
import { toast } from "sonner";
import { ArrowLeft, DollarSign, AlertTriangle, RefreshCw } from "lucide-react";

import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import CommonButton from "@/components/ui/button/new-button";

import { organizationState } from "@/state/user-state";
import { PaymentApi } from "@/api/payment/payment-api";

interface RefundModalProps {
    onClose: () => void;
    onSuccess?: () => void;
}

export const RefundModal = ({ onClose, onSuccess }: RefundModalProps) => {
    const [isOpen, setIsOpen] = useState(true);
    const [refundAmount, setRefundAmount] = useState<string>("");
    const [maxRefundableAmount, setMaxRefundableAmount] = useState<number>(0);

    const organization = useAtomValue(organizationState);

    // 检查可退款金额
    const { run: checkRefund, loading: checkRefundLoading } = useRequest(
        PaymentApi.checkRefund,
        {
            manual: true,
            onSuccess: (result) => {
                // 向下舍入到6位小数，避免多退钱
                const refundableAmount = Math.floor(parseFloat(result.refundable) * 1000000) / 1000000;
                setMaxRefundableAmount(refundableAmount);
                // 默认设置为最大可退款金额，显示2位小数
                const displayAmount = Math.floor(refundableAmount * 100) / 100;
                setRefundAmount(displayAmount.toFixed(2));
            },
            onError: (error) => {
                toast.error("Failed to check refund info: " + error.message);
                handleClose();
            }
        }
    );

    // 申请退款
    const { runAsync: processRefund, loading: refundLoading } = useRequest(
        PaymentApi.refundCharge,
        {
            manual: true,
            onSuccess: (result) => {
                const requestedAmount = parseFloat(result.requestedUsd);
                const refundedAmount = parseFloat(result.refundedUsd);
                const unrefundedAmount = parseFloat(result.unrefundedUsd);

                const message = `Refund request submitted successfully!
                
Requested: ${formatAmount(requestedAmount)}
Refunded: ${formatAmount(refundedAmount)}
Unrefunded: ${formatAmount(unrefundedAmount)}

Processing may take 3-5 business days.`;

                toast.success(message, {
                    duration: 8000, // 延长显示时间以便用户阅读
                });
                onSuccess?.();
                handleClose();
            },
            onError: (error) => {
                toast.error("Failed to process refund: " + error.message);
            }
        }
    );

    // 组件加载时检查可退款金额
    useEffect(() => {
        if (organization?.organizationId) {
            checkRefund(organization.organizationId);
        }
    }, [organization?.organizationId, checkRefund]);

    const handleClose = () => {
        setIsOpen(false);
        onClose();
    };

    const handleRefundAmountChange = (value: string) => {
        const numValue = parseFloat(value);
        if (isNaN(numValue) || numValue < 0) {
            setRefundAmount("");
            return;
        }
        if (numValue > maxRefundableAmount) {
            // 向下舍入到2位小数显示
            const flooredAmount = Math.floor(maxRefundableAmount * 100) / 100;
            setRefundAmount(flooredAmount.toFixed(2));
            return;
        }
        setRefundAmount(value);
    };

    const handleRefund = async () => {
        if (!organization || !refundAmount) return;

        const amount = parseFloat(refundAmount);
        if (isNaN(amount) || amount <= 0) {
            toast.error("Please enter a valid refund amount");
            return;
        }

        if (amount > maxRefundableAmount) {
            // 向下舍入显示最大金额
            const flooredMax = Math.floor(maxRefundableAmount * 100) / 100;
            toast.error(`Refund amount cannot exceed $${flooredMax.toFixed(2)}`);
            return;
        }

        await processRefund(organization.organizationId, amount);
    };

    const formatAmount = (amount: number) => {
        // 向下舍入到2位小数，避免多退钱
        const flooredAmount = Math.floor(amount * 100) / 100;
        return flooredAmount.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    };

    const currentRefundAmount = parseFloat(refundAmount) || 0;

    if (checkRefundLoading) {
        return (
            <Dialog open={isOpen} onOpenChange={setIsOpen}>
                <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
                    <div className="flex items-center justify-center py-12">
                        <RefreshCw className="w-6 h-6 animate-spin text-zinc-400 mr-2" />
                        <span className="text-sm text-zinc-500">Checking refund availability...</span>
                    </div>
                </DialogContent>
            </Dialog>
        );
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
                <DialogHeader className="space-y-4 pb-6 border-b border-zinc-100">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                            <ArrowLeft className="w-5 h-5 text-orange-600" />
                        </div>
                        <div className="text-left">
                            <DialogTitle className="text-lg font-medium text-zinc-900">Request Refund</DialogTitle>
                            <DialogDescription className="text-sm text-zinc-500 mt-1">
                                Refund your unused balance to the original payment method
                            </DialogDescription>
                        </div>
                    </div>
                </DialogHeader>

                <div className="space-y-6 py-6">
                    {/* 可退款金额显示 */}
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center gap-2 mb-2">
                            <DollarSign className="w-4 h-4 text-blue-600" />
                            <span className="text-sm font-medium text-blue-900">
                                Available for Refund
                            </span>
                        </div>
                        <div className="text-2xl font-bold text-blue-900">
                            {formatAmount(maxRefundableAmount)}
                        </div>
                    </div>

                    {maxRefundableAmount <= 0 ? (
                        <div className="bg-zinc-50 border border-zinc-200 rounded-lg p-4 text-center">
                            <AlertTriangle className="w-6 h-6 text-zinc-400 mx-auto mb-2" />
                            <p className="text-sm text-zinc-600">
                                No refundable amount available
                            </p>
                            <p className="text-xs text-zinc-400 mt-1">
                                Only unused balance from recent charges can be refunded
                            </p>
                        </div>
                    ) : (
                        <>
                            {/* 退款金额输入 */}
                            <div className="space-y-4">
                                <Label htmlFor="refundAmount" className="text-sm font-medium text-zinc-900">
                                    Refund Amount
                                </Label>
                                <div className="relative">
                                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-zinc-400" />
                                    <Input
                                        id="refundAmount"
                                        type="number"
                                        value={refundAmount}
                                        onChange={(e) => handleRefundAmountChange(e.target.value)}
                                        placeholder="0.00"
                                        className="pl-10 h-11 border-zinc-200 focus:border-zinc-400 focus:ring-0"
                                        min="0"
                                        max={maxRefundableAmount}
                                        step="0.01"
                                    />
                                </div>
                                <div className="flex justify-between items-center">
                                    <p className="text-xs text-zinc-400">
                                        Maximum: {formatAmount(maxRefundableAmount)}
                                    </p>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                            // 向下舍入到2位小数显示
                                            const flooredAmount = Math.floor(maxRefundableAmount * 100) / 100;
                                            setRefundAmount(flooredAmount.toFixed(2));
                                        }}
                                        className="text-xs text-blue-600 hover:text-blue-700 hover:bg-blue-50 h-auto p-1"
                                    >
                                        Max
                                    </Button>
                                </div>
                            </div>

                            {/* 退款金额预览 */}
                            {currentRefundAmount > 0 && (
                                <div className="bg-zinc-50 border border-zinc-200 rounded-lg p-4">
                                    <div className="flex items-center justify-between">
                                        <span className="text-sm font-medium text-zinc-600">
                                            Refund amount
                                        </span>
                                        <span className="text-lg font-semibold text-zinc-900">
                                            {formatAmount(currentRefundAmount)}
                                        </span>
                                    </div>
                                </div>
                            )}

                            {/* 重要提示 */}
                            <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                                <div className="flex-1">
                                    <p className="text-sm text-amber-800">
                                        <span className="font-medium">Important Information</span>
                                        <br />
                                        • Refunds typically take 3-5 business days to process
                                        <br />
                                        • Funds will be returned to your original payment method
                                        <br />
                                        • This action cannot be undone once submitted
                                    </p>
                                </div>
                            </div>
                        </>
                    )}
                </div>

                <div className="flex gap-3 pt-6 border-t border-zinc-100">
                    <CommonButton
                        variant="outline"
                        onClick={handleClose}
                        className="flex-1 border-zinc-200 text-zinc-700 hover:bg-zinc-50"
                    >
                        Cancel
                    </CommonButton>
                    {maxRefundableAmount > 0 && (
                        <CommonButton
                            onClick={handleRefund}
                            disabled={refundLoading || !refundAmount || currentRefundAmount <= 0}
                            className="flex-1 bg-orange-600 hover:bg-orange-700 text-white border-0"
                        >
                            {refundLoading ? (
                                <>
                                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                                    Processing...
                                </>
                            ) : (
                                `Request Refund`
                            )}
                        </CommonButton>
                    )}
                </div>

                {/* 安全提示 */}
                <div className="text-center pt-3">
                    <p className="text-xs text-zinc-400">
                        Secured by Stripe • Refunds are processed securely
                    </p>
                </div>
            </DialogContent>
        </Dialog>
    );
}; 