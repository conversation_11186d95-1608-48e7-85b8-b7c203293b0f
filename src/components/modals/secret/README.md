# 密钥创建模态框组件

## 组件概述

`CreateSecretModal` 是一个完整的密钥创建流程组件，提供两阶段的用户体验：

1. **创建确认阶段** - 显示安全提示并确认创建
2. **密钥显示阶段** - 展示生成的密钥并提供操作选项

## Props 接口

```typescript
interface CreateSecretModalProps {
    isOpen: boolean        // 控制模态框显示/隐藏
    onClose: () => void    // 关闭模态框的回调
    onSuccess?: () => void // 创建成功后的回调（用于刷新列表等）
}
```

## 功能特性

### 第一阶段：创建确认
- ✅ 安全提示和注意事项
- ✅ 警告图标和视觉提示
- ✅ 创建按钮加载状态
- ✅ 取消和确认操作

### 第二阶段：密钥显示
- ✅ 密钥信息展示（ID、密钥、创建时间）
- ✅ 密钥内容显示/隐藏切换
- ✅ 一键复制到剪贴板
- ✅ 下载密钥文件
- ✅ 安全提示警告
- ✅ 复制状态反馈
- ✅ 响应式布局

## 使用示例

```tsx
import { CreateSecretModal } from '@/components/modals/secret/create-secret-modal'

function SecretPage() {
    const [showModal, setShowModal] = useState(false)
    
    const handleSuccess = () => {
        // 刷新密钥列表
        refreshSecretList()
    }
    
    return (
        <>
            <button onClick={() => setShowModal(true)}>
                新建密钥
            </button>
            
            <CreateSecretModal 
                isOpen={showModal}
                onClose={() => setShowModal(false)}
                onSuccess={handleSuccess}
            />
        </>
    )
}
```

## 技术实现

### 状态管理
- `step`: 控制当前显示的阶段（'create' | 'display'）
- `createdSecret`: 存储创建成功的密钥数据
- `showSecret`: 控制密钥内容的显示/隐藏
- `copied`: 复制状态反馈

### 核心功能
- **密钥创建**: 调用 `secretApi.createSecret` API
- **复制功能**: 使用 `clipboard-polyfill` 实现跨浏览器兼容
- **文件下载**: 生成包含密钥信息的文本文件
- **状态重置**: 关闭时重置所有内部状态

### 样式设计
- 使用 Tailwind CSS 实现响应式设计
- 图标来自 Lucide React
- 支持深色/浅色主题
- 移动端友好的布局

## 安全考虑

1. **一次性显示**: 密钥只在创建后显示一次
2. **默认隐藏**: 密钥内容默认使用 `••••` 遮罩
3. **明确提示**: 多处提醒用户保存密钥的重要性
4. **状态清理**: 组件卸载时清理敏感数据

## 依赖项

- `@/api/secret/secret-api` - 密钥相关API
- `clipboard-polyfill` - 剪贴板操作
- `lucide-react` - 图标组件
- `sonner` - 消息提示
- `jotai` - 状态管理
- `ahooks` - React Hooks工具库 