import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON>alog<PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import CommonButton from "@/components/ui/button/new-button"
import { useRequest } from "ahooks"
import { secretApi } from "@/api/secret/secret-api"
import { toast } from "sonner"
import { useAtomValue } from "jotai"
import { organizationState } from "@/state/user-state"
import { Copy, Eye, EyeOff, AlertTriangle, Download } from "lucide-react"
import { cn } from "@/lib/utils"
import * as clipboard from "clipboard-polyfill"
import type { OrganizationSecret } from "@/api/secret/secret-model"
import dayjs from "dayjs"

interface CreateSecretModalProps {
    onClose: () => void
    onSuccess?: () => void
}

export const CreateSecretModal = ({ onClose, onSuccess }: CreateSecretModalProps) => {
    const [copied, setCopied] = useState(false)
    const [isOpen, setIsOpen] = useState(true)
    const [showSecret, setShowSecret] = useState(false)
    const [step, setStep] = useState<'create' | 'display'>('create')
    const [createdSecret, setCreatedSecret] = useState<OrganizationSecret | null>(null)

    const organization = useAtomValue(organizationState)

    const { runAsync: createSecret, loading: createSecretLoading } = useRequest(secretApi.createSecret, {
        manual: true,
        onSuccess: (data) => {
            setCreatedSecret(data)
            setStep('display')
            toast.success("Secret created successfully")
        },
        onError: (error) => {
            toast.error("Failed to create secret: " + error.message)
        }
    })

    const handleCopySecret = async () => {
        if (createdSecret?.secretKey) {
            try {
                await clipboard.writeText(createdSecret.secretKey)
                setCopied(true)
                toast.success("Secret copied to clipboard")
                setTimeout(() => setCopied(false), 2000)
            } catch (error) {
                toast.error("Copy failed")
            }
        }
    }

    const handleDownloadSecret = () => {
        if (createdSecret?.secretKey) {
            const element = document.createElement('a')
            const file = new Blob([`Secret: ${createdSecret.secretKey}\nCreated at: ${new Date(Number(createdSecret.createTime)).toLocaleString()}\nOrganization ID: ${createdSecret.organizationId}`], { type: 'text/plain' })
            element.href = URL.createObjectURL(file)
            element.download = `secret-${createdSecret.id}.txt`
            document.body.appendChild(element)
            element.click()
            document.body.removeChild(element)
            toast.success("Secret file downloaded")
        }
    }

    const handleClose = () => {
        setIsOpen(false)
        onClose()
    }

    const handleComplete = () => {
        handleClose()
        onSuccess?.()
    }

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="w-full max-w-md sm:max-w-md" aria-describedby={undefined}>
                {step === 'create' ? (
                    <>
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                                Create New Secret
                            </DialogTitle>
                            <DialogDescription className="text-left space-y-2">
                                <p>A new API secret will be created for you. Please note:</p>
                                <ul className="list-disc list-inside space-y-1 text-sm text-gray-600">
                                    <li>Please copy the secret after creation</li>
                                    <li>Keep it secure and do not share with others</li>
                                    <li>If lost, you'll need to create a new secret</li>
                                </ul>
                            </DialogDescription>
                        </DialogHeader>
                        <DialogFooter>
                            <CommonButton variant="outline" onClick={handleClose}>
                                Cancel
                            </CommonButton>
                            <CommonButton
                                loading={createSecretLoading}
                                onClick={async () => {
                                    await createSecret(organization?.organizationId!!)
                                }}
                            >
                                {createSecretLoading ? "Creating..." : "Confirm Create"}
                            </CommonButton>
                        </DialogFooter>
                    </>
                ) : (
                    <>
                        <DialogHeader>
                            <DialogTitle className="flex items-center gap-2">
                                Secret Created Successfully
                            </DialogTitle>
                            <DialogDescription className="text-left">
                                Your API secret has been created. Please copy and store it securely immediately. You won't be able to view it again after closing this window.
                            </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-4">
                            {/* Secret information */}
                            {/* <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-700">Secret ID</label>
                                <div className="p-2 bg-gray-50 rounded-md text-sm font-mono text-gray-600">
                                    {createdSecret?.id}
                                </div>
                            </div> */}

                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-700">API Secret</label>
                                <div className="relative">
                                    <div className="p-3 bg-gray-50 border-2 border-dashed border-gray-200 rounded-md">
                                        <div className="flex items-start gap-2">
                                            <div className="flex-1 font-mono text-sm break-all overflow-hidden">
                                                {showSecret
                                                    ? createdSecret?.secretKey
                                                    : '••••••••••••••••••••••••••••••••'
                                                }
                                            </div>
                                            <div className="flex gap-1 flex-shrink-0">
                                                <button
                                                    onClick={() => setShowSecret(!showSecret)}
                                                    className="p-1 hover:bg-gray-200 rounded transition-colors"
                                                    title={showSecret ? "Hide secret" : "Show secret"}
                                                >
                                                    {showSecret ? (
                                                        <EyeOff className="w-4 h-4 text-gray-500" />
                                                    ) : (
                                                        <Eye className="w-4 h-4 text-gray-500" />
                                                    )}
                                                </button>
                                                <button
                                                    onClick={handleCopySecret}
                                                    className={cn(
                                                        "p-1 rounded transition-colors",
                                                        copied
                                                            ? "bg-green-100 text-green-600"
                                                            : "hover:bg-gray-200 text-gray-500"
                                                    )}
                                                    title="Copy secret"
                                                >
                                                    <Copy className="w-4 h-4" />
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium text-gray-700">Created at</label>
                                <div className="p-2 bg-gray-50 rounded-md text-sm text-gray-600">
                                    {createdSecret?.createTime ? dayjs(Number(createdSecret.createTime)).format('YYYY-MM-DD HH:mm:ss') : ''}
                                </div>
                            </div>
                        </div>

                        {/* Security notice */}
                        <div className="bg-amber-50 border border-amber-200 rounded-md p-3">
                            <div className="flex gap-2">
                                <AlertTriangle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
                                <div className="text-sm text-amber-800">
                                    <p className="font-medium">Security Notice</p>
                                    <p className="mt-1">Please copy and save this secret immediately. We recommend storing the secret in a secure password manager.</p>
                                </div>
                            </div>
                        </div>

                        <DialogFooter className="flex-col sm:flex-row gap-2">
                            <CommonButton
                                variant="outline"
                                onClick={handleDownloadSecret}
                                className="w-full sm:w-auto"
                            >
                                <Download className="w-4 h-4" />
                                Download Secret File
                            </CommonButton>
                            <CommonButton
                                onClick={handleComplete}
                                className="w-full sm:w-auto"
                            >
                                I've Saved It, Complete
                            </CommonButton>
                        </DialogFooter>
                    </>
                )}
            </DialogContent>
        </Dialog>
    )
}