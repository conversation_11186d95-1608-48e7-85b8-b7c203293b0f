import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { useRequest } from 'ahooks';
import { useAtomValue } from 'jotai';
import { InfoIcon, Loader2, Mail, Shield } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { organizationState } from "@/state/user-state";
import { OrganizationBindApi } from "@/api/organization-bind/organization-bind-model";
import CommonButton from "@/components/ui/button/new-button";

interface AddTeamMemberModalProps {
    onSuccess: () => void;
}

const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
};

export function AddTeamMemberModal({ onSuccess }: AddTeamMemberModalProps) {
    const [userId, setUserId] = useState('');
    const [isOpen, setIsOpen] = useState(true);
    const [authority, setAuthority] = useState('1');
    const [emailError, setEmailError] = useState<string | null>(null);
    const organization = useAtomValue(organizationState);

    const { runAsync: addMember, loading } = useRequest(OrganizationBindApi.persistOrganizationBind, {
        manual: true,
        onSuccess: () => {
            toast.success('Member added successfully');
            onSuccess();
            setIsOpen(false);
        },
        onError: (e) => {
            toast.error('Failed to add member: ' + e.message);
        },
    });

    const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const email = e.target.value;
        setUserId(email);
        if (email && !validateEmail(email)) {
            setEmailError('Please enter a valid email address');
        } else {
            setEmailError(null);
        }
    };

    const handleSubmit = () => {
        if (!userId) {
            setEmailError('Please enter an email address');
            return;
        }

        if (!validateEmail(userId)) {
            setEmailError('Please enter a valid email address');
            return;
        }

        if (!authority || !organization?.organizationId) {
            toast.error('Please fill in all required fields');
            return;
        }

        addMember(organization.organizationId, parseInt(authority), userId);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader className="pb-4">
                    <DialogTitle className="text-xl font-semibold">
                        <div className="flex items-center gap-3">
                            <div>
                                <h3 className="font-semibold text-gray-900 dark:text-gray-100">Add Team Member</h3>
                                <p className="text-sm font-normal text-gray-600 dark:text-gray-400 mt-1">
                                    Invite new members to join your team
                                </p>
                            </div>
                        </div>
                    </DialogTitle>
                </DialogHeader>

                <div className="space-y-6">
                    {/* Email input section */}
                    <div className="space-y-3">
                        <div className="flex items-center gap-2">
                            <Mail className="h-4 w-4 text-gray-500" />
                            <label htmlFor="userId" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                Email Address
                            </label>
                        </div>
                        <div className="relative">
                            <Input
                                id="userId"
                                type="email"
                                value={userId}
                                onChange={handleEmailChange}
                                className={cn(
                                    "w-full pl-4 pr-4 py-3 text-sm border-gray-200 dark:border-gray-700",
                                    "focus:ring-2 focus:ring-blue-500 focus:border-blue-500",
                                    "placeholder:text-gray-400 dark:placeholder:text-gray-500",
                                    emailError && "border-red-300 focus:ring-red-500 focus:border-red-500"
                                )}
                                placeholder="Enter member email address, e.g.: <EMAIL>"
                            />
                        </div>
                        {emailError && (
                            <div className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400">
                                <InfoIcon className="h-4 w-4" />
                                <span>{emailError}</span>
                            </div>
                        )}
                    </div>

                    {/* Permission selection section */}
                    <div className="space-y-3">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                                <Shield className="h-4 w-4 text-gray-500" />
                                <label htmlFor="authority" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                    Permission Level
                                </label>
                            </div>
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger asChild>
                                        <InfoIcon className='h-4 w-4 text-gray-400 hover:text-gray-600 cursor-help' />
                                    </TooltipTrigger>
                                    <TooltipContent side="left" className="max-w-xs">
                                        <div className="space-y-2 text-xs">
                                            <div><strong>Owner</strong>: Has full management permissions, can modify team members and permission settings</div>
                                            <div><strong>Member</strong>: Has basic usage permissions, cannot modify team settings</div>
                                        </div>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        </div>

                        <Select value={authority} onValueChange={setAuthority}>
                            <SelectTrigger className="w-full h-11 border-gray-200 dark:border-gray-700">
                                <SelectValue placeholder="Select permission level" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="1" className="py-3">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 rounded-full bg-orange-500"></div>
                                        <div>
                                            <div className="font-medium">Owner</div>
                                            {/* <div className="text-xs text-gray-500">Full management permissions</div> */}
                                        </div>
                                    </div>
                                </SelectItem>
                                <SelectItem value="0" className="py-3">
                                    <div className="flex items-center gap-3">
                                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                        <div>
                                            <div className="font-medium">Member</div>
                                            {/* <div className="text-xs text-gray-500">Basic usage permissions</div> */}
                                        </div>
                                    </div>
                                </SelectItem>
                            </SelectContent>
                        </Select>
                    </div>

                    {/* Information notice */}
                    <div className="rounded-lg bg-blue-50 dark:bg-blue-900/10 border border-blue-200 dark:border-blue-800 p-4">
                        <div className="flex gap-3">
                            <InfoIcon className="h-5 w-5 text-blue-600 dark:text-blue-400 flex-shrink-0 mt-0.5" />
                            <div className="text-sm text-blue-800 dark:text-blue-200">
                                <p className="font-medium mb-1">Invitation Instructions</p>
                                <p className="text-blue-700 dark:text-blue-300">
                                    The system will send an invitation email to this address. The member needs to click the link in the email to complete joining.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <DialogFooter className="pt-6 gap-3 sm:gap-2">
                    <CommonButton
                        size="sm"
                        variant="outline"
                        onClick={() => setIsOpen(false)}
                        className='min-w-20'
                        disabled={loading}
                    >
                        Cancel
                    </CommonButton>
                    <CommonButton
                        size="sm"
                        onClick={handleSubmit}
                        disabled={loading || !!emailError || !userId.trim()}
                        className='min-w-24'
                    >
                        {loading ? (
                            <div className="flex items-center gap-2">
                                <Loader2 className="h-4 w-4 animate-spin" />
                                <span>Sending Invitation...</span>
                            </div>
                        ) : (
                            <div className="flex items-center gap-2">
                                <Mail className="h-4 w-4" />
                                <span>Send Invitation</span>
                            </div>
                        )}
                    </CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}