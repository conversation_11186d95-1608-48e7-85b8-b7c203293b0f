import { createPushModal } from 'pushmodal'
import { CreateOrganizationModal } from './organization/create-organization-modal'
import { OrganizationSettingsModal } from './organization/organization-settings-modal'
import { CreateSecretModal } from './secret/create-secret-modal'
import { DoubleCheckModal } from './double-check-modal'
import { AddTeamMemberModal } from './team/add-team-member-modal'
import { AutoRechargeModal } from './wallet/auto-recharge-modal'
import { RechargeModal } from './wallet/recharge-modal'
import { RefundModal } from './wallet/refund-modal'



/**
 * https://github.com/lindesvard/pushmodal
 */

export const {
    pushModal,
    popModal,
    popAllModals,
    replaceWithModal,
    useOnPushModal,
    onPushModal,
    ModalProvider
} = createPushModal({
    modals: {
        CreateOrganizationModal,
        OrganizationSettingsModal,
        CreateSecretModal,
        DoubleCheckModal,
        AddTeamMemberModal,
        AutoRechargeModal,
        RechargeModal,
        RefundModal
    },
})
