import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, DialogDescription, <PERSON><PERSON><PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import CommonButton from "@/components/ui/button/new-button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { organizationApi } from "@/api/organization/organization-api"
import { toast } from "sonner"
import { useRequest } from "ahooks"
import { organizationState } from "@/state/user-state"
import { useAtomValue, useSetAtom } from "jotai"
import { Settings } from "lucide-react"

interface OrganizationSettingsModalProps {
    onClose: () => void
    onSuccess?: () => void
}

export function OrganizationSettingsModal({ onClose, onSuccess }: OrganizationSettingsModalProps) {
    const [open, setOpen] = useState(true)
    const organization = useAtomValue(organizationState)
    const setOrganization = useSetAtom(organizationState)
    const [organizationName, setOrganizationName] = useState<string>(organization?.organizationName || "")
    const inputRef = useRef<HTMLInputElement>(null)

    const { runAsync: updateOrganization, loading: updateOrganizationLoading } = useRequest(organizationApi.updateOrganizationInfo, {
        manual: true,
        onSuccess: () => {
            toast.success("Organization settings updated successfully")
            // Update local state
            if (organization) {
                setOrganization({
                    ...organization,
                    organizationName: organizationName
                })
            }
            onSuccess?.()
            handleClose()
        },
        onError: (error) => {
            toast.error("Failed to update organization: " + error.message)
        }
    })

    useEffect(() => {
        // Delay to ensure Dialog is fully rendered before focusing
        const timer = setTimeout(() => {
            if (inputRef.current) {
                inputRef.current.focus()
            }
        }, 100)

        return () => clearTimeout(timer)
    }, [open])

    const handleClose = () => {
        setOpen(false)
        onClose()
    }

    const handleSave = async () => {
        if (!organization || !organizationName.trim()) {
            toast.error("Please enter organization name")
            return
        }

        if (organizationName.trim() === organization.organizationName) {
            toast.info("No changes made")
            handleClose()
            return
        }

        await updateOrganization(organization.organizationId, organizationName.trim())
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogContent className="sm:max-w-md" aria-describedby={undefined}>
                <DialogHeader className="space-y-4 pb-6 border-b border-zinc-100">
                    <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-zinc-100 rounded-lg flex items-center justify-center">
                            <Settings className="w-5 h-5 text-zinc-700" />
                        </div>
                        <div className="text-left">
                            <DialogTitle className="text-lg font-medium text-zinc-900">Organization Settings</DialogTitle>
                            <DialogDescription className="text-sm text-zinc-500 mt-1">
                                Manage your organization settings and preferences
                            </DialogDescription>
                        </div>
                    </div>
                </DialogHeader>

                <div className="space-y-6 py-6">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="organizationName" className="text-sm font-medium text-zinc-900">
                                Organization Name
                            </Label>
                            <Input
                                ref={inputRef}
                                id="organizationName"
                                value={organizationName}
                                onChange={(e) => setOrganizationName(e.target.value)}
                                placeholder="Enter organization name"
                                className="h-11 border-zinc-200 focus:border-zinc-400 focus:ring-0"
                            />
                        </div>

                        {/* Organization ID (read-only) */}
                        <div className="space-y-2">
                            <Label htmlFor="organizationId" className="text-sm font-medium text-zinc-900">
                                Organization ID
                            </Label>
                            <Input
                                id="organizationId"
                                value={organization?.organizationId || ""}
                                disabled
                                className="h-11 border-zinc-200 bg-zinc-50 text-zinc-500"
                            />
                            <p className="text-xs text-zinc-400">
                                This is your unique organization identifier and cannot be changed
                            </p>
                        </div>
                    </div>
                </div>

                <DialogFooter className="flex gap-3 pt-6 border-t border-zinc-100">
                    <CommonButton
                        variant="outline"
                        onClick={handleClose}
                        className="flex-1 border-zinc-200 text-zinc-700 hover:bg-zinc-50"
                        disabled={updateOrganizationLoading}
                    >
                        Cancel
                    </CommonButton>
                    <CommonButton
                        onClick={handleSave}
                        disabled={updateOrganizationLoading || !organizationName.trim()}
                        className="flex-1 bg-zinc-900 hover:bg-zinc-800 text-white border-0"
                    >
                        {updateOrganizationLoading ? "Saving..." : "Save Changes"}
                    </CommonButton>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
} 