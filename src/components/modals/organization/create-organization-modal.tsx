"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@/components/ui/new/dialog"
import StatusIcon from "@/components/icons/status-icon"
import CommonButton from "@/components/ui/button/new-button"
import { Input } from "@/components/ui/input"
import { organizationApi } from "@/api/organization/organization-api"
import { toast } from "sonner"
import { useRequest } from "ahooks"
import { organizationState } from "@/state/user-state"
import { useSetAtom } from "jotai"

export function CreateOrganizationModal() {
    const [open, setOpen] = useState(true)
    const setOrganization = useSetAtom(organizationState)
    const [organizationName, setOrganizationName] = useState<string>("")
    const [activeTab, setActiveTab] = useState<"type" | "details" | "campaign">("type")
    const inputRef = useRef<HTMLInputElement>(null)

    const { runAsync: createOrganization, loading: createOrganizationLoading } = useRequest(organizationApi.createOrganization, {
        manual: true,
        onSuccess: () => {
            toast.success("Organization created successfully");
            setOpen(false)
            organizationApi.currentOrganization().then((res) => {
                if (res) {
                    setOrganization(res)
                }
            })
        },
        onError: (error) => {
            toast.error("Failed to create organization: " + error.message)
        }
    })

    useEffect(() => {
        // Delay to ensure Dialog is fully rendered before focusing
        const timer = setTimeout(() => {
            if (inputRef.current) {
                inputRef.current.focus()
            }
        }, 100)

        return () => clearTimeout(timer)
    }, [open])

    return (
        <Dialog open={open} onOpenChange={() => { }}>
            <div tabIndex={0} className="sr-only" />
            <DialogContent className="sm:max-w-[45%] h-[40%] flex flex-col" hideCloseButton aria-describedby={undefined} onEscapeKeyDown={(e) => e.preventDefault()} onPointerDownOutside={(e) => e.preventDefault()} autoFocus={false}>
                <DialogHeader className="border-b flex-row items-center justify-start p-0 h-15 flex-shrink-0">
                    <DialogTitle className="sr-only">Create Organization</DialogTitle>
                    {/* <Button
                        variant="ghost"
                        className="rounded-none border-r flex items-center gap-2 h-full bg-white focus-visible:ring-0 focus-visible:border-gray-200" onClick={() => setOpen(false)}
                    >
                        <X className="h-4 w-4" />
                        <span className="text-xs text-muted-foreground">
                            <Badge variant="outline">
                                esc
                            </Badge>
                        </span>
                    </Button> */}

                    {/* sub  TabButton  same hover style*/}
                    <div className="flex flex-1 h-15 bg-white">
                        <TabButton
                            active={activeTab === "type"}
                            onClick={() => setActiveTab("type")}
                            icon={
                                <StatusIcon
                                    variant={activeTab === "type" ? "progress-blue" : "completed"}
                                    size="sm"
                                />
                            }
                            label="Create Organization"
                        />
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-auto flex flex-col items-center justify-center">
                    {activeTab === "type" && (
                        <div className="space-y-4">
                            <h2 className="text-normal font-medium">Organization Name</h2>
                            <Input
                                ref={inputRef}
                                placeholder="Enter organization name"
                                value={organizationName}
                                onChange={(e) => setOrganizationName(e.target.value)}
                            />
                        </div>
                    )}
                </div>

                <div className="border-t flex justify-end items-center p-2 gap-2 flex-shrink-0">
                    {/* <CommonButton variant="outline" onClick={() => setOpen(false)}>
                        Cancel
                    </CommonButton> */}
                    <CommonButton
                        onClick={() => {
                            if (organizationName) {
                                createOrganization(organizationName)
                            } else {
                                toast.error("Please enter organization name")
                            }
                        }}
                    >
                        {createOrganizationLoading ? "Creating..." : "Create"}
                    </CommonButton>
                </div>
            </DialogContent>
        </Dialog>
    )
}

export function TabButton({
    active,
    onClick,
    icon,
    label,
}: {
    active: boolean
    onClick: () => void
    icon: React.ReactNode
    label: string
}) {
    return (
        <button
            className={`flex w-56 font-light items-center gap-2 px-4 py-2 relative border-r border-zinc-200  ${active ? "after:absolute after:bottom-0 after:left-0 after:right-0 after:h-[2px]  " : "bg-zinc-100"
                }`}
            onClick={onClick}
        >
            {icon}
            <span className={` text-sm ${active ? "text-foreground" : "text-muted-foreground"}`}>{label}</span>
        </button>
    )
}
