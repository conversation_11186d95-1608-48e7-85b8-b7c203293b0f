import { useState, useEffect } from "react";
import { useAtomValue } from "jotai";
import { useRequest } from "ahooks";

import { Button } from "@/components/ui/button";
import { organizationState } from "@/state/user-state";
import { refreshTableAtom } from "@/state/table-state";
import { WalletApi } from "@/api/wallet/wallet-api";
import type { BalanceTransactionResponse } from "@/api/wallet/wallet-model";

interface BillingTableProps {
    className?: string;
}

export const BillingTable = ({ className }: BillingTableProps) => {
    const organization = useAtomValue(organizationState);
    const refreshTable = useAtomValue(refreshTableAtom);
    const [transactions, setTransactions] = useState<BalanceTransactionResponse[]>([]);

    // 获取账单交易记录
    const { run: fetchTransactions, loading } = useRequest(
        WalletApi.getBalanceTransactions,
        {
            manual: true,
            onSuccess: (data) => {
                setTransactions(data || []);
            },
            onError: (error) => {
                console.error("Failed to fetch transactions:", error);
                setTransactions([]);
            }
        }
    );

    // 当组织ID或刷新标志变化时重新获取数据
    useEffect(() => {
        if (organization?.organizationId) {
            fetchTransactions(organization.organizationId);
        }
    }, [organization?.organizationId, refreshTable, fetchTransactions]);

    // 格式化金额
    const formatAmount = (amount: number) => {
        return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    };

    // 格式化日期
    const formatDate = (timestamp: string) => {
        const date = new Date(parseInt(timestamp));
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    // 获取交易类型显示文本和样式
    const getTypeInfo = (type: string) => {
        switch (type) {
            case 'RECHARGE':
                return { text: 'Paid', variant: 'default' as const, bgColor: 'bg-green-100 text-green-800' };
            case 'GIFT':
                return { text: 'Gift', variant: 'secondary' as const, bgColor: 'bg-blue-100 text-blue-800' };
            case 'ADJUSTMENT':
                return { text: 'Adjustment', variant: 'outline' as const, bgColor: 'bg-gray-100 text-gray-800' };
            default:
                return { text: type, variant: 'outline' as const, bgColor: 'bg-gray-100 text-gray-800' };
        }
    };


    if (loading) {
        return (
            <div className={`space-y-4 ${className}`}>
                <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Billing History</h3>
                    <p className="text-sm text-zinc-500">Loading...</p>
                </div>
                <div className="animate-pulse space-y-3">
                    {[...Array(3)].map((_, i) => (
                        <div key={i} className="h-16 bg-zinc-100 rounded-lg"></div>
                    ))}
                </div>
            </div>
        );
    }

    return (
        <div className={`space-y-4 ${className}`}>
            <div className="flex items-center justify-between">
                <div>
                    <h3 className="text-lg font-semibold">Billing History</h3>
                    <p className="text-sm text-zinc-500">
                        Showing invoices within the past 12 months
                    </p>
                </div>
            </div>

            {transactions.length === 0 ? (
                <div className="text-center py-8 text-zinc-500">
                    <p>No billing records found</p>
                </div>
            ) : (
                <div className="space-y-3">
                    {/* 表头 */}
                    <div className="grid grid-cols-5 gap-4 px-4 py-2 text-sm font-medium text-zinc-600 border-b border-zinc-200">
                        <div>STATUS</div>
                        <div>AMOUNT</div>
                        <div>CREATED</div>
                        <div></div>
                    </div>

                    {/* 数据行 */}
                    {transactions.map((transaction, index) => {
                        const typeInfo = getTypeInfo(transaction.type);

                        return (
                            <div key={`${transaction.created}-${index}`} className="grid grid-cols-5 gap-4 px-4 py-3 hover:bg-zinc-50 rounded-lg border border-transparent hover:border-zinc-200 transition-colors">

                                <div>
                                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${typeInfo.bgColor}`}>
                                        {typeInfo.text}
                                    </span>
                                </div>
                                <div className="font-medium text-zinc-900">
                                    {formatAmount(transaction.amount)}
                                </div>
                                <div className="text-zinc-600">
                                    {formatDate(transaction.created)}
                                </div>
                                <div className="flex items-center gap-2">
                                    {transaction.invoiceUrl && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 px-2 text-zinc-600 hover:text-zinc-900"
                                            onClick={() => window.open(transaction.invoiceUrl, '_blank')}
                                        >
                                            View
                                        </Button>
                                    )}
                                </div>
                            </div>
                        );
                    })}
                </div>
            )}
        </div>
    );
};
