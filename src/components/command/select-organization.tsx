import { CommandDialog, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "../ui/command"
import React from "react"
import { organizationApi } from "@/api/organization/organization-api"
import { organizationState } from "@/state/user-state"
import { useSetAtom } from "jotai"
import { useRequest } from 'ahooks';
import { toast } from "sonner"
import { ScrollArea } from "../ui/scroll-area"
import type { Organization } from "@/api/organization/organization-model"

interface CommandMenuProps {
    open?: boolean;
    onOpenChange?: (open: boolean) => void;
}

export function SelectUserCommandMenu({ open: externalOpen, onOpenChange: externalOnOpenChange }: CommandMenuProps = {}) {
    const setCurrentOrg = useSetAtom(organizationState);
    const [internalOpen, setInternalOpen] = React.useState(false)

    const open = externalOpen !== undefined ? externalOpen : internalOpen;
    const setOpen = externalOnOpenChange !== undefined ? externalOnOpenChange : setInternalOpen;

    // 获取组织列表
    const { data: organizations = [], run: fetchOrganizations, loading } = useRequest(organizationApi.getOrganization, {
        manual: true,
    });

    // 当对话框打开时获取最新的组织数据
    React.useEffect(() => {
        if (open) {
            fetchOrganizations();
        }
    }, [open, fetchOrganizations]);

    // 切换组织的处理函数
    const handleSelectOrganization = async (organization: Organization) => {
        try {
            const result = await organizationApi.changeOrganization(organization.organizationId);
            setCurrentOrg(result);
            setOpen(false);
            toast.success("Switch organization successfully");
        } catch (error: any) {
            toast.error("Switch organization failed: " + error.message);
        }
    };

    // 格式化数字显示
    // const formatNumber = (num: number | bigint) => {
    //     return new Intl.NumberFormat('zh-CN').format(Number(num));
    // };

    return (
        <CommandDialog open={open} onOpenChange={setOpen}>
            <CommandInput placeholder="Search organization..." />
            <div className="max-h-[400px] overflow-hidden">
                <ScrollArea className="h-[320px]">
                    <CommandList>
                        <CommandEmpty>No results found</CommandEmpty>
                        <CommandGroup heading="Organization List">
                            {loading ? (
                                <CommandItem disabled>Loading...</CommandItem>
                            ) : organizations.length > 0 ? (
                                organizations.map((org) => (
                                    <CommandItem
                                        key={String(org.organizationId)}
                                        onSelect={() => handleSelectOrganization(org)}
                                        className="py-3"
                                    >
                                        <div className="flex h-8 w-8 items-center justify-center rounded-sm">
                                            <div className="p-[1px] rounded-md bg-white border border-zinc-200 flex flex-row items-center justify-center">
                                                <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders bg-zinc-100 h-8 w-8 rounded-md">
                                                    <span className="aspect-square object-cover object-center rounded bg-ui-bg-component-hover text-black/80 pointer-events-none flex select-none items-center justify-center text-sm font-semibold">
                                                        {org.organizationName.slice(0, 1).toUpperCase()}
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                        <div className="text-sm font-medium">
                                            {org.organizationName}
                                        </div>
                                    </CommandItem>
                                ))
                            ) : (
                                <CommandItem disabled>No organization found</CommandItem>
                            )}
                        </CommandGroup>
                    </CommandList>
                </ScrollArea>
            </div>
        </CommandDialog>
    )
}
