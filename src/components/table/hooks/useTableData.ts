import { refreshTable<PERSON>tom } from '@/state/table-state';
import { useRequest } from 'ahooks';
import { useAtomValue } from 'jotai';
import React from 'react';
import { useLocation } from 'react-router';
import { type FetchParams, type FetchResponse } from '../types';

export function useTableData<TData>(
    onFetch: (params: FetchParams) => Promise<FetchResponse<TData>>,
    sorting: any[],
    columnFilters: any[],
    pageIndex: number,
    pageSize: number,
    advancedFilters: Record<string, any>,
    dependencies: any[] = []
) {
    const [data, setData] = React.useState<TData[]>([]);
    const [rowCount, setRowCount] = React.useState(0);
    const { search } = useLocation();
    const refreshTable = useAtomValue(refreshTableAtom);

    const fetchDataOptions = React.useMemo(
        () => {
            const searchParams = new URLSearchParams(search);
            const searchParamsObject: Record<string, any> = {};
            for (const [key, value] of searchParams.entries()) {
                searchParamsObject[key] = value;
            }

            const sortingParams: Partial<FetchParams> = sorting.length > 0 ? {
                sort: sorting.map(s => s.id),
                direction: sorting.map(s => s.desc ? 'DESC' : 'ASC')
            } : {};

            const result: FetchParams = {
                filters: columnFilters.reduce((acc, filter) => {
                    if (Array.isArray(filter.value)) {
                        acc[filter.id] = filter.value.join(',');
                    } else {
                        acc[filter.id] = filter.value;
                    }
                    return acc;
                }, {} as Record<string, any>),
                ...sortingParams,
                pagination: {
                    pageIndex,
                    pageSize,
                },
                advancedFilters,
                searchParams: searchParamsObject,
            };

            // if (sorting.length > 0) {
            //     const params = new URLSearchParams();
            //     sorting.forEach((sort, index) => {
            //         params.append(`sort[${index}]`, sort.id);
            //         params.append(`direction[${index}]`, sort.desc ? 'DESC' : 'ASC');
            //     });
            //     for (const [key, value] of params.entries()) {
            //         result.searchParams[key] = value;
            //     }
            // }

            return result;
        },
        [sorting, columnFilters, pageIndex, pageSize, advancedFilters, refreshTable, search, ...dependencies]
    );

    const { loading, run: fetchData } = useRequest(
        async (options: typeof fetchDataOptions) => {
            const response = await onFetch(options);
            setData(response.content);
            setRowCount(response.total);
            return response;
        },
        {
            debounceWait: 300,
            manual: true,
        }
    );

    React.useEffect(() => {
        fetchData(fetchDataOptions);
    }, [fetchData, fetchDataOptions]);

    return { data, rowCount, fetchData, loading };
} 