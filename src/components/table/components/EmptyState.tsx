import { TableCell, TableRow } from '@/components/ui/table';
import { Ghost } from 'lucide-react';


interface EmptyStateProps {
    colSpan: number;
    message?: string;
}

export function EmptyState({ colSpan, message = "No data available" }: EmptyStateProps) {
    return (
        <TableRow>
            <TableCell colSpan={colSpan} className="h-48 text-center">
                <div className="flex flex-col items-center justify-center gap-2">
                    <Ghost className="h-8 w-8 text-muted-foreground/60" />
                    <span className="text-[13px] text-muted-foreground/80">{message}</span>
                </div>
            </TableCell>
        </TableRow>
    );
} 