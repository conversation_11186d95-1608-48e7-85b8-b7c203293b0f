import { TableCell, TableRow, TableBody as UITableBody } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import { flexRender } from '@tanstack/react-table';
import { EmptyState } from './EmptyState';
import { Loading } from '@/components/ui/loading';

interface TableBodyProps {
    table: any;
    loading?: boolean;
}

export function TableBody({ table, loading }: TableBodyProps) {
    if (loading) {
        return (
            <UITableBody>
                <TableRow>
                    <TableCell colSpan={table.getAllColumns().length}>
                        <Loading className="min-h-[300px]" />
                    </TableCell>
                </TableRow>
            </UITableBody>
        );
    }

    return (
        <UITableBody>
            {table.getRowModel().rows?.length ? (
                table.getRowModel().rows.map((row: any) => (
                    <TableRow
                        key={row.id}
                        data-state={row.getIsSelected() && "selected"}
                        className="group"
                    >
                        {row.getVisibleCells().map((cell: any, index: number) => {
                            const isFixedLeft = cell.column.columnDef.meta?.fixed === 'left';
                            const isFixedRight = cell.column.columnDef.meta?.fixed === 'right';

                            return (
                                <TableCell
                                    className={cn(
                                        "px-4 group-hover:bg-muted text-zinc-500",
                                        isFixedLeft && "sticky left-0 z-20 bg-background",
                                        isFixedRight && "sticky right-0 z-20 bg-background",
                                        (isFixedLeft || isFixedRight) && "drop-shadow-md"
                                    )}
                                    style={{
                                        ...(isFixedLeft && {
                                            left: `${index * 80}px`,
                                        })
                                    }}
                                    key={cell.id}
                                >
                                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                                </TableCell>
                            );
                        })}
                    </TableRow>
                ))
            ) : (
                <EmptyState colSpan={table.getAllColumns().length} />
            )}
        </UITableBody>
    );
} 