"use client"

import type React from "react"

import { useState, useRef, useEffect, type KeyboardEvent, type ClipboardEvent, forwardRef, useImperativeHandle } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Search } from "lucide-react"
import { cn } from "@/lib/utils"
import "./edit-table.css"


// New generic Item type
export type Item = Record<string, any> & { id?: string; _id: string };

// New Column Definition type
export interface ColumnDef {
    id: string;
    header: string;
    editable?: boolean;
    type: "text" | "number" | "boolean";
}

// Define the type for the exposed handle
export interface ListEditorHandle {
    getLatestData: () => Item[];
}

type CellPosition = {
    rowIndex: number
    colIndex: number
}

type CellSelection = {
    start: CellPosition
    end: CellPosition
}

// 筛选条件类型
type FilterCondition = {
    [key: string]: Set<any> // 存储选中的值
}


interface ListEditorProps {
    initialItems: Item[];
    columns: ColumnDef[];
}

const ListEditor = forwardRef<ListEditorHandle, ListEditorProps>(({ initialItems, columns: propColumns }, ref) => {
    const [items, setItems] = useState<Item[]>(initialItems)

    // 筛选相关状态
    const [filterConditions, setFilterConditions] = useState<FilterCondition>({})
    const [filteredItems, setFilteredItems] = useState<Item[]>(items) // Initialize with items from state
    const [filterSearch, setFilterSearch] = useState<string>("")
    const [, setActiveFilterColumn] = useState<string | null>(null)
    const [filterDialogOpen, setFilterDialogOpen] = useState<{ [key: string]: boolean }>({})

    // 当前活动单元格
    const [activeCell, setActiveCell] = useState<CellPosition | null>(null)

    // 选中的单元格范围
    const [selections, setSelections] = useState<CellSelection[]>([])

    // 编辑状态
    const [editingCell, setEditingCell] = useState<CellPosition | null>(null)
    // const [editValue, setEditValue] = useState<string>("") // Removed, value taken directly from DOM

    // 批量编辑状态
    const [batchEditing, setBatchEditing] = useState(false)
    const [batchEditValue, setBatchEditValue] = useState<string>("")

    // 复制粘贴缓冲区
    const [clipboard, setClipboard] = useState<string>("")

    const [, setShowPasteHint] = useState(false)

    const editableCellRef = useRef<HTMLDivElement>(null)
    const batchEditInputRef = useRef<HTMLInputElement>(null)
    const tableContainerRef = useRef<HTMLDivElement>(null)

    // Expose getLatestData method using useImperativeHandle
    useImperativeHandle(ref, () => ({
        getLatestData: () => {
            // Return items, potentially stripping the internal _id if necessary,
            // or callers can choose to ignore it. For now, return as is.
            return items;
        }
    }));

    // Effect to reset internal state if initialItems or propColumns change
    useEffect(() => {
        // Process initialItems to add an internal _id for reliable tracking
        const processedItems = initialItems.map(item => ({
            ...item,
            _id: crypto.randomUUID(), // Always generate a new internal ID
        }));

        setItems(processedItems);
        setFilteredItems(processedItems); // Also re-initialize filtered items
        setFilterConditions({});
        setActiveCell(null);
        setSelections([]);
        setEditingCell(null);
        setBatchEditing(false);
        setBatchEditValue("");
        setClipboard("");
        if (tableContainerRef.current) {
            tableContainerRef.current.scrollTop = 0; // Reset scroll position
        }
    }, [initialItems, propColumns]);

    // 应用筛选条件，更新筛选后的数据
    useEffect(() => {
        // 如果没有筛选条件，显示所有数据
        if (Object.keys(filterConditions).length === 0 || items.length === 0) {
            setFilteredItems(items) // items is now the state variable
            return
        }

        // 应用筛选条件
        const filtered = items.filter((item) => { // Filter from the `items` state
            // 检查每个筛选条件
            for (const columnId in filterConditions) {
                const selectedValues = filterConditions[columnId]

                // 如果该列没有选中的值，则跳过该列的筛选
                if (selectedValues.size === 0) continue

                // 获取当前项的该列的值
                const value = item[columnId] // Direct access for generic Item

                // 如果当前值不在选中的值中，则过滤掉该项
                if (!selectedValues.has(String(value))) {
                    return false
                }
            }

            // 所有条件都满足，保留该项
            return true
        })

        setFilteredItems(filtered)
    }, [filterConditions, items])

    // 获取列的所有唯一值
    const getUniqueValues = (columnId: string) => {
        const values = new Set<any>()
        items.forEach((item) => {
            values.add(String(item[columnId]))
        })
        return Array.from(values).sort()
    }

    // 处理筛选条件变化
    const handleFilterChange = (columnId: string, value: string, checked: boolean) => {
        setFilterConditions((prev) => {
            const newConditions = { ...prev }

            // 如果该列没有筛选条件，创建一个新的
            if (!newConditions[columnId]) {
                newConditions[columnId] = new Set()
            }

            // 更新筛选条件
            if (checked) {
                newConditions[columnId].add(value)
            } else {
                newConditions[columnId].delete(value)
            }

            // 如果该列没有选中的值，删除该列的筛选条件
            if (newConditions[columnId].size === 0) {
                delete newConditions[columnId]
            }

            return newConditions
        })
    }

    // 清除列的筛选条件
    const clearColumnFilter = (columnId: string) => {
        setFilterConditions((prev) => {
            const newConditions = { ...prev }
            delete newConditions[columnId]
            return newConditions
        })
    }

    // 检查列是否有筛选条件
    const hasColumnFilter = (columnId: string) => {
        return filterConditions[columnId] !== undefined
    }

    // 打开筛选对话框
    const openFilterDialog = (columnId: string) => {
        setActiveFilterColumn(columnId)
        setFilterSearch("")
        setFilterDialogOpen((prev) => ({ ...prev, [columnId]: true }))
    }

    // 关闭筛选对话框
    const closeFilterDialog = (columnId: string) => {
        setFilterDialogOpen((prev) => ({ ...prev, [columnId]: false }))
        setActiveFilterColumn(null)
        setFilterSearch("")
    }

    // 获取所有选中的单元格
    const getSelectedCells = () => {
        const selectedCells: CellPosition[] = []

        selections.forEach((selection) => {
            const minRow = Math.min(selection.start.rowIndex, selection.end.rowIndex)
            const maxRow = Math.max(selection.start.rowIndex, selection.end.rowIndex)
            const minCol = Math.min(selection.start.colIndex, selection.end.colIndex)
            const maxCol = Math.max(selection.start.colIndex, selection.end.colIndex)

            for (let r = minRow; r <= maxRow; r++) {
                for (let c = minCol; c <= maxCol; c++) {
                    selectedCells.push({ rowIndex: r, colIndex: c })
                }
            }
        })

        return selectedCells
    }

    // 检查单元格是否在选择范围内
    const isCellSelected = (rowIndex: number, colIndex: number) => {
        return selections.some((selection) => {
            const minRow = Math.min(selection.start.rowIndex, selection.end.rowIndex)
            const maxRow = Math.max(selection.start.rowIndex, selection.end.rowIndex)
            const minCol = Math.min(selection.start.colIndex, selection.end.colIndex)
            const maxCol = Math.max(selection.start.colIndex, selection.end.colIndex)

            return rowIndex >= minRow && rowIndex <= maxRow && colIndex >= minCol && colIndex <= maxCol
        })
    }

    // 处理单元格点击
    const handleCellClick = (rowIndex: number, colIndex: number, event: React.MouseEvent) => {
        // 如果点击的单元格是不可编辑的（且非布尔型），则不进行任何操作
        if (propColumns[colIndex].editable === false && propColumns[colIndex].type !== "boolean") {
            return;
        }

        // 如果正在编辑，先完成编辑
        if (editingCell) {
            commitEdit()
        }

        // 如果正在批量编辑，先完成批量编辑
        if (batchEditing) {
            commitBatchEdit()
        }

        if (event.ctrlKey || event.metaKey) {
            // Ctrl/Cmd+点击：添加新的选择
            setSelections((prev) => [...prev, { start: { rowIndex, colIndex }, end: { rowIndex, colIndex } }])

            // 更新activeCell
            setActiveCell({ rowIndex, colIndex })
        } else if (event.shiftKey && activeCell) {
            // Shift+点击：从活动单元格到当前单元格的范围
            // 如果已经有选择，使用第一个选择的起点作为起始点
            const startCell = selections.length > 0 ? selections[0].start : activeCell

            setSelections([
                {
                    start: startCell,
                    end: { rowIndex, colIndex },
                },
            ])

            // Shift+点击时不更新activeCell，保持原来的活动单元格位置
        } else {
            // 普通点击：设置活动单元格并清除其他选择
            setSelections([
                {
                    start: { rowIndex, colIndex },
                    end: { rowIndex, colIndex },
                },
            ])

            // 更新activeCell
            setActiveCell({ rowIndex, colIndex })
        }

        // 清除任何文本选择，防止单元格内容显示为被选中状态
        if (window.getSelection) {
            window.getSelection()?.removeAllRanges()
        }
    }

    // 将光标移动到文本末尾
    const moveCursorToEnd = (element: HTMLElement) => {
        // 确保元素有内容
        if (!element.firstChild) {
            element.appendChild(document.createTextNode(""))
        }

        const range = document.createRange()
        const selection = window.getSelection()

        // 将范围设置到文本节点的末尾
        range.selectNodeContents(element)
        range.collapse(false) // false表示折叠到末尾

        // 应用选择
        if (selection) {
            selection.removeAllRanges()
            selection.addRange(range)
        }
    }

    // 处理双击进入编辑模式
    const handleCellDoubleClick = (rowIndex: number, colIndex: number, value: any) => {
        // 只允许编辑文本和数字类型的单元格
        if (propColumns[colIndex].type === "boolean") return
        // 如果列不可编辑，则不允许双击编辑
        if (propColumns[colIndex].editable === false) return

        setEditingCell({ rowIndex, colIndex })

        // 聚焦到可编辑单元格
        setTimeout(() => {
            if (editableCellRef.current) {
                editableCellRef.current.focus()

                // 将光标移到文本末尾
                const range = document.createRange()
                const selection = window.getSelection()

                if (editableCellRef.current.childNodes.length > 0) {
                    const textNode = editableCellRef.current.childNodes[0]
                    const textLength = textNode.textContent?.length || 0
                    range.setStart(textNode, textLength)
                    range.collapse(true)

                    if (selection) {
                        selection.removeAllRanges()
                        selection.addRange(range)
                    }
                } else {
                    // 如果没有文本节点，创建一个
                    const textNode = document.createTextNode(String(value))
                    editableCellRef.current.appendChild(textNode)

                    range.setStart(textNode, textNode.length)
                    range.collapse(true)

                    if (selection) {
                        selection.removeAllRanges()
                        selection.addRange(range)
                    }
                }
            }
        }, 0)
    }

    // 提交单个单元格编辑
    const commitEdit = () => {
        if (!editingCell) return

        const { rowIndex, colIndex } = editingCell
        const columnId = propColumns[colIndex].id
        const columnType = propColumns[colIndex].type

        // 从DOM元素获取当前内容，而不是使用状态中的editValue
        let newValue: any = editableCellRef.current?.textContent || ""

        if (columnType === "number") {
            newValue = Number.parseInt(newValue) || 0
        } else if (columnType === "boolean") {
            newValue = newValue === "true"
        }

        // 更新数据
        setItems((prevItems) => {
            const newItems = [...prevItems]
            const selectedCells = getSelectedCells() // Indices are for filteredItems

            const itemInFiltered = filteredItems[rowIndex]; // The primary cell being edited (in filtered view)
            if (!itemInFiltered && selectedCells.length <= 1) return prevItems; // Should not happen if editing

            if (selectedCells.length > 1) {
                // For multi-cell edit, iterate through selected cells
                selectedCells.forEach((cell) => {
                    // Find the item in filteredItems that corresponds to this selected cell
                    const currentItemInFiltered = filteredItems[cell.rowIndex];
                    if (!currentItemInFiltered) return; // Skip if no corresponding item in filtered view

                    // Find the original item in newItems (copy of items state) using its unique internal _id
                    const originalItemIndex = newItems.findIndex(it => it._id === currentItemInFiltered._id);
                    if (originalItemIndex === -1) return; // Skip if the original item is not found

                    const cellColumnId = propColumns[cell.colIndex].id
                    const cellColumnType = propColumns[cell.colIndex].type

                    // Only update if the column type of the selected cell matches the type of the initially edited cell
                    if (cellColumnType === columnType) {
                        newItems[originalItemIndex] = {
                            ...newItems[originalItemIndex],
                            [cellColumnId]: newValue,
                        }
                    }
                })
            } else if (itemInFiltered) { // Single cell edit
                // Find the original item in newItems (copy of items state) using its unique internal _id
                const originalItemIndex = newItems.findIndex(it => it._id === itemInFiltered._id);
                if (originalItemIndex !== -1) {
                    newItems[originalItemIndex] = {
                        ...newItems[originalItemIndex],
                        [columnId]: newValue,
                    }
                }
            } else {
                // Fallback if itemInFiltered is somehow undefined but it's a single cell edit (should not occur)
                return prevItems;
            }
            return newItems
        })

        setEditingCell(null)
        // 确保焦点回到表格容器
        setTimeout(() => {
            tableContainerRef.current?.focus()
        }, 0)
    }

    // 开始批量编辑
    const startBatchEdit = (initialValue = "") => {
        setBatchEditing(true)
        setBatchEditValue(initialValue)

        // 聚焦到批量编辑输入框
        setTimeout(() => {
            if (batchEditInputRef.current) {
                batchEditInputRef.current.focus()
                batchEditInputRef.current.select()
            }
        }, 0)
    }

    // 提交批量编辑
    const commitBatchEdit = () => {
        if (!batchEditing) return

        const selectedCells = getSelectedCells()

        // 检查所有选中单元格是否都是同一类型
        const firstCell = selectedCells[0]
        if (!firstCell) {
            setBatchEditing(false)
            return
        }

        const columnType = propColumns[firstCell.colIndex].type
        // const columnId = propColumns[firstCell.colIndex].id // Not directly used for setting value, but for type checking

        // 根据列类型处理值
        let newValue: any = batchEditValue
        if (columnType === "number") {
            newValue = Number.parseInt(batchEditValue) || 0
        } else if (columnType === "boolean") {
            newValue = batchEditValue === "true"
        }

        // 更新所有选中的同类型单元格
        setItems((prevItems) => {
            const newItems = [...prevItems]

            selectedCells.forEach((cell) => {
                const currentItemInFiltered = filteredItems[cell.rowIndex];
                if (!currentItemInFiltered) return;
                const originalItemIndex = newItems.findIndex(it => it._id === currentItemInFiltered._id);
                if (originalItemIndex === -1) return;

                const cellColumnType = propColumns[cell.colIndex].type
                const cellColumnId = propColumns[cell.colIndex].id

                // 只更新相同类型的列
                if (cellColumnType === columnType) {
                    newItems[originalItemIndex] = {
                        ...newItems[originalItemIndex],
                        [cellColumnId]: newValue,
                    }
                }
            })
            return newItems
        })

        setBatchEditing(false)
    }

    // 切换选中单元格的布尔值
    const toggleSelectedBooleanCells = () => {
        const selectedCells = getSelectedCells()

        // 检查是否有布尔类型的单元格
        const booleanCells = selectedCells.filter((cell) => propColumns[cell.colIndex].type === "boolean")
        if (booleanCells.length === 0) return

        // 确定切换方向（如果大多数是true，则切换为false，反之亦然）
        let trueCount = 0
        // Iterate over a snapshot of items or correctly map filteredItems to items for counting
        booleanCells.forEach((cell) => {
            const itemInFiltered = filteredItems[cell.rowIndex];
            if (!itemInFiltered) return;
            const originalItem = items.find(it => it._id === itemInFiltered._id); // Find in current `items` state using _id
            if (!originalItem) return;

            const columnId = propColumns[cell.colIndex].id
            if (originalItem[columnId] === true) {
                trueCount++
            }
        })

        const newValue = trueCount < booleanCells.length / 2

        // 更新所有选中的布尔单元格
        setItems((prevItems) => {
            const newItems = [...prevItems]

            booleanCells.forEach((cell) => {
                const itemInFiltered = filteredItems[cell.rowIndex];
                if (!itemInFiltered) return;
                const originalItemIndex = newItems.findIndex(it => it._id === itemInFiltered._id);
                if (originalItemIndex === -1) return;

                const columnId = propColumns[cell.colIndex].id

                newItems[originalItemIndex] = {
                    ...newItems[originalItemIndex],
                    [columnId]: newValue,
                }
            })
            return newItems
        })
    }

    // 处理复制操作
    const handleCopy = () => {
        if (selections.length === 0) return

        const selectedCells = getSelectedCells()
        if (selectedCells.length === 0) return

        // 确定选择区域的边界
        let minRow = Number.MAX_SAFE_INTEGER
        let maxRow = 0
        let minCol = Number.MAX_SAFE_INTEGER
        let maxCol = 0

        selectedCells.forEach((cell) => {
            minRow = Math.min(minRow, cell.rowIndex)
            maxRow = Math.max(maxRow, cell.rowIndex)
            minCol = Math.min(minCol, cell.colIndex)
            maxCol = Math.max(maxCol, cell.colIndex)
        })

        // 创建一个二维数组表示选择区域
        const grid: string[][] = []
        for (let r = minRow; r <= maxRow; r++) {
            const row: string[] = []
            for (let c = minCol; c <= maxCol; c++) {
                if (isCellSelected(r, c)) {
                    const columnId = propColumns[c].id
                    const item = filteredItems[r]
                    row.push(String(item[columnId]))
                } else {
                    row.push("")
                }
            }
            grid.push(row)
        }

        // 将二维数组转换为制表符分隔的文本
        const text = grid.map((row) => row.join("\t")).join("\n")
        setClipboard(text)

        // 复制到剪贴板
        navigator.clipboard.writeText(text).catch((err) => {
            console.error("无法复制到剪贴板:", err)
        })
    }

    // 处理粘贴操作 - 支持多选
    const handlePaste = (pasteText: string) => {
        if (selections.length === 0 || !activeCell) return

        // 解析粘贴的文本为二维数组
        const rows = pasteText.split(/\r\n|\n|\r/).filter((row) => row.trim() !== "")
        const grid = rows.map((row) => row.split("\t"))

        // 获取选中的单元格
        const selectedCells = getSelectedCells()

        // 检查是否是多选状态
        if (selectedCells.length > 1) {
            // 多选状态下的粘贴逻辑

            // 检查是否所有选中的单元格都是同一类型且可编辑
            const firstCell = selectedCells[0]
            if (propColumns[firstCell.colIndex].editable === false) return; // 如果第一个单元格不可编辑，则不进行粘贴

            const firstColumnType = propColumns[firstCell.colIndex].type
            const sameTypeAndEditable = selectedCells.every((cell) => propColumns[cell.colIndex].type === firstColumnType && propColumns[cell.colIndex].editable !== false)

            if (sameTypeAndEditable) {
                // 如果所有单元格都是同一类型且可编辑，使用第一个粘贴值应用到所有选中的单元格
                const pasteValue = grid[0][0] || ""

                // 根据列类型处理值
                let typedValue: any = pasteValue
                if (firstColumnType === "number") {
                    typedValue = Number.parseInt(pasteValue) || 0
                } else if (firstColumnType === "boolean") {
                    typedValue = pasteValue.toLowerCase() === "true"
                }

                // 更新所有选中的单元格
                setItems((prevItems) => {
                    const newItems = [...prevItems]

                    selectedCells.forEach((cell) => {
                        const itemInFiltered = filteredItems[cell.rowIndex];
                        if (!itemInFiltered) return;
                        // 将筛选后的索引转换为原始数据索引
                        const originalRowIndex = newItems.findIndex(it => it._id === itemInFiltered._id)
                        if (originalRowIndex === -1) return // 如果找不到对应的原始行，跳过

                        const columnId = propColumns[cell.colIndex].id

                        newItems[originalRowIndex] = {
                            ...newItems[originalRowIndex],
                            [columnId]: typedValue,
                        }
                    })
                    return newItems
                })
            } else {
                // 如果选中的单元格类型不同，或者某些单元格不可编辑，尝试按区域填充（跳过不可编辑的）

                // 确定选择区域的边界
                let minRow = Number.MAX_SAFE_INTEGER
                let maxRow = 0
                let minCol = Number.MAX_SAFE_INTEGER
                let maxCol = 0

                selectedCells.forEach((cell) => {
                    minRow = Math.min(minRow, cell.rowIndex)
                    maxRow = Math.max(maxRow, cell.rowIndex)
                    minCol = Math.min(minCol, cell.colIndex)
                    maxCol = Math.max(maxCol, cell.colIndex)
                })

                // 更新数据
                setItems((prevItems) => {
                    const newItems = [...prevItems]

                    // 遍历选中区域
                    for (let r = minRow; r <= maxRow; r++) {
                        for (let c = minCol; c <= maxCol; c++) {
                            if (isCellSelected(r, c)) {
                                const itemInFiltered = filteredItems[r];
                                if (!itemInFiltered) continue;
                                // 将筛选后的索引转换为原始数据索引
                                const originalRowIndex = newItems.findIndex(it => it._id === itemInFiltered._id)
                                if (originalRowIndex === -1) continue // 如果找不到对应的原始行，跳过

                                // 跳过不可编辑的单元格
                                if (propColumns[c].editable === false) continue;

                                // 计算对应的粘贴数据索引（循环使用粘贴数据）
                                const gridRow = (r - minRow) % grid.length
                                const gridCol = (c - minCol) % grid[0].length

                                const pasteValue = grid[gridRow][gridCol] || ""
                                const columnId = propColumns[c].id
                                const columnType = propColumns[c].type

                                // 根据列类型处理值
                                let typedValue: any = pasteValue
                                if (columnType === "number") {
                                    typedValue = Number.parseInt(pasteValue) || 0
                                } else if (columnType === "boolean") {
                                    typedValue = pasteValue.toLowerCase() === "true"
                                }

                                newItems[originalRowIndex] = {
                                    ...newItems[originalRowIndex],
                                    [columnId]: typedValue,
                                }
                            }
                        }
                    }
                    return newItems
                })
            }
        } else {
            // 单选状态下的粘贴逻辑（从活动单元格开始填充）
            const { rowIndex: startRow, colIndex: startCol } = activeCell

            // 更新数据
            setItems((prevItems) => {
                const newItems = [...prevItems]

                for (let r = 0; r < grid.length; r++) {
                    const targetRow = startRow + r
                    if (targetRow >= filteredItems.length) break

                    const itemInFiltered = filteredItems[targetRow];
                    if (!itemInFiltered) continue;
                    // 将筛选后的索引转换为原始数据索引
                    const originalRowIndex = newItems.findIndex((item) => item._id === itemInFiltered._id)
                    if (originalRowIndex === -1) continue // 如果找不到对应的原始行，跳过

                    for (let c = 0; c < grid[r].length; c++) {
                        const targetCol = startCol + c
                        if (targetCol >= propColumns.length) break

                        // 跳过不可编辑的单元格
                        if (propColumns[targetCol].editable === false) continue;

                        const columnId = propColumns[targetCol].id
                        const columnType = propColumns[targetCol].type

                        // 根据列类型处理值
                        let newValue: any = grid[r][c]
                        if (columnType === "number") {
                            newValue = Number.parseInt(newValue) || 0
                        } else if (columnType === "boolean") {
                            newValue = newValue.toLowerCase() === "true"
                        }

                        newItems[originalRowIndex] = {
                            ...newItems[originalRowIndex],
                            [columnId]: newValue,
                        }
                    }
                }
                return newItems
            })
        }
    }

    // 显示粘贴提示
    const showPasteTooltip = () => {
        setShowPasteHint(true)
        setTimeout(() => {
            setShowPasteHint(false)
        }, 3000) // 3秒后自动隐藏
    }

    // 处理键盘导航和快捷键
    const handleKeyDown = (e: KeyboardEvent) => {
        // 如果正在编辑，处理编辑相关的键
        if (editingCell) {
            if (e.key === "Enter" || e.key === "Tab") {
                e.preventDefault()
                commitEdit()

                // Enter键向下移动，Tab键向右移动
                if (e.key === "Enter" && editingCell) {
                    const newRowIndex = Math.min(filteredItems.length - 1, editingCell.rowIndex + 1)
                    setActiveCell({ rowIndex: newRowIndex, colIndex: editingCell.colIndex })
                    setSelections([
                        {
                            start: { rowIndex: newRowIndex, colIndex: editingCell.colIndex },
                            end: { rowIndex: newRowIndex, colIndex: editingCell.colIndex },
                        },
                    ])
                } else if (e.key === "Tab" && editingCell) {
                    const newColIndex = Math.min(propColumns.length - 1, editingCell.colIndex + 1)
                    setActiveCell({ rowIndex: editingCell.rowIndex, colIndex: newColIndex })
                    setSelections([
                        {
                            start: { rowIndex: editingCell.rowIndex, colIndex: newColIndex },
                            end: { rowIndex: editingCell.rowIndex, colIndex: newColIndex },
                        },
                    ])
                }
                return
            }

            if (e.key === "Escape") {
                e.preventDefault()
                setEditingCell(null)
                // 确保焦点回到表格容器
                setTimeout(() => {
                    tableContainerRef.current?.focus()
                }, 0)
                return
            }

            // 其他键正常处理编辑
            return
        }

        // 如果正在批量编辑，处理批量编辑相关的键
        if (batchEditing) {
            if (e.key === "Enter") {
                e.preventDefault()
                commitBatchEdit()
                return
            }

            if (e.key === "Escape") {
                e.preventDefault()
                setBatchEditing(false)
                return
            }

            // 其他键正常处理批量编辑
            return
        }

        if (!activeCell) return

        const { rowIndex, colIndex } = activeCell

        // 获取当前选择区域的结束位置，用于Shift+方向键移动
        let currentEndRow = rowIndex
        let currentEndCol = colIndex

        // 如果有选择区域且是Shift按下状态，使用选择区域的结束位置
        if (e.shiftKey && selections.length > 0) {
            currentEndRow = selections[0].end.rowIndex
            currentEndCol = selections[0].end.colIndex
        }

        let newRowIndex = currentEndRow
        let newColIndex = currentEndCol

        // 处理 Enter 键进入编辑模式
        if (e.key === "Enter" && !e.shiftKey && !e.ctrlKey && !e.metaKey && !e.altKey) {
            const { rowIndex, colIndex } = activeCell
            if (propColumns[colIndex].type !== "boolean") {
                e.preventDefault()
                handleCellDoubleClick(rowIndex, colIndex, getCellValue(rowIndex, colIndex))
                return
            }
        }

        // 处理复制粘贴快捷键
        if ((e.ctrlKey || e.metaKey) && e.key === "c") {
            e.preventDefault()
            handleCopy()
            return
        }

        if ((e.ctrlKey || e.metaKey) && e.key === "v") {
            e.preventDefault()
            // 不再尝试直接读取剪贴板，而是依赖onPaste事件
            // 如果有内部存储的剪贴板内容，则使用它
            if (clipboard) {
                handlePaste(clipboard)
            } else {
                // 显示粘贴提示
                showPasteTooltip()
            }
            return
        }

        // 处理空格键切换布尔值
        if (e.key === " " && !e.ctrlKey && !e.metaKey) {
            e.preventDefault()
            toggleSelectedBooleanCells()
            return
        }

        // 处理开始编辑的键
        if (
            e.key === "F2" ||
            (e.key !== "Tab" && e.key !== "Escape" && e.key.length === 1 && !e.ctrlKey && !e.metaKey && !e.altKey)
        ) {
            const selectedCells = getSelectedCells()

            // 检查所有选中的单元格是否都是同一类型且不是布尔类型
            const firstCell = selectedCells[0]
            if (!firstCell) return
            // 检查第一个单元格是否可编辑
            if (propColumns[firstCell.colIndex].editable === false) return

            const firstColumnType = propColumns[firstCell.colIndex].type
            const allSameTypeAndEditable = selectedCells.every(
                (cell) => propColumns[cell.colIndex].type === firstColumnType && firstColumnType !== "boolean" && propColumns[cell.colIndex].editable !== false,
            )

            if (selectedCells.length > 1 && allSameTypeAndEditable) {
                // 多选状态下，如果所有单元格都是同一类型且可编辑，直接开始编辑
                e.preventDefault()

                // 设置第一个单元格为编辑状态
                setEditingCell(firstCell)

                // 聚焦到可编辑单元格
                setTimeout(() => {
                    if (editableCellRef.current) {
                        // 设置初始内容为当前单元格值
                        const currentValue = String(getCellValue(firstCell.rowIndex, firstCell.colIndex))
                        editableCellRef.current.textContent = currentValue

                        editableCellRef.current.focus()
                        moveCursorToEnd(editableCellRef.current)

                        // 如果不是F2键，在光标位置插入按下的键
                        if (e.key !== "F2") {
                            const selection = window.getSelection()
                            if (selection && selection.rangeCount > 0) {
                                const range = selection.getRangeAt(0)
                                const textNode = document.createTextNode(e.key)
                                range.insertNode(textNode)
                                range.setStartAfter(textNode)
                                range.setEndAfter(textNode)
                                selection.removeAllRanges()
                                selection.addRange(range)
                            }
                        }
                    }
                }, 0)
                return
            } else if (selectedCells.length > 1) {
                // 多选状态下，如果单元格类型不同，或者某些单元格不可编辑，则不允许批量编辑（除非是F2，这时应检查是否所有都可编辑）
                // 对于字符输入触发的批量编辑，需要确保所有选中的单元格都允许编辑。
                const allEditableForBatch = selectedCells.every(cell => propColumns[cell.colIndex].editable !== false);
                if (!allEditableForBatch && e.key !== "F2") return;

                e.preventDefault()
                if (e.key === "F2") {
                    startBatchEdit()
                } else {
                    startBatchEdit(e.key)
                }
                return
            } else if (propColumns[colIndex].type !== "boolean" && propColumns[colIndex].editable !== false) {
                // 单选状态下，开始单元格编辑
                e.preventDefault()

                if (e.key === "F2") {
                    handleCellDoubleClick(rowIndex, colIndex, getCellValue(rowIndex, colIndex))
                } else {
                    // 设置编辑状态
                    setEditingCell({ rowIndex, colIndex })

                    // 聚焦到可编辑单元格
                    setTimeout(() => {
                        if (editableCellRef.current) {
                            // 设置初始内容为当前单元格值
                            const currentValue = String(getCellValue(rowIndex, colIndex))
                            editableCellRef.current.textContent = currentValue

                            editableCellRef.current.focus()
                            moveCursorToEnd(editableCellRef.current)

                            // 在光标位置插入按下的键
                            const selection = window.getSelection()
                            if (selection && selection.rangeCount > 0) {
                                const range = selection.getRangeAt(0)
                                const textNode = document.createTextNode(e.key)
                                range.insertNode(textNode)
                                range.setStartAfter(textNode)
                                range.setEndAfter(textNode)
                                selection.removeAllRanges()
                                selection.addRange(range)
                            }
                        }
                    }, 0)
                    return
                }
            }
        }

        // 处理导航键
        switch (e.key) {
            case "ArrowUp":
                e.preventDefault();
                // Only move if not at the first row AND the current column is navigable
                if (currentEndRow > 0 && !(propColumns[currentEndCol].editable === false && propColumns[currentEndCol].type !== "boolean")) {
                    newRowIndex = currentEndRow - 1;
                }
                break;
            case "ArrowDown":
                e.preventDefault();
                // Only move if not at the last row AND the current column is navigable
                if (currentEndRow < filteredItems.length - 1 && !(propColumns[currentEndCol].editable === false && propColumns[currentEndCol].type !== "boolean")) {
                    newRowIndex = currentEndRow + 1;
                }
                break;
            case "ArrowLeft":
                e.preventDefault();
                let newPotentialColLeft = currentEndCol - 1;
                // Scan leftwards for the first navigable column
                while (newPotentialColLeft >= 0 &&
                    (propColumns[newPotentialColLeft].editable === false && propColumns[newPotentialColLeft].type !== "boolean")) {
                    newPotentialColLeft--;
                }
                // If a navigable column is found within bounds
                if (newPotentialColLeft >= 0) {
                    newColIndex = newPotentialColLeft;
                }
                break;
            case "ArrowRight":
                e.preventDefault();
                let newPotentialColRight = currentEndCol + 1;
                // Scan rightwards for the first navigable column
                while (newPotentialColRight < propColumns.length &&
                    (propColumns[newPotentialColRight].editable === false && propColumns[newPotentialColRight].type !== "boolean")) {
                    newPotentialColRight++;
                }
                // If a navigable column is found within bounds
                if (newPotentialColRight < propColumns.length) {
                    newColIndex = newPotentialColRight;
                }
                break;
            case "Tab":
                e.preventDefault();
                let tempR = rowIndex;
                let tempC = colIndex;
                const numRows = filteredItems.length;
                const numCols = propColumns.length;
                let iterations = numRows * numCols; // Safety break for full table traversal

                if (e.shiftKey) { // Backwards
                    while (iterations-- > 0) {
                        if (tempC > 0) {
                            tempC--;
                        } else if (tempR > 0) {
                            tempR--;
                            tempC = numCols - 1;
                        } else {
                            break; // Reached (0,0) and cannot go further back
                        }
                        // Check if the new cell (tempR, tempC) is navigable
                        if (!(propColumns[tempC].editable === false && propColumns[tempC].type !== "boolean")) {
                            newRowIndex = tempR;
                            newColIndex = tempC;
                            break; // Found a navigable cell
                        }
                        // If we have looped back to the start without finding anything (e.g. single uneditable cell table)
                        if (tempR === rowIndex && tempC === colIndex) break;
                    }
                } else { // Forwards
                    while (iterations-- > 0) {
                        if (tempC < numCols - 1) {
                            tempC++;
                        } else if (tempR < numRows - 1) {
                            tempR++;
                            tempC = 0;
                        } else {
                            break; // Reached last cell and cannot go further
                        }
                        // Check if the new cell (tempR, tempC) is navigable
                        if (!(propColumns[tempC].editable === false && propColumns[tempC].type !== "boolean")) {
                            newRowIndex = tempR;
                            newColIndex = tempC;
                            break; // Found a navigable cell
                        }
                        // If we have looped back to the start without finding anything
                        if (tempR === rowIndex && tempC === colIndex) break;
                    }
                }
                break;
            case "Enter":
                e.preventDefault()
                // Enter键向下移动
                newRowIndex = Math.min(filteredItems.length - 1, rowIndex + 1)
                break
            case "a":
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault()
                    // Ctrl+A 全选
                    setSelections([
                        {
                            start: { rowIndex: 0, colIndex: 0 },
                            end: { rowIndex: filteredItems.length - 1, colIndex: propColumns.length - 1 },
                        },
                    ])
                    return
                }
                break
            case "Escape":
                e.preventDefault()
                setSelections([])
                return
        }

        // 如果位置改变了
        if (newRowIndex !== currentEndRow || newColIndex !== currentEndCol) {
            if (e.shiftKey) {
                // Shift+方向键：扩展选择
                setSelections([
                    {
                        start: selections.length > 0 ? selections[0].start : activeCell,
                        end: { rowIndex: newRowIndex, colIndex: newColIndex },
                    },
                ])

                // 清除文本选择
                if (window.getSelection) {
                    window.getSelection()?.removeAllRanges()
                }

                // 不更新activeCell，保持原来的活动单元格
            } else {
                // 普通方向键：移动活动单元格
                setSelections([
                    {
                        start: { rowIndex: newRowIndex, colIndex: newColIndex },
                        end: { rowIndex: newRowIndex, colIndex: newColIndex },
                    },
                ])

                setActiveCell({ rowIndex: newRowIndex, colIndex: newColIndex })
            }

            // 确保新的活动单元格在视图中
            setTimeout(() => {
                const activeElement = document.querySelector(".active-cell")
                if (activeElement) {
                    activeElement.scrollIntoView({ block: "nearest" })
                }
            }, 0)
        }
    }

    // 处理可编辑单元格的输入
    const handleEditableInput = () => {
        // 不需要更新状态，只在提交编辑时获取最终内容
    }

    // 处理粘贴事件
    const handlePasteEvent = (e: ClipboardEvent) => {
        e.preventDefault()
        const text = e.clipboardData?.getData("text") || ""
        if (text) {
            handlePaste(text)
            // 同时更新内部剪贴板，以便后续使用
            setClipboard(text)
        }
    }

    // 获取单元格值
    const getCellValue = (rowIndex: number, colIndex: number) => {
        const item = filteredItems[rowIndex]
        if (!item) return undefined; // Handle case where item might not exist
        const columnId = propColumns[colIndex].id
        return item[columnId]
    }

    // 渲染单元格内容
    const renderCellContent = (item: Item, rowIndex: number, colIndex: number) => {
        const columnId = propColumns[colIndex].id
        const columnType = propColumns[colIndex].type
        const value = item[columnId]

        // 如果当前单元格正在编辑
        if (editingCell && editingCell.rowIndex === rowIndex && editingCell.colIndex === colIndex) {
            return (
                <div
                    ref={editableCellRef}
                    contentEditable
                    suppressContentEditableWarning
                    className="w-full h-full outline-none p-2"
                    onInput={handleEditableInput}
                    onBlur={commitEdit}
                    onKeyDown={(e) => {
                        e.stopPropagation()
                        if (e.key === "Enter" || e.key === "Tab") {
                            e.preventDefault()
                            commitEdit()

                            // Enter键向下移动，Tab键向右移动
                            if (e.key === "Enter") {
                                const newRowIndex = Math.min(filteredItems.length - 1, rowIndex + 1)
                                setActiveCell({ rowIndex: newRowIndex, colIndex })
                                setSelections([
                                    {
                                        start: { rowIndex: newRowIndex, colIndex },
                                        end: { rowIndex: newRowIndex, colIndex },
                                    },
                                ])
                            } else if (e.key === "Tab") {
                                const newColIndex = Math.min(propColumns.length - 1, colIndex + 1)
                                setActiveCell({ rowIndex, colIndex: newColIndex })
                                setSelections([
                                    {
                                        start: { rowIndex, colIndex: newColIndex },
                                        end: { rowIndex, colIndex: newColIndex },
                                    },
                                ])
                            }
                        } else if (e.key === "Escape") {
                            e.preventDefault()
                            setEditingCell(null)
                            // 确保焦点回到表格容器
                            setTimeout(() => {
                                tableContainerRef.current?.focus()
                            }, 0)
                        }
                    }}
                >
                    {/* 不再设置初始内容，让它在挂载后通过DOM操作设置 */}
                </div>
            )
        }

        // 根据列类型渲染不同的内容
        if (columnType === "boolean") {
            return (
                <div className="table-cell-content flex justify-center items-center h-full">
                    <Switch
                        checked={value as boolean}
                        onCheckedChange={(checked) => {
                            setItems((prev) => {
                                const newItems = [...prev]
                                // 找到原始数据中对应的项
                                const originalIndex = newItems.findIndex((i) => i._id === item._id) // item is from filteredItems, use _id
                                if (originalIndex !== -1) {
                                    newItems[originalIndex] = {
                                        ...newItems[originalIndex],
                                        [columnId]: checked, // columnId is from propColumns
                                    }
                                }
                                return newItems
                            })
                        }}
                        onClick={(e) => e.stopPropagation()}
                    />
                </div>
            )
        }

        if (columnType === "number") {
            const num = Number(value);
            if (!Number.isNaN(num)) {
                return <div className="table-cell-content text-right">{num.toLocaleString()}</div>;
            }
            // For NaN or if value couldn't be converted to a valid number, display original value or an empty string for null/undefined
            return <div className="table-cell-content text-right">{value === null || value === undefined ? "" : String(value)}</div>;
        }

        return <div className="table-cell-content">{value}</div>
    }

    // 渲染筛选菜单
    const renderFilterMenu = (columnId: string, columnType: string) => {
        const uniqueValues = getUniqueValues(columnId)
        const filteredValues = filterSearch
            ? uniqueValues.filter((value) => String(value).toLowerCase().includes(filterSearch.toLowerCase()))
            : uniqueValues

        return (
            <div className="p-4 w-80">
                <div className="flex items-center space-x-2 mb-4">
                    <Search className="h-4 w-4 text-gray-500" />
                    <Input
                        placeholder="Search..."
                        value={filterSearch}
                        onChange={(e) => setFilterSearch(e.target.value)}
                        className="h-8"
                    />
                </div>

                <div className="flex justify-between items-center mb-4">
                    <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs"
                        onClick={() => {
                            // 选择所有值
                            const newConditions = { ...filterConditions }
                            if (!newConditions[columnId]) {
                                newConditions[columnId] = new Set()
                            }
                            uniqueValues.forEach((value) => {
                                newConditions[columnId].add(String(value))
                            })
                            setFilterConditions(newConditions)
                        }}
                    >
                        全选
                    </Button>

                    <Button
                        variant="ghost"
                        size="sm"
                        className="text-xs"
                        onClick={() => {
                            // 清除所有选择
                            clearColumnFilter(columnId)
                        }}
                    >
                        清除
                    </Button>
                </div>

                <ScrollArea className="h-64">
                    <div className="space-y-2">
                        {filteredValues.map((value) => (
                            <div key={value} className="flex items-center space-x-2">
                                <Checkbox
                                    id={`${columnId}-${value}`}
                                    checked={filterConditions[columnId]?.has(String(value)) || false}
                                    onCheckedChange={(checked) => {
                                        handleFilterChange(columnId, String(value), checked === true)
                                    }}
                                />
                                <label htmlFor={`${columnId}-${value}`} className="text-sm cursor-pointer flex-1 truncate">
                                    {columnType === "boolean" ? (value === "true" ? "是" : "否") : value}
                                </label>
                            </div>
                        ))}
                    </div>
                </ScrollArea>

                <div className="flex justify-end gap-2 mt-4 pt-4 border-t">
                    <Button variant="outline" size="sm" onClick={() => closeFilterDialog(columnId)}>
                        取消
                    </Button>
                    <Button size="sm" onClick={() => closeFilterDialog(columnId)}>
                        确定
                    </Button>
                </div>
            </div>
        )
    }

    // 设置事件监听
    useEffect(() => {
        const handleGlobalKeyDown = (e: globalThis.KeyboardEvent) => {
            // 防止全局快捷键冲突
            if ((e.ctrlKey || e.metaKey) && (e.key === "c" || e.key === "v")) {
                // 允许复制粘贴事件冒泡到表格处理函数
                return
            }
        }

        window.addEventListener("keydown", handleGlobalKeyDown)

        return () => {
            window.removeEventListener("keydown", handleGlobalKeyDown)
        }
    }, [])

    return (
        <div
            ref={tableContainerRef}
            className="edit-table-container flex-1 overflow-auto focus:outline-none"
            tabIndex={0}
            onKeyDown={handleKeyDown}
            onPaste={handlePasteEvent}
        >
            <Table className="border-collapse">
                <TableHeader>
                    <TableRow className="">
                        {propColumns.map((column) => (
                            <TableHead
                                key={column.id}
                                className={cn(
                                    "border-r border-gray-200 relative text-gray-500 bg-white",
                                    column.id === "quantity" ? "text-right" : "",
                                    hasColumnFilter(column.id) ? "bg-blue-50" : "",
                                )}
                            >
                                <div className="flex items-center justify-between">
                                    <span>{column.header}</span>
                                    <Dialog
                                        open={filterDialogOpen[column.id] || false}
                                        onOpenChange={(open) => {
                                            if (open) {
                                                openFilterDialog(column.id)
                                            } else {
                                                closeFilterDialog(column.id)
                                            }
                                        }}
                                    >
                                        {/* <DialogTrigger asChild>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className={cn(
                                                    "h-6 w-6 p-0 ml-1",
                                                    hasColumnFilter(column.id) ? "text-blue-500" : "text-gray-500",
                                                )}
                                                onClick={() => openFilterDialog(column.id)}
                                            >
                                                <Filter className="h-3 w-3" />
                                            </Button>
                                        </DialogTrigger> */}
                                        <DialogContent className="p-0 max-w-md">
                                            <DialogHeader className="p-4 pb-0">
                                                <DialogTitle>筛选 {column.header}</DialogTitle>
                                            </DialogHeader>
                                            {renderFilterMenu(column.id, column.type)}
                                        </DialogContent>
                                    </Dialog>
                                </div>
                            </TableHead>
                        ))}
                    </TableRow>
                </TableHeader>
                <TableBody>
                    {filteredItems.map((item, rowIndex) => (
                        <TableRow key={item._id}>
                            {propColumns.map((column, colIndex) => (
                                <TableCell
                                    key={`${item._id}-${column.id}`}
                                    className={cn(
                                        "cursor-pointer border border-gray-200 min-h-[36px] text-gray-500 bg-white relative",
                                        // 不可编辑单元格的特定样式 (优先级最高)
                                        (propColumns[colIndex].editable === false && propColumns[colIndex].type !== "boolean")
                                            ? "cursor-not-allowed text-gray-400"
                                            : [
                                                // 活动单元格 (非编辑状态, 且可被选中) - 第一个选中的单元格，有边框
                                                activeCell?.rowIndex === rowIndex &&
                                                activeCell?.colIndex === colIndex &&
                                                !editingCell &&
                                                "bg-blue-50 ring-2  ring-inset ring-blue-500 z-10",

                                                // 选中的非活动单元格 - 只有背景色变化，没有边框
                                                isCellSelected(rowIndex, colIndex) &&
                                                !editingCell &&
                                                !(activeCell?.rowIndex === rowIndex && activeCell?.colIndex === colIndex) &&
                                                (propColumns[colIndex].editable !== false || propColumns[colIndex].type === "boolean") &&
                                                "bg-blue-50 z-0",

                                                // 编辑中的单元格
                                                editingCell?.rowIndex === rowIndex &&
                                                editingCell?.colIndex === colIndex &&
                                                "p-1 ring-2 ring-inset ring-blue-500 z-20 bg-white",

                                                // 多选编辑中的其他单元格 - 只有背景色变化
                                                editingCell &&
                                                isCellSelected(rowIndex, colIndex) &&
                                                !(editingCell.rowIndex === rowIndex && editingCell.colIndex === colIndex) &&
                                                propColumns[colIndex].type === propColumns[editingCell.colIndex].type &&
                                                (propColumns[colIndex].editable !== false || propColumns[colIndex].type === "boolean") &&
                                                "bg-blue-50 z-0",
                                            ],
                                    )}
                                    onClick={(e) => handleCellClick(rowIndex, colIndex, e)}
                                    onDoubleClick={() => {
                                        // 双击不可编辑单元格时不执行任何操作
                                        if (propColumns[colIndex].editable === false && propColumns[colIndex].type !== "boolean") {
                                            return;
                                        }
                                        handleCellDoubleClick(rowIndex, colIndex, getCellValue(rowIndex, colIndex))
                                    }}
                                >
                                    {renderCellContent(item, rowIndex, colIndex)}
                                </TableCell>
                            ))}
                        </TableRow>
                    ))}
                    {filteredItems.length === 0 && (
                        <TableRow>
                            <TableCell colSpan={propColumns.length} className="h-32 text-center text-gray-500 bg-white">
                                没有符合筛选条件的数据
                            </TableCell>
                        </TableRow>
                    )}
                </TableBody>
            </Table>
        </div>
    )
});

export default ListEditor;
