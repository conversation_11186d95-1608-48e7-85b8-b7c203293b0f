import { Checkbox } from "@/components/ui/checkbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Table } from "@/components/ui/table";
import { cn } from "@/lib/utils";
import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import React from 'react';
import { TableBody } from './components/TableBody';
import { TableHeader } from './components/TableHeader';
import { DataTablePagination } from './data-table-pagination';
import { useAutoTableHeight } from './hooks/useAutoTableHeight';
import { useTableData } from './hooks/useTableData';
import { useTableState } from './hooks/useTableState';
import { type DataTableProps } from './types';

export function DataTable<TData, TValue>({
    columns,
    onFetch,
    toolbar,
    advancedSearch,
    isNeedSelect = false,
    dependencies = [],
    isFixedHeader = false,
    containerHeight: customHeight,
    isHidePagination = false,
    className,
}: DataTableProps<TData, TValue>) {
    const autoHeight = useAutoTableHeight();
    const finalHeight = customHeight || autoHeight;

    const tableId = React.useMemo(() => {
        return columns.map(c => c.id).join('-');
    }, [columns]);

    const {
        columnOrder,
        setColumnOrder,
        sorting,
        setSorting,
        columnFilters,
        setColumnFilters,
        columnVisibility,
        setColumnVisibility,
        pagination: { pageIndex, pageSize },
        setPagination,
        rowSelection,
        setRowSelection
    } = useTableState(columns, isNeedSelect, tableId);

    const [advancedFilters, setAdvancedFilters] = React.useState<Record<string, any>>({});

    const { data, rowCount, loading } = useTableData(
        onFetch,
        sorting,
        columnFilters,
        pageIndex,
        pageSize,
        advancedFilters,
        dependencies
    );

    const table = useReactTable({
        data,
        columns: isNeedSelect
            ? [
                {
                    id: 'select',
                    header: ({ table }) => (
                        <div className="h-2 flex items-center justify-center">
                            <Checkbox
                                checked={table.getIsAllPageRowsSelected()}
                                onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
                                aria-label="Select all"
                            />
                        </div>
                    ),
                    cell: ({ row }) => (
                        <div className="h-2 flex items-center justify-center">
                            <Checkbox
                                checked={row.getIsSelected()}
                                onCheckedChange={(value) => row.toggleSelected(!!value)}
                                aria-label="Select row"
                            />
                        </div>
                    ),
                    enableSorting: false,
                    enableHiding: false,
                },
                ...columns,
            ]
            : columns,
        pageCount: Math.ceil(rowCount / pageSize),
        state: {
            sorting,
            columnFilters,
            columnVisibility,
            pagination: {
                pageIndex,
                pageSize,
            },
            columnOrder,
            rowSelection,
        },
        onColumnOrderChange: setColumnOrder,
        onSortingChange: setSorting,
        onColumnFiltersChange: setColumnFilters,
        onPaginationChange: setPagination,
        onColumnVisibilityChange: setColumnVisibility,
        onRowSelectionChange: setRowSelection,
        getCoreRowModel: getCoreRowModel(),
        manualPagination: true,
        manualFiltering: true,
        manualSorting: true,
    });

    const handleAdvancedSearch = (filters: Record<string, any>) => {
        setAdvancedFilters(filters);
        setPagination({ pageIndex: 0, pageSize });
    };

    const handleAdvancedReset = () => {
        setAdvancedFilters({});
        setPagination({ pageIndex: 0, pageSize });
    };

    return (
        <div className="space-y-2 sm:space-y-4 text-[11px] sm:text-xs">
            {advancedSearch && (
                <>
                    <div className="p-2 sm:p-4">
                        {React.cloneElement(advancedSearch as React.ReactElement, {
                            // @ts-ignore
                            onSearch: handleAdvancedSearch,
                            onReset: handleAdvancedReset,
                        })}
                    </div>
                    <Separator />
                </>
            )}
            {toolbar && toolbar(table, tableId)}
            <div className={cn("", className)}>
                {isFixedHeader ? (
                    <ScrollArea className="relative" style={{ height: finalHeight }}>
                        <Table className="min-w-[320px]">
                            <TableHeader table={table} isFixedHeader={true} />
                            <TableBody table={table} loading={loading} />
                        </Table>
                    </ScrollArea>
                ) : (
                    <div className="relative overflow-auto">
                        <Table className="min-w-[320px]">
                            <TableHeader table={table} />
                            <TableBody table={table} loading={loading} />
                        </Table>
                    </div>
                )}
            </div>
            {!isHidePagination && <DataTablePagination table={table} />}
        </div>
    );
}
