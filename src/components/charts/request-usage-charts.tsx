"use client"

import React, { useEffect, useState } from "react"
import { <PERSON>, <PERSON>hart, XAxis, ResponsiveContainer, YA<PERSON>s, CartesianGrid } from "recharts"
import { type ChartConfig, ChartContainer, ChartTooltip } from "@/components/ui/chart"
import { dashStatisticApi } from "@/api/dash/dash-api"
import type { OrganizationDailyBillResponse } from "@/api/dash/dash-model"
import { useAtomValue } from "jotai"
import { organizationState } from "@/state/user-state"
import { Loading } from "../ui/loading"
import { Info } from "lucide-react"

// Simplified initialChartConfig for requests, can be expanded if needed
const initialChartConfig = {
    // Colors can be dynamically assigned or predefined if models are known
} satisfies ChartConfig

interface TransformedData {
    date: string;
    [modelName: string]: number | string; // modelName will store total requests for that model on that date
}

const colors = [ // Re-using color palette
    "oklch(0.398 0.07 227.392)",
    "oklch(0.828 0.189 84.429)",
    "oklch(0.769 0.188 70.08)",
    "oklch(0.488 0.243 264.376)",
    "oklch(0.696 0.17 162.48)",
    "oklch(0.627 0.265 303.9)",
    "oklch(0.645 0.246 16.439)",
    "oklch(0.577 0.245 27.325)",
    "oklch(0.704 0.191 22.216)",
    "oklch(0.725 0.255 264.5)",
];

interface RequestUsageChartsProps {
    selectedYear: number;
    selectedMonth: number;
}

export default function RequestUsageCharts({ selectedYear, selectedMonth }: RequestUsageChartsProps) {
    const [chartData, setChartData] = useState<TransformedData[]>([]);
    const [chartConfig, setChartConfig] = useState<ChartConfig>(initialChartConfig);
    const [modelKeys, setModelKeys] = useState<string[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const organization = useAtomValue(organizationState)

    useEffect(() => {
        const orgId = organization?.organizationId;

        // Show loading when organization ID is not available instead of error
        if (!orgId) {
            setLoading(true);
            setError(null);
            setChartData([]);
            return;
        }

        if (!selectedYear || !selectedMonth) {
            setError("Invalid year or month, unable to load chart data.");
            setLoading(false);
            setChartData([]);
            return;
        }

        const fetchData = async () => {
            try {
                setLoading(true);
                setError(null);
                const response = await dashStatisticApi.getDashStatistic({
                    selectedYear,
                    selectedMonth,
                    organizationId: orgId,
                });

                const transformedData: TransformedData[] = [];
                const newModelKeys = new Set<string>();
                const newChartConfig: ChartConfig = { ...initialChartConfig };
                let colorIndex = 0;

                Object.keys(response).forEach(dateTimestampKey => {
                    const dailyData: TransformedData = { date: dateTimestampKey };
                    response[dateTimestampKey].forEach((bill: OrganizationDailyBillResponse) => {
                        const requestCount = Number(bill.request || 0);
                        if (requestCount > 0) {
                            dailyData[bill.modelName] = (Number(dailyData[bill.modelName] || 0)) + requestCount;
                            if (!newModelKeys.has(bill.modelName)) {
                                newModelKeys.add(bill.modelName);
                                if (!newChartConfig[bill.modelName]) {
                                    newChartConfig[bill.modelName] = {
                                        label: bill.modelName, // Label will be modelName
                                        color: colors[colorIndex % colors.length],
                                    };
                                    colorIndex++;
                                }
                            }
                        }
                    });
                    transformedData.push(dailyData); // Keep all dates for X-axis consistency
                });

                transformedData.sort((a, b) => Number(a.date) - Number(b.date));

                setChartData(transformedData);
                setModelKeys(Array.from(newModelKeys));
                setChartConfig(newChartConfig);

            } catch (err) {
                setError(err instanceof Error ? err.message : "An unknown error occurred while loading chart data");
                console.error("Failed to fetch chart data for requests:", err);
                setChartData([]);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [organization, selectedYear, selectedMonth]);

    if (loading) {
        return <Loading className="h-full" />;
    }

    if (error) {
        return <div className="flex justify-center items-center h-full text-red-500">Error: {error}</div>;
    }

    if (chartData.length === 0 || modelKeys.length === 0) {
        return (
            <div className="flex flex-col justify-center items-center h-full text-zinc-500 text-sm gap-2">
                <Info className="w-4 h-4" />
                <span className="text-black text-xs">No data available</span>
                <span className="text-zinc-500 text-xs tracking-wider">Please check if models are enabled</span>
            </div>
        );
    }

    return (
        <div className="h-full w-full flex flex-col p-2">
            <div className="w-full h-[600px] mt-12 flex justify-center px-8">
                <ChartContainer className="h-full w-full" config={chartConfig}>
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 20 }}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                                dataKey="date"
                                tickLine={false}
                                tickMargin={10}
                                axisLine={false}
                                interval={2} // Adjust interval as needed or use dynamic ticks
                                tickFormatter={(value) => {
                                    const numericTimestamp = Number(value);
                                    if (isNaN(numericTimestamp)) return String(value);
                                    const dateObj = new Date(numericTimestamp);
                                    if (isNaN(dateObj.getTime())) return String(value);
                                    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                                    const day = dateObj.getDate().toString().padStart(2, '0');
                                    return `${month}-${day}`;
                                }}
                            />
                            <YAxis
                                tickLine={false}
                                axisLine={false}
                                stroke="#64748b"
                                fontSize={12}
                                tickFormatter={(value) => Number(value).toLocaleString()} // Format for request counts
                                width={80}
                            />
                            {modelKeys.map((modelKey, index) => (
                                <Bar
                                    key={modelKey}
                                    dataKey={modelKey}
                                    stackId="a" // Stack bars per day
                                    fill={chartConfig[modelKey]?.color || colors[index % colors.length]}
                                    radius={
                                        modelKeys.length === 1 ? [4, 4, 0, 0] :
                                            index === 0 ? [0, 0, 4, 4] :
                                                index === modelKeys.length - 1 ? [4, 4, 0, 0] :
                                                    [0, 0, 0, 0]
                                    }
                                />
                            ))}
                            <ChartTooltip
                                content={({ active, payload, label }) => {
                                    if (!active || !payload || payload.length === 0) return null;

                                    const validData = payload
                                        .filter(item => item.value && Number(item.value) > 0) // Filter out zero values
                                        .sort((a, b) => Number(b.value) - Number(a.value));

                                    if (validData.length === 0) return null; // Prevent empty tooltip

                                    const total = validData.reduce((sum, item) => sum + Number(item.value || 0), 0);

                                    return (
                                        <div className="w-[300px] bg-background/95 backdrop-blur-sm border border-border/50 shadow-lg rounded-lg p-3">
                                            <div className="mb-2 flex basis-full items-center border-b border-border/50 pb-2 text-xs font-medium text-foreground">
                                                Date
                                                <div className="ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground">
                                                    {(() => {
                                                        const numericTimestamp = Number(label);
                                                        if (isNaN(numericTimestamp)) return String(label);
                                                        const dateObj = new Date(numericTimestamp);
                                                        if (isNaN(dateObj.getTime())) return String(label);
                                                        const year = dateObj.getFullYear();
                                                        const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
                                                        const day = dateObj.getDate().toString().padStart(2, '0');
                                                        return `${year}-${month}-${day}`;
                                                    })()}
                                                </div>
                                            </div>

                                            {validData.map((item, index) => (
                                                <div key={`${item.dataKey}-${index}`} className="flex items-center gap-2 py-1">
                                                    <div
                                                        className="h-2.5 w-2.5 shrink-0 rounded-[2px]"
                                                        style={{ backgroundColor: item.color } as React.CSSProperties}
                                                    />
                                                    <span className="flex-1 text-xs text-foreground">
                                                        {chartConfig[item.dataKey as keyof typeof chartConfig]?.label || item.dataKey}
                                                    </span>
                                                    <div className="ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground text-xs">
                                                        {Number(item.value).toLocaleString()}
                                                    </div>
                                                </div>
                                            ))}

                                            {validData.length > 0 && (
                                                <div className="mt-2 flex basis-full items-center border-t border-border/50 pt-2 text-xs font-medium text-foreground">
                                                    <span className="font-semibold">Total Requests</span>
                                                    <div className="ml-auto flex items-baseline gap-0.5 font-mono font-medium tabular-nums text-foreground">
                                                        {total.toLocaleString()}
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    );
                                }}
                                cursor={false}
                            />
                        </BarChart>
                    </ResponsiveContainer>
                </ChartContainer>
            </div>
        </div>
    )
} 