import { cn } from "@/lib/utils";

interface LoadingProps {
    className?: string;
    error?: string;
}

export function Loading({ className, error }: LoadingProps) {
    const defaultHeightClass = className?.includes('h-') ? '' : 'min-h-[80vh]';

    if (error) {
        return (
            <div className={cn("flex items-center justify-center", defaultHeightClass, className)}>
                <p className="text-gray-500">{error}</p>
            </div>
        )
    }

    return (
        <div className={cn("flex items-center justify-center", defaultHeightClass, className)}>
            <div className="animate-pulse flex space-x-2">
                <div className="w-2 h-2 bg-zinc-300 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-zinc-300 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-zinc-300 rounded-full animate-bounce"></div>
            </div>
        </div>
    )
} 