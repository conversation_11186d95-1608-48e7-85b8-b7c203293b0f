import React, { memo } from "react";
import { useLocation } from "react-router";
import {
    Breadcrumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";

// Path segment to English name mapping table
const PATH_TO_CHINESE_NAME: Record<string, string> = {
    // Common path mappings
    "": "Home",
    "home": "Home",
    "dashboard": "Dashboard",
    "organization": "Organization",
    "model": "Model",
    "secret": "Secret",
    "subscribe": "Subscribe",
    "wallet": "Wallet",
    "team": "Team",
};

function RawAppBreadcrumb() {
    const { pathname } = useLocation();

    // Process path to generate breadcrumb navigation
    const generateBreadcrumbs = () => {
        // Remove first slash and split path
        const pathSegments = pathname.replace(/^\//, '').split('/').filter(Boolean);

        // If path is empty, show home page
        if (pathSegments.length === 0) {
            return (
                <BreadcrumbItem>
                    <BreadcrumbPage>{PATH_TO_CHINESE_NAME[""]}</BreadcrumbPage>
                </BreadcrumbItem>
            );
        }

        return pathSegments.map((segment: string, index: number) => {
            // Build complete path for current segment
            const path = '/' + pathSegments.slice(0, index + 1).join('/');

            // Get English name, if not found in mapping table use formatted segment name
            const formattedSegment = PATH_TO_CHINESE_NAME[segment] ||
                (segment.charAt(0).toUpperCase() + segment.slice(1));

            // Last segment uses BreadcrumbPage
            if (index === pathSegments.length - 1) {
                return (
                    <BreadcrumbItem key={path}>
                        <BreadcrumbPage className="!text-zinc-500">{formattedSegment}</BreadcrumbPage>
                    </BreadcrumbItem>
                );
            }

            // Other segments use BreadcrumbLink
            return (
                <React.Fragment key={path}>
                    <BreadcrumbItem>
                        <BreadcrumbLink href={path}>{formattedSegment}</BreadcrumbLink>
                    </BreadcrumbItem>
                    <BreadcrumbSeparator />
                </React.Fragment>
            );
        });
    };

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {generateBreadcrumbs()}
            </BreadcrumbList>
        </Breadcrumb>
    );
}


export const AppBreadcrumb = memo(RawAppBreadcrumb)