"use client"

import {
    <PERSON>ge<PERSON><PERSON><PERSON>,
    <PERSON>,
    El<PERSON>sis,
    LogOut,
    <PERSON>rk<PERSON>,
} from "lucide-react"

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    useSidebar,
} from "@/components/ui/sidebar"
import { useAtomValue } from "jotai"
import { userState } from "@/state/user-state"
import { useAuth0 } from "@auth0/auth0-react"
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar"

export function NavUser() {
    const { logout } = useAuth0();
    const { isMobile } = useSidebar()

    const user = useAtomValue(userState);

    return (
        <SidebarMenu>
            <SidebarMenuItem>
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <SidebarMenuButton
                            size="lg"
                            className="data-[state=open]:bg-zinc-100 data-[state=open]:text-zinc-900"
                        >
                            <div className="flex size-6 items-center justify-center ">
                                <Avatar className="h-6 w-6">
                                    <AvatarImage src={user?.avatar} />
                                    <AvatarFallback>
                                        <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders-base rounded-full h-5 w-5 bg-white border border-zinc-200 shadow-zinc-200 shadow-xl">
                                            <span className="aspect-square object-cover object-center rounded-full size-4 bg-zinc-100 text-black/80 pointer-events-none flex select-none items-center justify-center text-xs">
                                                T
                                            </span>
                                        </span>
                                    </AvatarFallback>
                                </Avatar>
                            </div>
                            <div className="grid flex-1 text-left text-sm leading-tight">
                                <span className="truncate font-medium text-[12px]">{user?.name}</span>
                            </div>
                            <Ellipsis className="ml-auto size-4 text-zinc-500" />
                        </SidebarMenuButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                        className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                        side={isMobile ? "bottom" : "top"}
                        align="end"
                        sideOffset={4}
                    >
                        <DropdownMenuLabel className="p-0 font-normal">
                            <div className="flex items-center gap-4 px-1 py-1.5 text-left text-sm">
                                <Avatar className="h-6 w-6">
                                    <AvatarImage src={user?.avatar} />
                                    <AvatarFallback>
                                        <span className="flex shrink-0 items-center justify-center overflow-hidden shadow-borders-base rounded-full h-5 w-5 bg-white border border-zinc-200 shadow-zinc-200 shadow-xl">
                                            <span className="aspect-square object-cover object-center rounded-full size-4 bg-zinc-100 text-black/80 pointer-events-none flex select-none items-center justify-center text-xs">
                                                T
                                            </span>
                                        </span>
                                    </AvatarFallback>
                                </Avatar>
                                <div className="grid flex-1 text-left text-sm leading-tight">
                                    <span className="truncate font-medium text-zinc-900">{user?.name}</span>
                                    <span className="truncate text-xs text-zinc-500">{user?.email}</span>
                                </div>
                            </div>
                        </DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem>
                                <Sparkles />
                                Upgrade to Pro
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuGroup>
                            <DropdownMenuItem >
                                <BadgeCheck />
                                Account
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                                <Bell />
                                Notifications
                            </DropdownMenuItem>
                        </DropdownMenuGroup>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                            onClick={() =>
                                logout({ logoutParams: { returnTo: window.location.origin } })
                            }
                        >
                            <LogOut />
                            Logout
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            </SidebarMenuItem>
        </SidebarMenu>
    )
}
