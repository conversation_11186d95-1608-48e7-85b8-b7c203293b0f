import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { type ReactNode, useEffect } from "react";
import { Loading } from "../ui/loading";
import { organizationState, userState } from "@/state/user-state";
import { useAuth0 } from "@auth0/auth0-react";
import { useLocation } from "react-router-dom";
import { organizationApi } from "@/api/organization/organization-api";
import { useRequest } from "ahooks";
import { pushModal } from "../modals";

interface AuthProviderProps {
    children: ReactNode;
}

export default function AuthProvider({ children }: AuthProviderProps) {
    const location = useLocation();
    const setUser = useSetAtom(userState);

    const setOrganization = useSetAtom(organizationState);
    const { user, isAuthenticated, isLoading, error, loginWithRedirect } = useAuth0();

    const { runAsync: currentOrganization } = useRequest(organizationApi.currentOrganization, {
        manual: true,
        onSuccess: (data) => {
            if (data) {
                setOrganization(data);
            } else {
                pushModal("CreateOrganizationModal");
            }
        },
    });

    useEffect(() => {
        if (isLoading) {
            return;
        }

        if (error) {
            console.error("Auth0 error:", error);
            if (!location.pathname.includes('/login')) {
                loginWithRedirect({
                    appState: { returnTo: location.pathname }
                });
            }
            return;
        }

        if (isAuthenticated && user) {
            setUser({
                id: user.sub || "", // Use sub as id
                name: user.name || "",
                email: user.email || "",
                avatar: user.picture || "",
            });

            console.log("user", user);

            currentOrganization();

        } else if (!isAuthenticated && !location.pathname.includes('/login')) {
            loginWithRedirect({
                appState: { returnTo: location.pathname }
            });
        }
    }, [isLoading, isAuthenticated, user, error, setUser, loginWithRedirect, location.pathname]);

    if (isLoading) {
        return <Loading />;
    }

    if (error) {
        return <Loading />;
    }

    if (!isAuthenticated && !location.pathname.includes('/login')) {
        return <Loading />;
    }

    return <>{children}</>;
}