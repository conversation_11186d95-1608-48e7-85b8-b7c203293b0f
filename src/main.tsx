import { StrictMode, useEffect } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'
import { ModalProvider } from './components/modals/index.ts'
import { Toaster } from "@/components/ui/sonner"
import { Auth0Provider, useAuth0 } from '@auth0/auth0-react'
import { setAuthTokenProvider } from './lib/apiClient.ts'

// Helper component to set up the token provider
const TokenProviderSetup = () => {
  const { getAccessTokenSilently, isAuthenticated } = useAuth0();

  useEffect(() => {
    // We only want to set the provider once, and ideally when authenticated,
    // but getAccessTokenSilently handles unauthenticated state by throwing an error.
    // apiClient is designed to call this provider on each request.
    setAuthTokenProvider(getAccessTokenSilently);
  }, [getAccessTokenSilently, isAuthenticated]); // Rerun if auth state changes, though provider is set once

  return null; // This component does not render anything
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <Auth0Provider
      domain={import.meta.env.VITE_AUTH0_DOMAIN}
      clientId={import.meta.env.VITE_AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: import.meta.env.VITE_AUTH0_REDIRECT_URI,
        scope: "openid profile email read:messages write:messages"
      }}
      cacheLocation="localstorage" // Enable local storage cache to maintain authentication state
      useRefreshTokens={true} // Enable refresh tokens
    >
      <TokenProviderSetup /> {/* Add the helper component here */}
      <ModalProvider />
      <Toaster position='top-right' richColors />
      <App />
    </Auth0Provider>
  </StrictMode>
)
