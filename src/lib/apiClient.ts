import axios, { AxiosError, type AxiosRequestConfig, type AxiosResponse } from 'axios';

let tokenProvider: (() => Promise<string>) | null = null;

export function setAuthTokenProvider(provider: () => Promise<string>) {
    tokenProvider = provider;
}

// Create axios instance
const axiosInstance = axios.create({
    timeout: 500000,
    baseURL: import.meta.env.VITE_API_BASE_URL,
    headers: { 'Content-Type': 'application/json;charset=utf-8' },
    // withCredentials: true, // Remove cookie support, use Bearer token instead
});


// Utility functions
function isServerSide(): boolean {
    return typeof window === 'undefined';
}

function handleAuthError(): void {
    // Cookie clearing operations are handled by the backend
    // TODO: Review if this is still the best way to handle auth errors with Auth0
    window.location.href = import.meta.env.VITE_LOGIN_URL || '/login';
}

// Request interceptor
axiosInstance.interceptors.request.use(
    async (config) => {
        if (tokenProvider) {
            try {
                //@ts-ignore
                const token = await tokenProvider({
                    scope: "openid profile email read:messages read:messages"
                });
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
            } catch (error) {
                console.error("Error getting access token for API request:", error);
                // Optionally, you could reject the request here or let it proceed without a token
                // For now, let it proceed, Auth0 might handle redirects or the API will return 401
            }
        }
        return config;
    },
    (error) => {
        // Do something with request error
        return Promise.reject(error);
    },
);

// Response interceptor
axiosInstance.interceptors.response.use((res) => {
    if (res.data instanceof Blob) {
        return res;
    }
    // Check response content type
    const contentType = res.headers['content-type'];
    if (contentType && contentType.includes('application/octet-stream')) {
        // If it's binary stream data, return the entire response object directly
        return res;
    }
    if (res.data.success) {
        return res.data;
    } else {
        throw new Error(res.data.message);
    }
    // For other types of responses, return the data part
    // return res.data;
},
    (error: AxiosError<any>) => {
        if (error.response?.status === 401) {
            handleAuthError();
            return Promise.reject(new Error('Login expired, please login again'));
        }
        const { response, message } = error || {};

        let errMsg = '';
        try {
            errMsg = response?.data?.msg || message;
        } catch (error) {
            throw new Error(error as unknown as string);
        }
        if (errMsg === '') {
            errMsg = "system error";
        }
        // console.error(errMsg);
        return Promise.reject(new Error(errMsg));
    },
);

class APIClient {
    private makeRequest<T = any>(config: AxiosRequestConfig): Promise<T> {
        if (isServerSide()) {
            return Promise.resolve({} as T);
        }
        return axiosInstance.request<any, AxiosResponse<T>>(config).then(res => res.data);
    }

    get<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'GET' });
    }

    post<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'POST' });
    }

    put<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'PUT' });
    }

    delete<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'DELETE' });
    }

    patch<T = any>(config: AxiosRequestConfig): Promise<T> {
        return this.makeRequest({ ...config, method: 'PATCH' });
    }

    // Convenience methods: requests with URL and data
    getByUrl<T = any>(url: string, params?: any): Promise<T> {
        return this.get({ url, params });
    }

    postByUrl<T = any>(url: string, data?: any): Promise<T> {
        return this.post({ url, data });
    }

    putByUrl<T = any>(url: string, data?: any): Promise<T> {
        return this.put({ url, data });
    }

    deleteByUrl<T = any>(url: string): Promise<T> {
        return this.delete({ url });
    }

    patchByUrl<T = any>(url: string, data?: any): Promise<T> {
        return this.patch({ url, data });
    }
}

const apiClient = new APIClient();
export { apiClient };


