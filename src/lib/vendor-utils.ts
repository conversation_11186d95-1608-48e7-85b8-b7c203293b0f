import type { VendorOption } from "@/components/table/toolbar/vendor-filter"
import type { ModelPriceInfoResponse } from "@/api/system/system-model-model"

/**
 * 将模型数据转换为厂商过滤选项
 */
export function transformModelsToVendorOptions(models: ModelPriceInfoResponse[]): VendorOption[] {
    const vendorOptions: VendorOption[] = []

    models.forEach(model => {
        // 为每个厂商名称创建选项
        model.vendorNames.forEach(vendorName => {
            vendorOptions.push({
                label: `${model.modelName} (${vendorName})`,
                value: `${model.modelName}-${vendorName}`,
                manufacturerName: model.manufacturerName
            })
        })
    })

    return vendorOptions
}

/**
 * 按厂商分组数据并排序
 */
export function groupDataByManufacturer<T extends { manufacturerName: string }>(
    data: T[]
): Array<{ manufacturer: string; items: T[] }> {
    const grouped = data.reduce((acc, item) => {
        const manufacturer = item.manufacturerName
        if (!acc[manufacturer]) {
            acc[manufacturer] = []
        }
        acc[manufacturer].push(item)
        return acc
    }, {} as Record<string, T[]>)

    // 对厂商名称排序，对每个厂商下的项目也按名称排序
    return Object.keys(grouped)
        .sort()
        .map(manufacturer => ({
            manufacturer,
            items: grouped[manufacturer].sort((a, b) => {
                // 如果有 modelName 属性，按 modelName 排序
                if ('modelName' in a && 'modelName' in b) {
                    return (a as any).modelName.localeCompare((b as any).modelName)
                }
                // 否则按 manufacturerName 排序
                return a.manufacturerName.localeCompare(b.manufacturerName)
            })
        }))
}

/**
 * 根据选中的厂商过滤器过滤数据
 */
export function filterBySelectedVendors<T extends { manufacturerName: string; vendorNames?: string[] }>(
    data: T[],
    selectedVendorValues: string[]
): T[] {
    if (!selectedVendorValues.length) {
        return data
    }

    return data.filter(item => {
        // 检查是否有匹配的厂商-模型组合
        if (item.vendorNames) {
            return item.vendorNames.some(vendorName => {
                const modelVendorKey = `${(item as any).modelName}-${vendorName}`
                return selectedVendorValues.includes(modelVendorKey)
            })
        }

        // 如果没有 vendorNames，检查 manufacturerName
        return selectedVendorValues.some(value => value.includes(item.manufacturerName))
    })
}

/**
 * 获取所有唯一的厂商名称
 */
export function getUniqueManufacturers<T extends { manufacturerName: string }>(data: T[]): string[] {
    const manufacturers = new Set(data.map(item => item.manufacturerName))
    return Array.from(manufacturers).sort()
}

/**
 * 创建简单的厂商过滤选项（只按厂商名称分组）
 */
export function createSimpleVendorOptions<T extends { manufacturerName: string }>(
    data: T[]
): VendorOption[] {
    const manufacturers = getUniqueManufacturers(data)

    return manufacturers.map(manufacturer => ({
        label: manufacturer,
        value: manufacturer,
        manufacturerName: manufacturer
    }))
} 