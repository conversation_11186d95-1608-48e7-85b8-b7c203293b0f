import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON><PERSON>, ArrowLeft, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function PaymentCancelPage() {
    const navigate = useNavigate();

    useEffect(() => {
        // Show cancel message
        toast.info("Payment was cancelled. No charges were made.");
    }, []);

    const handleBackToWallet = () => {
        navigate('/dashboard/wallet');
    };

    const handleTryAgain = () => {
        navigate('/dashboard/wallet');
    };

    return (
        <div className="min-h-screen bg-zinc-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-8 max-w-md w-full text-center space-y-6">
                <div className="w-12 h-12 bg-zinc-100 rounded-lg flex items-center justify-center mx-auto">
                    <XCircle className="w-6 h-6 text-zinc-700" />
                </div>

                <div className="space-y-2">
                    <h1 className="text-xl font-semibold text-zinc-900">Payment Cancelled</h1>
                    <p className="text-sm text-zinc-600">
                        Your payment was cancelled and no charges were made to your account. You can try again anytime.
                    </p>
                </div>

                <div className="space-y-3">
                    <Button
                        onClick={handleTryAgain}
                        className="w-full bg-zinc-900 hover:bg-zinc-800 text-white"
                    >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Try Again
                    </Button>

                    <Button
                        onClick={handleBackToWallet}
                        variant="outline"
                        className="w-full border-zinc-200 text-zinc-700 hover:bg-zinc-50"
                    >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Wallet
                    </Button>
                </div>
            </div>
        </div>
    );
}
