import { useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { CheckCircle, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function PaymentSuccessPage() {
    const navigate = useNavigate();
    const [searchParams] = useSearchParams();
    const sessionId = searchParams.get('session_id');

    useEffect(() => {
        // Show success message
        toast.success("Payment completed successfully! Your balance will be updated shortly.");
    }, []);

    const handleBackToWallet = () => {
        navigate('/dashboard/wallet');
    };

    return (
        <div className="min-h-screen bg-zinc-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-sm border border-zinc-200 p-8 max-w-md w-full text-center space-y-6">
                <div className="w-12 h-12 bg-zinc-100 rounded-lg flex items-center justify-center mx-auto">
                    <CheckCircle className="w-6 h-6 text-zinc-700" />
                </div>

                <div className="space-y-2">
                    <h1 className="text-xl font-semibold text-zinc-900">Payment Successful</h1>
                    <p className="text-sm text-zinc-600">
                        Your payment has been processed successfully. Your wallet balance will be updated within a few minutes.
                    </p>
                </div>

                {sessionId && (
                    <div className="bg-zinc-50 rounded-lg p-4 text-left">
                        <p className="text-xs text-zinc-500 mb-1">Session ID:</p>
                        <p className="text-xs font-mono text-zinc-700 break-all">{sessionId}</p>
                    </div>
                )}

                <Button
                    onClick={handleBackToWallet}
                    className="w-full bg-zinc-900 hover:bg-zinc-800 text-white"
                >
                    <ArrowLeft className="w-4 h-4 mr-2" />
                    Back to Wallet
                </Button>
            </div>
        </div>
    );
}
