import { useAuth0 } from "@auth0/auth0-react";
import { Loading } from "@/components/ui/loading";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export default function AuthCallbackPage() {
    const { handleRedirectCallback, isLoading, error } = useAuth0();
    const navigate = useNavigate();

    useEffect(() => {
        const handleCallback = async () => {
            try {
                const result = await handleRedirectCallback();
                // Get the original page to access, default to home page if none
                const returnTo = result?.appState?.returnTo || '/dashboard/home';
                navigate(returnTo, { replace: true });
            } catch (error) {
                console.error('Auth callback error:', error);
                // Redirect to login page when error occurs
                navigate('/login', { replace: true });
            }
        };

        if (!isLoading) {
            handleCallback();
        }
    }, [handleRedirectCallback, isLoading, navigate]);

    if (error) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <div className="text-center">
                    <h1 className="text-xl font-semibold text-red-600 mb-2">Authentication Failed</h1>
                    <p className="text-gray-600 mb-4">An error occurred during login, please try again.</p>
                    <button
                        onClick={() => navigate('/login', { replace: true })}
                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                        Back to Login
                    </button>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center">
            <div className="text-center">
                <Loading />
                <p className="mt-4 text-gray-600">Processing login...</p>
            </div>
        </div>
    );
} 