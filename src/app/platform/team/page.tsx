import { OrganizationBindApi } from "@/api/organization-bind/organization-bind-model";
import { pushModal } from "@/components/modals";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { CopyButton } from "@/components/ui/button/copy-button";
import CommonButton from "@/components/ui/button/new-button";
import { Loading } from "@/components/ui/loading";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { organizationState } from "@/state/user-state";
import { useRequest } from "ahooks";
import { useAtomValue } from "jotai";
import { Crown, PlusIcon, Trash2, User, UserPlus } from "lucide-react";
import { useEffect } from "react";
import { toast } from "sonner";


export default function TeamPage() {

    const organization = useAtomValue(organizationState)

    const { data, loading, runAsync: refreshAsync } = useRequest(OrganizationBindApi.getOrganizationBind, {
        manual: true,
    })

    useEffect(() => {
        if (organization) {
            refreshAsync(organization.organizationId)
        }
    }, [organization])


    const { runAsync: updateAuthority } = useRequest(OrganizationBindApi.updateOrganizationBind, {
        manual: true,
        onSuccess: async () => {
            toast.success('Updated successfully')
            await refreshAsync(organization?.organizationId!!)
        },
        onError: async (e) => {
            toast.error('Update failed: ' + e.message)
            await refreshAsync(organization?.organizationId!!)
        }
    })


    if (loading) {
        return <Loading className="min-h-[80vh]" />
    }



    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
            <div className="flex flex-col gap-2 h-full">
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    {/* left */}
                    <div>
                        <h1 className="text-[18px]">Team</h1>
                        <h2 className="text-[13px] text-zinc-500">
                            <span className="text-zinc-500 text-[13px]">
                                Manage team members, permissions and team settings
                            </span>
                        </h2>
                    </div>
                    {/* right */}
                    <div className="pr-4">
                        <CommonButton onClick={() => {
                            pushModal('AddTeamMemberModal', {
                                onSuccess: async () => {
                                    await refreshAsync(organization?.organizationId!!)
                                }
                            })
                        }}>
                            <PlusIcon className="w-4 h-4" />
                            Add Member
                        </CommonButton>
                    </div>
                </header>
                <Separator />
                {data && data.length > 0 ? (
                    <>
                        <div className="divide-y">
                            {data.map((item, index) => (
                                <div key={index} className="flex items-center justify-between p-4  transition-colors">
                                    <div className="flex items-center">
                                        <Avatar className="h-8 w-8 ring-1 ring-border ring-inset">
                                            {/* <AvatarImage src={item} /> */}
                                            <AvatarFallback>{item.email.charAt(0).toUpperCase()}</AvatarFallback>
                                        </Avatar>
                                        <div className="ml-4 flex flex-row items-center gap-2">
                                            <p className="font-medium text-zinc-600">{item.email}</p>
                                            <CopyButton size='xs' text={item.email} className="text-xs text-muted-foreground" />
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3 ml-4">
                                        <Select
                                            value={item.permission.toString()}
                                            onValueChange={(value) => {
                                                const newPermission = parseInt(value)
                                                if (parseInt(item.permission) !== newPermission) {
                                                    updateAuthority(organization!.organizationId, item.email, newPermission)
                                                }
                                            }}
                                        >
                                            <SelectTrigger className="w-[120px] h-8 border-gray-200 text-xs">
                                                <SelectValue placeholder="Role" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="1" className="text-xs">
                                                    <div className="flex items-center gap-2">
                                                        <Crown className="h-3 w-3" />
                                                        Owner
                                                    </div>
                                                </SelectItem>
                                                <SelectItem value="0" className="text-xs">
                                                    <div className="flex items-center gap-2">
                                                        <User className="h-3 w-3" />
                                                        Member
                                                    </div>
                                                </SelectItem>
                                            </SelectContent>
                                        </Select>

                                        <Button
                                            onClick={() => {
                                                pushModal('DoubleCheckModal', {
                                                    title: 'Remove Member',
                                                    description: `Are you sure you want to remove ${item.email}? This action cannot be undone.`,
                                                    onConfirm: () => {
                                                        toast.promise(async () => {
                                                            await OrganizationBindApi.deleteOrganizationBind(organization!.organizationId, item.email)
                                                            await refreshAsync(organization?.organizationId!!)
                                                        }, {
                                                            loading: 'Removing...',
                                                            success: 'Member removed successfully',
                                                            error: (e) => 'Failed to remove: ' + e.message
                                                        })
                                                    },
                                                    onClose: () => {
                                                        // Close modal
                                                    }
                                                })
                                            }}
                                            variant="ghost"
                                            size="sm"
                                            className="h-8 w-8 p-0 text-gray-400 hover:text-red-600 hover:bg-red-50"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </>
                ) : (
                    <div className="flex flex-col items-center justify-center py-16 px-4">
                        <div className="bg-primary/5 p-4 rounded-full mb-4">
                            <UserPlus className="h-6 w-6 text-zinc-500" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">No team members yet</h3>
                        <p className="text-muted-foreground text-center mb-6 max-w-sm text-sm">
                            Start building your team by adding members to collaborate.
                        </p>
                    </div>
                )}
            </div>
        </div>
    )
}