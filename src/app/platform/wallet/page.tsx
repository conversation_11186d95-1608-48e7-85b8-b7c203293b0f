import { useAtomValue, useSet<PERSON><PERSON> } from "jotai";
import { toast } from "sonner";
import { InfoIcon, Arrow<PERSON>ef<PERSON> } from "lucide-react";

import CommonButton from "@/components/ui/button/new-button";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { BillingTable } from "@/components/wallet/billing-table";

import { organizationState } from "@/state/user-state";
import { refreshTableAtom } from "@/state/table-state";
import { pushModal } from "@/components/modals";

export default function WalletPage() {
    const organization = useAtomValue(organizationState);
    const setRefreshTable = useSetAtom(refreshTableAtom);

    const handleOpenAutoRechargeModal = () => {
        pushModal("AutoRechargeModal", {
            onClose: () => {
                // Modal closed
            },
            currentSettings: {
                minimumAmount: Number(organization?.balanceAmount) || 0,
                rechargeAmount: Number(organization?.balanceAmount) || 0
            },
            onSuccess: () => {
                // refreshBalance();
            }
        });
    };

    const handleOpenRechargeModal = () => {
        pushModal("RechargeModal", {
            onClose: () => {
                // Modal closed
            },
            onSuccess: () => {
                // Refresh transaction record data after successful payment
                setRefreshTable(prev => prev + 1);
                toast.success("Payment completed! Your balance will be updated shortly.");
            }
        });
    };

    const handleOpenRefundModal = () => {
        pushModal("RefundModal", {
            onClose: () => {
                // Modal closed
            },
            onSuccess: () => {
                // Refresh transaction record data after successful refund
                setRefreshTable(prev => prev + 1);
            }
        });
    };

    const formatAmount = (amount: number) => {
        return (amount).toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    };

    return (
        <div className="rounded-lg shadow-borders-base h-full w-auto px-24" >
            <div className="flex flex-col gap-4 h-full">
                <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                    <div>
                        <h1 className="text-[18px]">Wallet</h1>
                        <h2 className="text-[13px] text-zinc-500">
                            Manage wallet balance, transaction records and recharge
                        </h2>
                    </div>
                </header>
                <Separator />

                <div className="px-4 pb-4 space-y-6 flex-1 overflow-auto">
                    {/* Balance and recharge area */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Current balance */}
                        <div className="space-y-3">
                            <div>
                                <h3 className="text-lg font-semibold">Current balance</h3>
                                <p className="text-sm text-zinc-500">Your account available balance</p>
                            </div>
                            <div>
                                <div className="text-2xl font-bold text-zinc-900">
                                    {"$" + formatAmount(Number(organization?.balanceAmount) || 0)}
                                </div>
                            </div>
                            <div className="flex gap-3">
                                <CommonButton
                                    onClick={handleOpenRechargeModal}
                                    className="w-auto"
                                    size="sm"
                                >
                                    Add payment
                                </CommonButton>
                                <CommonButton
                                    onClick={handleOpenRefundModal}
                                    className="w-auto bg-orange-600 hover:bg-orange-700 text-white border-0"
                                    size="sm"
                                    variant="outline"
                                >
                                    <ArrowLeft className="w-4 h-4 mr-1" />
                                    Request refund
                                </CommonButton>
                            </div>
                        </div>
                    </div>

                    {/* Auto recharge reminder */}
                    <div className="flex items-start gap-3 p-4 bg-zinc-50 border border-zinc-200 rounded-lg">
                        <InfoIcon className="h-5 w-5 text-zinc-600 mt-0.5 flex-shrink-0" />
                        <div className="flex-1">
                            <p className="text-sm text-zinc-700">
                                <span className="font-medium">Auto recharge settings</span>
                                <br />
                                When your balance reaches $0, your API requests will stop working. Enable auto recharge to keep your balance topped up.
                            </p>
                        </div>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={handleOpenAutoRechargeModal}
                            className="flex-shrink-0 bg-emerald-600 text-white border-emerald-600 hover:bg-emerald-700 hover:border-emerald-700 hover:text-white"
                        >
                            Set auto recharge
                        </Button>
                    </div>

                    {/* Billing History Table */}
                    <BillingTable />
                </div>
            </div>
        </div >
    );
}