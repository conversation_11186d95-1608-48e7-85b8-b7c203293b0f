import { useRequest } from "ahooks";
import { useMemo } from "react";
import { systemModelApi } from "@/api/system/system-model-api";
import type { ModelPriceInfoResponse } from "@/api/system/system-model-model";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Loading } from "@/components/ui/loading";
import { AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { ScrollArea } from "@/components/ui/scroll-area";
import { VendorUrlFilter } from "@/components/table/toolbar/vendor-filter";
import { transformModelsToVendorOptions, groupDataByManufacturer, filterBySelectedVendors } from "@/lib/vendor-utils";
import useSearchParamsManager from "@/hooks/use-url-param";
import {
    OpenAI,
    Claude,
    Gemini,
    Meta,
    Alibaba,
    Baidu,
    Tencent,
    ByteDance,
    Zhipu,
    Moonshot,
    DeepSeek,
    Minimax,
    HuggingFace,
    AzureAI,
    Volcengine,
    Grok,
    Mistral
} from '@lobehub/icons';

// Vendor logo mapping
const vendorLogos: Record<string, React.ComponentType<any>> = {
    'OpenAI': OpenAI,
    'Claude': Claude.Color,
    'Google': Gemini.Color,
    'Azure': AzureAI.Color,
    'Meta': Meta,
    "X": Grok,
    'Mistral': Mistral.Color,
    'Volcengine': Volcengine.Color,
    'Tencent': Tencent,
    'ByteDance': ByteDance,
    'Zhipu': Zhipu,
    'Moonshot': Moonshot,
    'DeepSeek': DeepSeek,
    'Minimax': Minimax,
    'HuggingFace': HuggingFace,
    // 添加一些常见的厂商名称映射
    'openai': OpenAI,
    'claude': Claude.Color,
    'google': Gemini.Color,
    'azure': AzureAI.Color,
    'meta': Meta,
    'alibaba': Alibaba,
    'baidu': Baidu,
    'tencent': Tencent,
    'bytedance': ByteDance,
    'zhipu': Zhipu,
    'moonshot': Moonshot,
    'deepseek': DeepSeek,
    'minimax': Minimax,
    'huggingface': HuggingFace,
};

// 价格分组配置
const priceGroups = [
    {
        title: "Text Pricing",
        // icon: MessageSquare,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
        prices: [
            { key: 'textPrompt', label: 'Text Prompt' },
            { key: 'textCompletion', label: 'Text Completion' },
            { key: 'textCachePrompt', label: 'Text Cache Prompt' },
        ]
    },
    {
        title: "Audio Pricing",
        // icon: Volume2,
        color: "text-green-600",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
        prices: [
            { key: 'audioPrompt', label: 'Audio Prompt' },
            { key: 'audioCompletion', label: 'Audio Completion' },
            { key: 'audioCachePrompt', label: 'Audio Cache Prompt' },
        ]
    },
    {
        title: "Image Pricing",
        // icon: Image,
        color: "text-purple-600",
        bgColor: "bg-purple-50",
        borderColor: "border-purple-200",
        prices: [
            { key: 'imagePrompt', label: 'Image Prompt' },
            { key: 'imageCompletion', label: 'Image Completion' },
            { key: 'imageCachePrompt', label: 'Image Cache Prompt' },
        ]
    },
    {
        title: "Advanced Features",
        // icon: Cpu,
        color: "text-orange-600",
        bgColor: "bg-orange-50",
        borderColor: "border-orange-200",
        prices: [
            { key: 'reasoningCompletion', label: 'Reasoning Completion' },
            { key: 'searchCall', label: 'Search Call' },
            { key: 'searchTool', label: 'Search Tool' },
        ]
    },
    {
        title: "Cache Duration",
        // icon: Clock,
        color: "text-indigo-600",
        bgColor: "bg-indigo-50",
        borderColor: "border-indigo-200",
        prices: [
            { key: 'textCachePromptWrite5m', label: '5min Cache Write' },
            { key: 'textCachePromptWrite1h', label: '1hr Cache Write' },
        ]
    }
];

export default function SystemModelPage() {
    const { data, loading, error } = useRequest(systemModelApi.getModelInfoAll);
    const { getParam } = useSearchParamsManager();

    // 获取厂商过滤器选项
    const vendorOptions = useMemo(() => {
        if (!data) return [];
        return transformModelsToVendorOptions(data);
    }, [data]);

    // 根据URL参数过滤数据
    const filteredData = useMemo(() => {
        if (!data) return [];

        const selectedVendors = getParam('vendor');
        if (!selectedVendors) return data;

        const selectedVendorValues = selectedVendors.split(',');
        return filterBySelectedVendors(data, selectedVendorValues);
    }, [data, getParam]);

    // 按厂商分组数据
    const groupedData = useMemo(() => {
        return groupDataByManufacturer(filteredData);
    }, [filteredData]);

    const getVendorLogo = (manufacturerName: string) => {
        const LogoComponent = vendorLogos[manufacturerName] || vendorLogos[manufacturerName.toLowerCase()];
        if (LogoComponent) {
            return <LogoComponent size={20} />;
        }
        return null;
    };

    const formatPrice = (price: string) => {
        if (!price || price === '0' || price === '-') {
            return <span className="text-gray-400">-</span>;
        }
        return <span className="text-xs text-zinc-900">${price}</span>;
    };

    const hasValidPrices = (group: typeof priceGroups[0], model: ModelPriceInfoResponse) => {
        return group.prices.some(({ key }) => {
            const price = (model.prices as any)[key];
            return price && price !== '0' && price !== '-';
        });
    };

    const getValidPriceGroups = (model: ModelPriceInfoResponse) => {
        return priceGroups.filter(group => hasValidPrices(group, model));
    };

    // 渲染厂商标签，支持悬停显示折叠的厂商
    const renderVendorBadges = (vendorNames: string[], maxVisible: number = 2) => {
        if (vendorNames.length <= maxVisible) {
            return (
                <div className="flex flex-wrap gap-1">
                    {vendorNames.map((vendor, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                            {vendor}
                        </Badge>
                    ))}
                </div>
            );
        }

        const visibleVendors = vendorNames.slice(0, maxVisible);
        const hiddenVendors = vendorNames.slice(maxVisible);
        const hiddenCount = hiddenVendors.length;

        return (
            <div className="flex flex-wrap gap-1">
                {visibleVendors.map((vendor, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                        {vendor}
                    </Badge>
                ))}
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <Badge variant="outline" className="text-xs cursor-help hover:bg-zinc-100">
                                +{hiddenCount}
                            </Badge>
                        </TooltipTrigger>
                        <TooltipContent side="top" className="max-w-xs">
                            <div className="space-y-1">
                                <p className="text-sm font-medium">All Vendors ({vendorNames.length}):</p>
                                <div className="flex flex-wrap gap-1">
                                    {vendorNames.map((vendor, index) => (
                                        <span
                                            key={index}
                                            className="inline-block bg-zinc-100 text-zinc-700 px-2 py-1 rounded text-xs"
                                        >
                                            {vendor}
                                        </span>
                                    ))}
                                </div>
                            </div>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            </div>
        );
    };

    const renderPriceGroup = (group: typeof priceGroups[0], model: ModelPriceInfoResponse) => {
        if (!hasValidPrices(group, model)) {
            return null;
        }

        const validPrices = group.prices.filter(({ key }) => {
            const price = (model.prices as any)[key];
            return price && price !== '0' && price !== '-';
        });

        return (
            <div className={`rounded-lg border ${group.borderColor} ${group.bgColor} p-3`}>
                <div className="flex items-center gap-2 mb-2">
                    <span className={`text-sm font-medium ${group.color}`}>{group.title}</span>
                </div>
                <div className="space-y-1">
                    {validPrices.map(({ key, label }) => (
                        <div key={key} className="flex justify-between items-center text-xs">
                            <span className="text-gray-600">{label}:</span>
                            {formatPrice((model.prices as any)[key])}
                        </div>
                    ))}
                </div>
            </div>
        );
    };

    const renderCompactModel = (model: ModelPriceInfoResponse, validGroups: typeof priceGroups) => {
        const group = validGroups[0];
        return (
            <div key={model.modelName} className="border border-zinc-200 rounded-lg bg-white p-4 space-y-3 md:col-span-1 lg:col-span-2">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <span className="font-medium text-base">{model.modelName}</span>
                        <Badge variant="outline" className="text-xs">
                            {model.modelType === 0 ? "LLM" : "Search"}
                        </Badge>
                    </div>
                    {renderVendorBadges(model.vendorNames, 2)}
                </div>
                {renderPriceGroup(group, model)}
            </div>
        );
    };

    const renderMediumModel = (model: ModelPriceInfoResponse, validGroups: typeof priceGroups) => {
        return (
            <div key={model.modelName} className="border border-zinc-200 rounded-lg bg-white p-4 space-y-3 md:col-span-2 lg:col-span-3">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                        <span className="font-medium text-base">{model.modelName}</span>
                        <Badge variant="outline" className="text-xs">
                            {model.modelType === 0 ? "LLM" : "Search"}
                        </Badge>
                    </div>
                    {renderVendorBadges(model.vendorNames, 2)}
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {validGroups.map((group) => renderPriceGroup(group, model))}
                </div>
            </div>
        );
    };

    const renderFullModel = (model: ModelPriceInfoResponse, validGroups: typeof priceGroups) => {
        return (
            <div key={model.modelName} className="border border-zinc-200 rounded-lg bg-white p-4 space-y-4 col-span-full">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <span className="font-medium text-lg">{model.modelName}</span>
                        <Badge variant="outline" className="text-xs">
                            {model.modelType === 0 ? "LLM" : "Search"}
                        </Badge>
                    </div>
                    {renderVendorBadges(model.vendorNames, 3)}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
                    {validGroups.map((group) => renderPriceGroup(group, model))}
                </div>
            </div>
        );
    };

    if (loading) {
        return <Loading />;
    }

    if (error) {
        return (
            <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                    Failed to load system model information: {error.message}
                </AlertDescription>
            </Alert>
        );
    }

    return (
        <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200">
            <div className="flex flex-col gap-2 h-full">
                <header className="px-4 py-2 space-y-1">
                    <h1 className="text-[18px]">System Model Management</h1>
                    <h2 className="text-[13px] text-zinc-500">
                        View and manage all available AI models and their detailed pricing information
                    </h2>
                </header>
                <Separator />
                <div className="px-4 py-2">
                    <div className="flex items-center gap-2">
                        <VendorUrlFilter
                            paramKey="vendor"
                            title="Vendor Filter"
                            options={vendorOptions}
                        />
                        {filteredData.length > 0 && (
                            <span className="text-sm text-zinc-500">
                                total {filteredData.length} models
                            </span>
                        )}
                    </div>
                </div>
                <Separator />
                <div className="flex-grow overflow-hidden">
                    <ScrollArea className="h-full">
                        <div className="p-4 space-y-6">
                            {groupedData.length === 0 ? (
                                <div className="text-center py-12">
                                    <p className="text-zinc-500">No models found</p>
                                </div>
                            ) : (
                                groupedData.map(({ manufacturer, items }) => (
                                    <div key={manufacturer} className="space-y-4">
                                        <div className="flex items-center gap-3 pb-2 border-b border-zinc-200">
                                            <div className="flex items-center gap-2">
                                                {getVendorLogo(manufacturer)}
                                                <h3 className="text-lg font-semibold text-gray-900">{manufacturer}</h3>
                                            </div>
                                            <Badge variant="secondary" className="text-xs">
                                                {items.length} models
                                            </Badge>
                                        </div>
                                        <div className="ml-6">
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
                                                {items.map((model) => {
                                                    const validGroups = getValidPriceGroups(model);

                                                    if (validGroups.length === 0) {
                                                        return null;
                                                    }

                                                    if (validGroups.length === 1) {
                                                        return renderCompactModel(model, validGroups);
                                                    }

                                                    if (validGroups.length === 2) {
                                                        return renderMediumModel(model, validGroups);
                                                    }

                                                    return renderFullModel(model, validGroups);
                                                })}
                                            </div>
                                        </div>
                                    </div>
                                ))
                            )}
                        </div>
                    </ScrollArea>
                </div>
            </div>
        </div>
    );
}
