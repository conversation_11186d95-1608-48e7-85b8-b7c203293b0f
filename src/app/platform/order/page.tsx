import { DataTable } from "@/components/table/base-table";
import CommonButton from "@/components/ui/button/new-button";
import { PlusIcon } from "lucide-react";

import { DataTableToolbar, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import { Separator } from "@/components/ui/separator";

export default function OrderPage() {

    return (
        <>
            <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200 py-2">
                <div className="flex flex-col gap-2 h-full">
                    <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                        {/* left */}
                        <div>
                            <h1 className="text-[18px]">Order</h1>
                            <h2 className="text-[13px] text-zinc-500">
                                <span className="text-zinc-500 text-[13px]">
                                    Manage orders
                                </span>
                            </h2>
                        </div>
                        {/* right */}
                        <div className="pr-4">
                            <CommonButton onClick={() => { }}>
                                <PlusIcon className="w-4 h-4" />
                                New Secret
                            </CommonButton>
                        </div>
                    </header>
                    <Separator />
                    <DataTable
                        columns={[]}
                        isNeedSelect={true}
                        onFetch={async () => {
                            return {
                                content: [],
                                total: 0
                            }
                        }}
                        toolbar={(table, tableId) => {
                            return <DataTableToolbar table={table} tableId={tableId}>
                                <ToolbarLeft>

                                </ToolbarLeft>
                            </DataTableToolbar>
                        }}
                    />
                </div>
            </div>
        </>
    )
}