import type { OrganizationSecret } from "@/api/secret/secret-model"
import { Badge } from "@/components/ui/badge"
import { CopyButton } from "@/components/ui/button/copy-button"
import { cn } from "@/lib/utils"
import type { ColumnDef } from "@tanstack/react-table"
import dayjs from "dayjs"


export const secretColumn: ColumnDef<OrganizationSecret>[] = [
    // {
    //     id: "id",
    //     header: "ID",
    //     cell: ({ row }) => {
    //         return <div>{row.original.id}</div>
    //     }
    // },
    {
        id: "secretKey",
        header: "Secret Key",
        cell: ({ row }) => {
            return (
                <div className="flex items-center gap-2">
                    {row.original.secretKey}
                    <CopyButton text={row.original.secretKey} size="xs" />
                </div>
            )
        }
    },
    {
        id: "status",
        header: "Status",
        cell: ({ row }) => {
            const status = row.original.statuses
            return <div>
                <Badge variant="outline" className="gap-1.5 text-zinc-600">
                    <span
                        className={cn("size-1.5 rounded-full ", status == 1 ? "bg-green-500" : "bg-red-500")}
                        aria-hidden="true"
                    ></span>
                    {status === 1 ? "Active" : "Inactive"}
                </Badge></div>
        }
    },
    {
        id: "createTime",
        header: "Created Time",
        cell: ({ row }) => {
            return <div>{dayjs(Number(row.original.createTime)).format("YYYY-MM-DD HH:mm:ss")}</div>
        }
    }
]