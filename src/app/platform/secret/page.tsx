import { DataTable } from "@/components/table/base-table";
import CommonButton from "@/components/ui/button/new-button";
import { PlusIcon } from "lucide-react";
import { secretColumn } from "./secret-column";
import { secretApi } from "@/api/secret/secret-api";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { organizationState } from "@/state/user-state";
import { DataTableToolbar, ToolbarLeft } from "@/components/table/toolbar/data-table-toolbar";
import FloatingMultiActionBar from "@/components/toolbar/floating-multi-action-bar";
import { refreshTableAtom } from "@/state/table-state";
import { pushModal } from "@/components/modals";
import { toast } from "sonner";

export default function SecretPage() {
    const organization = useAtomValue(organizationState)
    const setRefreshTable = useSetAtom(refreshTableAtom)

    const handleCreateSuccess = () => {
        // Refresh table data
        setRefreshTable(prev => prev + 1)
    }
    return (
        <>
            <div className="mx-auto bg-white rounded-lg shadow-borders-base h-full shadow-sm border border-zinc-200 py-2">
                <div className="flex flex-col gap-2 h-full">
                    <header className="px-4 py-2 space-y-1 flex flex-row items-center justify-between">
                        {/* left */}
                        <div>
                            <h1 className="text-[18px]">Secret</h1>
                            <h2 className="text-[13px] text-zinc-500">
                                <span className="text-zinc-500 text-[13px]">
                                    Manage API keys, access tokens and security credentials
                                </span>
                            </h2>
                        </div>
                        {/* right */}
                        <div className="pr-4">
                            <CommonButton onClick={() => pushModal("CreateSecretModal", {
                                isOpen: true,
                                onClose: () => { },
                                onSuccess: handleCreateSuccess
                            })}>
                                <PlusIcon className="w-4 h-4" />
                                New Secret
                            </CommonButton>
                        </div>
                    </header>
                    {/* <Separator /> */}
                    <DataTable
                        columns={secretColumn}
                        isNeedSelect={true}
                        onFetch={async (params) => secretApi.pageSecret(organization?.organizationId, params)}
                        dependencies={[organization]}
                        toolbar={(table, tableId) => {
                            return <DataTableToolbar table={table} tableId={tableId}>
                                <ToolbarLeft>

                                </ToolbarLeft>
                                {
                                    table.getFilteredSelectedRowModel().rows.length > 0 && (
                                        <FloatingMultiActionBar
                                            selectedCount={table.getFilteredSelectedRowModel().rows.length}
                                            selectedItems={table.getFilteredSelectedRowModel().rows.map((row: any) => row.original)}
                                            actions={[{
                                                id: "delete",
                                                label: "Delete",
                                                shortcut: "D",
                                                onClick: (selectedItems) => {
                                                    const itemsToDelete = [...selectedItems];
                                                    pushModal("DoubleCheckModal", {
                                                        title: "Delete Secret",
                                                        description: "Are you sure you want to delete these secrets?",
                                                        onConfirm: async () => {

                                                            toast.promise(async () => {
                                                                await secretApi.deleteSecret(organization?.organizationId!!, itemsToDelete.map((item: any) => item.id))
                                                                setRefreshTable(prev => prev + 1)
                                                                table.resetRowSelection()
                                                            }, {
                                                                loading: "Deleting...",
                                                                success: "Secrets deleted successfully",
                                                                error: "Failed to delete secrets"
                                                            })
                                                        }
                                                    })
                                                }
                                            }]
                                            }
                                        />
                                    )
                                }
                            </DataTableToolbar>
                        }}
                    />
                </div>
            </div>
        </>
    )
}