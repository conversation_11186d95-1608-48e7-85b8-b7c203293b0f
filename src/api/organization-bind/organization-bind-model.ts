import { apiClient } from "@/lib/apiClient"
import type { OrganizationBind } from "./organization-bind-api"



export const OrganizationBindApi = {

    persistOrganizationBind: async (organizationId: string, permission: number, userEmail: string) => {
        return apiClient.post({
            url: "/private/organization/team/persistenceOrganizationTeam",
            params: {
                organizationId,
                permission,
                userEmail
            }
        })
    },

    getOrganizationBind: async (organizationId: string): Promise<OrganizationBind[]> => {
        return apiClient.get({
            url: "/private/organization/team/selectOrganizationTeamByOrganizationId",
            params: {
                organizationId,
            }
        })
    },

    updateOrganizationBind: async (organizationId: string, userEmail: string, permission: number) => {
        return apiClient.put({
            url: "/private/organization/team/updateOrganizationTeamByOrganizationId",
            params: {
                organizationId,
                userEmail,
                permission
            }
        })
    },
    deleteOrganizationBind: async (organizationId: string, userEmail: string) => {
        return apiClient.delete({
            url: "/private/organization/team/deleteOrganizationTeamByOrganizationId",
            params: {
                organizationId,
                userEmail
            }
        })
    }
}