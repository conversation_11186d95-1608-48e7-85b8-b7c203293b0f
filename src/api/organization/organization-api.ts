import { apiClient } from "@/lib/apiClient";
import type { Organization } from "./organization-model";



export const baseOrganizationUrl = "/private/organization/info";



export const organizationApi = {
    getOrganization: async () => {
        return await apiClient.get<Organization[]>({
            url: `${baseOrganizationUrl}/selectOrganizationInfoAll`,
        });
    },

    createOrganization: async (organizationName: string) => {
        return await apiClient.post<Organization>({
            url: `${baseOrganizationUrl}/persistenceOrganizationInfo`,
            params: {
                organizationName,
            },
        });
    },

    updateOrganizationInfo: async (organizationId: string, organizationName: string) => {
        return await apiClient.put<Organization>({
            url: `${baseOrganizationUrl}/updateOrganizationInfo`,
            params: {
                organizationId,
                organizationName,
            },
        });
    },

    currentOrganization: async () => {
        return await apiClient.get<Organization | undefined>({
            url: `${baseOrganizationUrl}/current/organization`,
        });
    },


    changeOrganization: async (organizationId: string) => {
        return await apiClient.post<Organization>({
            url: `${baseOrganizationUrl}/current/organization/change`,
            params: {
                organizationId,
            },
        });
    },
}
