export interface ModelPriceInfoResponse {
    modelName: string;
    manufacturerName: string;
    vendorNames: string[];
    modelType: number; // 0: LLM, 1: Search
    prices: {
        textPrompt: string;
        textCachePrompt: string;
        textCompletion: string;
        audioPrompt: string;
        audioCachePrompt: string;
        audioCompletion: string;
        reasoningCompletion: string;
        imagePrompt: string;
        imageCachePrompt: string;
        imageCompletion: string;
        textCachePromptWrite5m: string;
        textCachePromptWrite1h: string;
        searchCall: string;
        searchTool: string;
    };
}
