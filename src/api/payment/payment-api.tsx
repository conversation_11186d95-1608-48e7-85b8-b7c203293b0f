import { apiClient } from "@/lib/apiClient"









export const PaymentApi = {
    getCheckoutSession: async (organizationId: string) => {
        apiClient.get<{
            redirectUrl: string;
        }>({
            url: "/private/stripe/payment/getCheckoutSession",
            params: {
                organizationId
            }
        });
    },

    createSetupIntentSuccess: async (organizationId: string, sessionId: string) => {
        apiClient.post({
            url: "/private/stripe/payment/createSetupIntentSuccess",
            params: {
                organizationId,
                sessionId
            }
        });
    },

    payment: async (organizationId: string, amount: number, cardId: string) => {
        apiClient.post({
            url: "/private/stripe/payment/payment",
            params: {
                amount,
                cardId,
                organizationId
            }
        });
    },

    // 检查可退款金额
    checkRefund: async (organizationId: string) => {
        return await apiClient.get<{
            refundable: string;
        }>({
            url: "/private/stripe/payment/checkRefund",
            params: {
                organizationId
            }
        });
    },

    // 申请退款
    refundCharge: async (organizationId: string, amountUsdLong: number) => {
        return await apiClient.post<{
            requestedUsd: string;
            refundedUsd: string;
            unrefundedUsd: string;
        }>({
            url: "/private/stripe/payment/refund",
            params: {
                organizationId,
                amountUsdLong
            }
        });
    }
}