import type { FetchPara<PERSON>, FetchResponse } from "@/components/table/types";
import type { OrganizationSecret as OrganizationSecretResponse } from "./secret-model";
import { apiClient } from "@/lib/apiClient";

export const secretApi = {

    pageSecret: async (organizationId: string | undefined, params: FetchParams) => {
        if (!organizationId) {
            return {
                content: [],
                total: 0
            }
        }
        const response = await apiClient.get<FetchResponse<OrganizationSecretResponse>>({
            url: "/private/organizationSecret/pageOrganizationSecretByOrganizationId",
            params: {
                organizationId,
                ...params.searchParams,
                pageSize: params.pagination.pageSize,
                pageNumber: params.pagination.pageIndex,
            }
        });
        return response;
    },

    createSecret: async (organizationId: string): Promise<OrganizationSecretResponse> => {
        return await apiClient.post<OrganizationSecretResponse>({
            url: "/private/organizationSecret/persistenceOrganizationSecretByOrganizationId",
            params: {
                organizationId
            }
        });
    },

    deleteSecret: async (organizationId: string, organizationSecretIds: string[]) => {
        await apiClient.delete<OrganizationSecretResponse>({
            url: "/private/organizationSecret/deleteOrganizationSecretByOrganizationIdAndOrganizationSecretId",
            data: {
                organizationId,
                organizationSecretIds
            }
        });
    }

}