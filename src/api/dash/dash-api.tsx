import { apiClient } from "@/lib/apiClient"
import type { OrganizationDailyBillResponse } from "./dash-model";



export const dashStatisticApi = {

    getDashStatistic: async (params: {
        selectedYear: number;
        selectedMonth: number;
        organizationId: string;
    }) => {
        return await apiClient.get<Record<string, OrganizationDailyBillResponse[]>>({
            url: '/private/organizationDailyBill/charts',
            params
        })
    }
}