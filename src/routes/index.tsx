import { createBrowserRouter, Navigate } from "react-router-dom"
import MainLayout from "@/components/layout/main-layout"
import HomePage from "@/app/platform/home/<USER>"
import NotFoundPage from "@/app/404/not-found-page"
import AuthCallbackPage from "@/app/auth/callback/page"
import SecretPage from "@/app/platform/secret/page"
import WalletPage from "@/app/platform/wallet/page"
import TeamPage from "@/app/platform/team/page"
import SystemModelPage from "@/app/platform/system-model/page"
import PaymentSuccessPage from "@/app/pay/success/page"
import PaymentCancelPage from "@/app/pay/cancel/page"

export const router = createBrowserRouter([
    {
        path: "/",
        errorElement: <NotFoundPage />,
        children: [
            {
                index: true,
                element: <Navigate to="/dashboard/home" replace />,
            },
            // {
            //     path: "login",
            //     element: <Login />,
            // },
            {
                path: "auth/callback",
                element: <AuthCallbackPage />,
            },
            {
                path: "pay/success",
                element: <PaymentSuccessPage />,
            },
            {
                path: "pay/cancel",
                element: <PaymentCancelPage />,
            },
            {
                path: "dashboard",
                element: <MainLayout />,
                children: [
                    {
                        path: "home",
                        element: <HomePage />,
                    },
                    {
                        path: "team",
                        element: <TeamPage />,
                    },
                    {
                        path: "wallet",
                        element: <WalletPage />,
                    },
                    {
                        path: "secret",
                        element: <SecretPage />,
                    },
                    {
                        path: "system-model",
                        element: <SystemModelPage />,
                    },
                ],
            }
        ],
    },
    {
        path: "*",
        element: <NotFoundPage />,
    },
]) 